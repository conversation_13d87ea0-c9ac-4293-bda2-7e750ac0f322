// .side-nav .side-nav-item:hover > .collapse > ul > li:before{
//     content:'' !important;
//     position:absolute !important;
//     bottom:0 !important;
//     width:100% !important;
//     height:20px !important;
// }
// .add-visible{
//     overflow-y: visible !important;
// }

.bg-vente-quick {
  background: #9E2A2B !important;
}

.vente-link {
  margin: 0px 24px 0 18px;
}

::ng-deep body[data-leftbar-compact-mode="condensed"] .vente-link {
  margin: 0 !important;
}

ul.side-nav-second-level {
  position: relative;
}
ul.side-nav-third-level {
  position: relative;
}

ul.side-nav-second-level::before {
  content: "";
  width: 2px;
  position: absolute;
  top: 0;
  display: block;
  background: #335C67;
  height: 100%;
  left: 26px;
  z-index: 1;
}

ul.side-nav-third-level::before {
  content: "";
  width: 1px;
  position: absolute;
  top: 0;
  display: block;
  background: #335C67;
  height: 100%;
  left: 38px;
  z-index: 1;
}

ul.side-nav-third-level::after {
  content: "";
  width: 12px;
  position: absolute;
  top: 0;
  display: block;
  background: #335C67;
  height: 1px;
  left: 27px;
  z-index: 1;
}

.side-nav-third-level li a span::before {
  content: "";
  width: 9px;
  height: 1px;
  position: absolute;
  top: 50%;
  display: block;
  background: #335C67;
  left: 39px;
  z-index: 1;
}

// disable color when the menu is closed
::ng-deep
  body[data-leftbar-compact-mode="condensed"]
  .side-nav-item:has(.collapse.show) {
  background: transparent;
}

// add color to opned menu
// .side-nav-item:has(.collapse.show) {
//   background-color: #fff;
// }

// .side-nav-third-level:has(.menuitem-active) {
//   background-color: #fff;
// }
