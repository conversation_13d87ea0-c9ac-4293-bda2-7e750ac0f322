import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';

// import { Moment } from 'moment';
import { Depot } from 'src/app/references-app/references-produits/models/produit/stock/depot.model';
import { Fournisseur } from '../../../tiers/fournisseur/fournisseur.model';



export class DemandeAvoirCriteria { 
    dateDebut?: any;
    dateFin?: any;
    depot?: Depot;
    estCloture?: boolean;
    fournisseurID?: number;
    numero?: number;
    statut?: Statut;
    fournisseur?:Fournisseur
}
