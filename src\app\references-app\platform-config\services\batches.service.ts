import { Injectable } from '@angular/core';
import { BatchAdmin } from '../../referential/models/admin/batch-admin.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BatchesService {

    constructor(private httpClient: HttpClient) { }

    listAvailableBatches(): Observable<BatchAdmin[]> {
        return this.httpClient.get<BatchAdmin[]>( `${env.base_url}/api/batch/list`);

    }

    executeBatch(data) {
        return this.httpClient.post<any>(`${env.base_url}/api/batch/execute`, { codeBatch: data.codeBatch })
    }


}
