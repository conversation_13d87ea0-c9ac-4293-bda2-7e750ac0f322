import { Component, OnInit } from '@angular/core';
import { WinClientProvinces } from '../../Services/winclient.provinces';
import { Province } from '../../models/province';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { filterBy } from '@progress/kendo-data-query';

@Component({
  selector: 'app-province',
  templateUrl: './province.component.html',
  styleUrls: ['./province.component.scss']
})
export class ProvinceComponent implements OnInit {
  navigation: Pagination = {
    pageSize: 25,
    skip: 0,
  };
  provinces: Province[] = [];
  filtredProvinces: Province[] = [];
  constructor(private winClientProvinces: WinClientProvinces) { }

 
  ngOnInit() {
    this.getProvinces();
  }

  getProvinces() {
    this.winClientProvinces.getProvinces().subscribe(
      (data) => {
        this.provinces = data;
        this.filtredProvinces =data;
      }
    );
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    const page = Math.ceil(skip / this.navigation.pageSize);
  }

  filterProvinces(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.navigation.skip = 0; 
    const result = filterBy(this.filtredProvinces, {
      logic: 'or',
      filters: [
        { field: 'libProvince', operator: 'contains', value: searchTerm },
      ]
    });
    this.provinces = result;
  }

}
