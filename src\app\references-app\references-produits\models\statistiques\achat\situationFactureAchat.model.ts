import { TypeStatutReglement } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeStatutReglement.enum';
import { ModePaiement } from 'src/app/references-app/references-produits/enums/common/ModePaiement.enum';
import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';

// import { Moment } from 'moment';

import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { SyntheseFactureAchat } from './syntheseFactureAchat.model';
import { ModePaiementFacture } from 'src/app/references-app/references-produits/enums/common/ModePaiementFacture.enum';


export class SituationFactureAchat {
    dateFacture?: any;
    etatReglementFacture?: TypeStatutReglement;
    fournisseur?: Fournisseur;
    idFacture?: number;
    indicateurs?: SyntheseFactureAchat;
    modePaiementFacture?: ModePaiementFacture;
    numeroFacture?: number;
    statutFacture?: Statut;
}

