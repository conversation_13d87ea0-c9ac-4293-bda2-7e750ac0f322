import { Statut } from "src/app/references-app/references-produits/enums/common/Statut.enum";
import { TypeAxeEnum } from "src/app/references-app/references-produits/enums/Compta/TypeAxe.enum";
import { TypeComptaEnum } from "src/app/references-app/references-produits/enums/Compta/TypeCompta.enum";

 
  export enum CategoryType {
    MEDICAMENT = "Médicament",
    AUTRES = "Autres",
    DIETETIQUE = "Diétetique",
    PARAPHARMACIE = "Parapharmacie",
  }
  

  export interface ComptaAnalytiqueDto {
    annee?: number;
    categorie?: string; 
    codeFt?: string; 
    dateVente?: string; 
    ftMarge?: number; 
    libFt?: string; 
    mapMontantTtcByCategorie?: { [category: string]: number }; 
    mntBrut?: number;
    mntHt?: number; 
    mntPph?: number;
    mntRemise?: number; 
    mntTtc?: number; 
    mntTva?: number; 
    mois?: number; 
    statut?: Statut; 
    tauxTva?: number; 
    typeAxe?: TypeAxeEnum; 
    typeCompta?: TypeComptaEnum; 
  }

