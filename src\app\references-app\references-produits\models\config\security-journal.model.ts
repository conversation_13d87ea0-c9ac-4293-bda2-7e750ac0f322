import { TenantPrincipal } from "src/app/shared/models/tenantPrincipal.model";
import { Operateur } from "../common/operateur.model";

export type SecurityJournalFilterCreateria ={
    dateDebut? : any;
    dateFin? : any;
    operateur? : Operateur;
    tenantId? :number;
    nomPrenomOperateur? :string
    adresseIp? :string
  }



  export class SecurityJournalModel {
        id: number;
        logDate: string;
        srcIp: string;
        type: string;
        user: Operateur;
        tenantPrincipal:TenantPrincipal;
  }