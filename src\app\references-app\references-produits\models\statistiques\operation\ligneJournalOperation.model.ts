import { TypeDocument } from 'src/app/references-app/references-produits/enums/common/TypeDocument.enum';

// import { Moment } from 'moment';

import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { SyntheseOperation } from './syntheseOperation.model';
import { Tiers } from 'src/app/references-app/references-produits/models/tiers/tiers.model';


export class LigneJournalOperation { 
    dateHeureOperation?: any;
    natureOperation?: string;
    numOperation?: number;
    operateur?: Operateur;
    prixUnit?: number;
    syntheseOperation?: SyntheseOperation;
    tiers?: Tiers;
    typeOperation?: TypeDocument;
}

