import { Component, ElementRef, Input, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';

@Component({
  selector: 'app-copy-cell',
  template: `
    <div *ngIf="value && value?.trim() != ''" class="d-flex align-items-center justify-content-between position-relative" [ngClass]="{'copy-flash': isCopied}">
      <span>{{value}}</span>
      <div [appCopyToClipboard]="value" (copied)="onCopied()">
        <i  
           [ngClass]="isCopied ? 'mdi mdi-check text-success' : 'mdi mdi-content-copy text-black'"
           class="ms-1 copy-icon" 
           style="cursor: pointer; font-size: 18px;line-height: 0;" 
           title="{{isCopied ? 'Copied!' : 'Copy'}}"></i>
      </div>
    </div>
  `,
   styles: [`
    .copy-flash {
      animation: flash-background .4s  ease 2;
    }
    
    @keyframes flash-background {
      0% { background-color: transparent; }
      30% { background-color: rgba(10, 70, 161, 0.4); }
      100% { background-color: transparent; }
    }
  `]
})
export class CopyCellComponent implements OnDestroy {
  @Input() value: string = '';

  isCopied: boolean = false;

  private timeoutId: number | null = null;
  
  onCopied(): void {
    this.isCopied = true;
    
    // Clear any existing timeout
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
    }
    
    // Set new timeout
    this.timeoutId = window.setTimeout(() => {
      this.isCopied = false;
      this.timeoutId = null;
    }, 1200); // Extended to match animation duration (1.2s × 3 iterations)
  }
  
  ngOnDestroy(): void {
    // Clean up timeout when component is destroyed
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
    }
  }
}