import { Component } from "@angular/core";
import { ClientStats } from "../references-app/references-clients/models/winclient.stats";
import { WinClientSite } from "../references-app/references-clients/models/sites";
import { WinclientStatsService } from "../references-app/references-clients/Services/winclient.stats";
import { WinclientSitesService } from "../references-app/references-clients/Services/winclient.sites.service";
import { WinclientBatchesService } from "../references-app/references-clients/Services/winclient.batches";
import { AlertService } from "../shared/services/alert.service";

import moment from 'moment';
import { WinclientAlertsService } from "../references-app/references-clients/Services/winclient.alerts";
import { AlertClient } from "../references-app/references-clients/models/alertClient";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { AuthService } from "../shared/services/auth.service";
import { UserInputService } from "../shared/services/user-input.service";
import { Pagination } from "../references-app/referential/models/pagination.interface";
import { Router } from "@angular/router";
import { ProduitService } from "../references-app/references-produits/Services/produit/produit.service";
import { ProductStats, StatsCriteria } from "../references-app/references-produits/models/produit/stats/dashStats.model";
import { setFilter } from "@progress/kendo-angular-grid/dist/es2015/filtering/base-filter-cell.component";
import { filterBy } from "@progress/kendo-data-query";
import { EventService } from 'src/app/core/service/event.service';
import {SIDEBAR_WIDTH_CONDENSED, SIDEBAR_WIDTH_FIXED } from "../layouts/partage/models/layout.model";


type SiteStats = WinClientSite & { derniereMaj: string };

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class dashboardComponent  {

  winclientAlert :GridDataResult ={
    data: [] as AlertClient[],
    total: 0
  };
  winclientAlertFilter: AlertClient[] = [];

  navigation : Pagination = {
    pageSize :21,
    skip:0,
  };
  winclientStats:ClientStats = null;
  produitStats: ProductStats = null;
  isLoading:boolean = true;
  sitesMajStats : SiteStats[] = [];
  sites : WinClientSite[] = [];
  alertFilter: 'tous' | 'ouverte' | 'fermer' = 'tous';
  nouveauClientsInterval:{dateDebut?:string,dateFin?:string,label:string} | null = {
    dateDebut: moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'),
    dateFin: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '1m'
  };

  nouveauProductsWinplusInterval:{dateDebut?:string,dateFin?:string,label:string} | null = {
    dateDebut: moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'),
    dateFin: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '1m'
  };

   nouveauProductsGroupeInterval:{dateDebut?:string,dateFin?:string,label:string} | null = {
    dateDebut: moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'),
    dateFin: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '1m'
  };

  isNouveauClientsLoading: boolean =false;
  isNouveauProduitLoadingLabel: string ='';

  
 

  constructor(
    private statsService: WinclientStatsService,
    private winclientSiteService: WinclientSitesService,
    private winclientBatchService: WinclientBatchesService,
    private winclientAlertService: WinclientAlertsService,
    private produitService :ProduitService,
    private alertService: AlertService,
    private authService : AuthService,
    private inputService : UserInputService,
    private router : Router,
    private eventService : EventService
  ) { }

  ngOnInit() {
    this.getStats();
    this.getAlerts();
    this.getProduitTranscoStats();
  }

  ngAfterViewInit() {
     setTimeout(() => {
      this.closeSidebarOn();
     }, 200);
  }

  closeSidebarOn() {
        if (document.body.hasAttribute('data-leftbar-compact-mode') && document.body.getAttribute('data-leftbar-compact-mode') === 'condensed') {
      this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_CONDENSED);
    } else {
      // document.body.setAttribute('data-leftbar-compact-mode', 'condensed');
      this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_CONDENSED);
    }
    
  }

   storeMenuState(value) {
    localStorage.setItem('menu_webfix', value);
  }


  getProduitTranscoStats() {
    // const statsCriteria = new StatsCriteria({ dateAu: '2023-01-01', dateDu: '2023-12-31' });
    this.produitService.getProduitTranscoStats().subscribe(
      (data) => {
        this.produitStats = data;
        this.produitStats.derniereMajParSite = this.produitStats.derniereMajParSite.map((site) => {
          return {
            ...site,
            libelleSource: this.sites.find((s) => s.id === site.codeSite)?.libelleLong,
          };
        });
        console.log('Product Stats:', data);
      }
    );
  }

  isDateToday(date: string): boolean {
    if (!date) return false;
    
    const today = moment().startOf('day');
    const inputDate = moment(date, ['DD-MM-YYYY', 'YYYY-MM-DD', 'MM-DD-YYYY']);
    
    if (!inputDate.isValid()) {
      return false;
    }
    
    return today.isSame(inputDate, 'day');
  }


  executeBatchImportClientSite() {
    this.winclientBatchService.executeBatchImportClientSite().subscribe(
      {
        next: (data) => {
          if(data && data.status === "SUCCESS"){
            this.alertService.success("Importation des sites clients effectuée avec succès");
            this.getStats();
          }
        }
      }
    );
  }

  
  executeBatchMajProduit() {
    this.produitService.triggerMajProduit().subscribe(
      {
        next: () => {
            this.alertService.success("Mise à jour des produits site effectuée avec succès");
            this.getProduitTranscoStats();
        }
      }
    );
  }

  getNouveauClientsIntervalReadableLabel() {
    if (!this.nouveauClientsInterval) return '';
    // here i need to return french label for the interval
    switch (this.nouveauClientsInterval.label) {
      case 'today': return 'Aujourd\'hui';
      case '7d': return '7 derniers jours';
      case '15d': return '15 derniers jours';
      case '1m': return '1 mois';
      default: return '';
    }
  }

  getStats(){
    this.statsService.getStats().subscribe(
      {
         next: (data) => {
           this.winclientStats = data;
           this.getsites();
         },
         complete: () => {
           this.isLoading = false;
         }
 
      }
     )
  }

  getsites(){
    this.winclientSiteService.getAllSites().subscribe(
      {
        next: (data) => {
          this.sites = data;
           this.sitesMajStats = this.mergeSiteStats(this.winclientStats, data);
           this.mergeSiteAlerts();
        } ,
        complete: () => {
          this.isLoading = false;
        }
      }
    );

  }



  IntervalToDate(interval: 'today' | '7d' | '15d' | '1m') {
    const now = moment();
    let dateDebut: moment.Moment;
    const dateFin = now.clone().endOf('day');

    dateDebut = now.clone().subtract(
      interval === 'today' ? 0 :
      interval === '7d' ? 7 :
      interval === '15d' ? 15 :
      interval === '1m' ? 1 : 0,
      interval === '1m' ? 'month' : 'days'
    ).startOf('day');

    return { dateDebut: dateDebut.format('YYYY-MM-DD HH:mm:ss'), dateFin: dateFin.format('YYYY-MM-DD HH:mm:ss') ,label:interval};
  }

   refreshTranscpdeCount(interval: 'today' | '7d' | '15d' | '1m', type: 'winplus' | 'groupe') {
    const { dateDebut, dateFin ,label } =  this.IntervalToDate(interval);
     type === "groupe" && (this.nouveauProductsGroupeInterval = { dateDebut, dateFin, label })
      type === "winplus" && (this.nouveauProductsWinplusInterval = { dateDebut, dateFin, label });
    this.isNouveauProduitLoadingLabel = type;
    this.produitService.getProduitTranscoStats({
      dateDu: dateDebut,
      dateAu: dateFin,
    }).subscribe({
      next: (value) => {
        this.isNouveauProduitLoadingLabel = type;
        if(type === "groupe"){
          this.produitStats.totalProdSiteSansCodeGroupe = value.totalProdSiteSansCodeGroupe;
        }else{
          this.produitStats.totalProdBaseGroupeSansCodeWinplus = value.totalProdBaseGroupeSansCodeWinplus;
        }
      },
      complete: () => {
        this.isNouveauProduitLoadingLabel = '';
      }
    })
  }

  refreshNewClientCount(interval: 'today' | '7d' | '15d' | '1m') {
    const { dateDebut, dateFin ,label } =  this.IntervalToDate(interval);
    this.nouveauClientsInterval = { dateDebut, dateFin, label };
    this.isNouveauClientsLoading = true;
    this.statsService.getStats({
      dateCreationDebut: dateDebut,
      dateCreationFin: dateFin
    }).subscribe({
      next: (value) => {
        this.isNouveauClientsLoading = true;
        this.winclientStats.nouveauxClients = value.nouveauxClients;
      },
      complete: () => {
        this.isNouveauClientsLoading = false;
      }
    })
  }

  getAlerts(page =0){
    this.winclientAlertService.getAlerts({page,size:this.navigation.pageSize}).subscribe({
      next:(value) => {
        this.winclientAlert.data = value.content;
        this.winclientAlertFilter = value.content;
        this.winclientAlert.total = value.totalElements;
        setTimeout(() => {
                  this.setAlertFilter("ouverte")
        }, 200);
      },
    })
  }

  changeAlertStatus(dataItem: AlertClient) {
    const userId = this.authService.getPrincipal()?.idhash;
    if(!userId) {
      this.alertService.error("Utilisateur non trouvé pour changer le statut de l'alerte");
      return;
    }
    const { id  } = dataItem;
    const etat = dataItem.etat === 0 ? 1 : 0;

    console.log("Payload to send",{userId,etat,id})
    this.inputService.confirm(
      "Confirmation de changement de statu",
      `Voulez-vous vraiment ${etat === 1 ? 'férmée' : 'réouvrir'} cette alerte ?`,
      "Oui",
      "Non"
    ).then(
      (result) => {
        if (result) {
          this.winclientAlertService.changeAlertStatus({userId,etat,id}).subscribe({
            next: (data) => {
              this.alertService.success(`Alerte ${etat === 1 ? 'fermée' : 'réouverte'} avec succès`);
              this.getAlerts();
            }
          });
        }
      }
    )
    // this.winclientAlertService.changeAlertStatus({userId,etat: 0,id}).subscribe();
  }


  
  pageChange(skip:number) {
    this.navigation.skip =skip;
    const page = Math.ceil(skip / this.navigation.pageSize);
    this.getAlerts(page);
  }

  exploreClientSiteWithIntervale(){
    if(!this.nouveauClientsInterval) return;
    const { dateDebut, dateFin } = this.nouveauClientsInterval;
    this.router.navigate(['/references/ref-clients/client-sites'], {
      queryParams: {
        filterNouveauClient: true,
        interval: this.nouveauClientsInterval.label,
      }
    });
  }

  exploreProductFournisseurWithIntervale(target: 'winplus' | 'groupe' = 'winplus'){
    if(!this.nouveauClientsInterval) return;
    const { dateDebut, dateFin } = this.nouveauClientsInterval;
    this.router.navigate(['/references/ref-produits/produits/produits-sites'], {
      queryParams: {
        nouveaux: true,
        interval: this.nouveauClientsInterval.label,
        target
      }
    });
  }
  setAlertFilter(filter: 'tous' | 'ouverte' | 'fermer'): void {
    this.alertFilter = filter;
  const filtredAlerts = filterBy(this.winclientAlertFilter,{
    logic: 'or',
    filters: [
      { field: 'etat', operator: 'eq', value: filter === 'ouverte' ? 0 : 1 },
      { field: 'etat', operator: 'eq', value: filter === 'fermer' ? 1 : 0 }
    ]
  });
  this.winclientAlert.data =  filtredAlerts;
  this.winclientAlert.total = filtredAlerts.length;
   this.mergeSiteAlerts();
}

  mergeSiteStats(stats: ClientStats, sites: WinClientSite[]) :SiteStats[] {
    const mergedSites = sites.map(site => {
      // Skip site with id 6 and above 14 and 0
      if (site.id === 6 || site.id > 14 || site.id === 0) {
        return null;
      }
      
      return {
        ...site,
        derniereMaj: stats.derniereMajParSite[site.id.toString()] === "non disponible" ? null :  stats.derniereMajParSite[site.id.toString()]
      };
    }).filter(site => site !== null);

    mergedSites.sort((a, b) => a.id - b.id);

    return mergedSites;
  }

  mergeSiteAlerts() {
  this.winclientAlert.data = this.winclientAlert.data.map(alert => {
    const site = this.sites.find(site => site.id === alert.codeSite);
    return {...alert, siteLabel: site?.libelleCourt};
  });
  }

  gotoAnomalies(){
    this.router.navigate(['/references/ref-clients/anomalies-client-groupe']);
  }
    

}
