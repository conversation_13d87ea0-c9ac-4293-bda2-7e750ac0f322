export enum LogCalculRemiseVenteEnum   {
  MANUEL = "M",         // attribué mannuellement
  PRIX_UNIT = "P",      // prix unit ttc a changé
  TOTAL_REMISE = "T",   // total remise a changé
  AUTO_TYPE = "A",      // attribué automatiquement avec type remise
  AUTO_ZERO = "Z",      // attribué automatiquement 0
  NEGATIF = "N",        // forcé zero car négatif
  PLAFOND_USER = "U"    // forcé to plafond ou 100
}