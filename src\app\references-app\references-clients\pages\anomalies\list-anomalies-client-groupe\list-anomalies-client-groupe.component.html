<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Anomalies Client Groupe</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-dark" title="Filtrer">
            <i class="mdi mdi-filter"></i>
          </button> 
          <button type="button" [class]="'btn ' + (selectedStatut == 'TOUS' ? 'btn-dark' : 'btn-outline-dark')" (click)="changeStatutFilter('TOUS')">TOUS</button>
          <button type="button" [class]="'btn ' + (selectedStatut == 'OUVERT' ? 'btn-dark' : 'btn-outline-dark')" (click)="changeStatutFilter('OUVERT')">OUVERT</button>
          <button type="button" [class]="'btn ' + (selectedStatut == 'FERME' ? 'btn-dark' : 'btn-outline-dark')" (click)="changeStatutFilter('FERME')">FERME</button>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-grid [data]="anomalies"  
style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid ref-grid content-wrap"
[pageable]="true"
[pageSize]="navigation.pageSize" [skip]="navigation.skip"
[rowClass]="rowClass"
>
<kendo-grid-column field="codeClientGroupe" title="Code Client Groupe" [width]="100">
  <ng-template kendoGridCellTemplate let-dataItem>
     <span class="text-primary text-decoration-underline k-cursor-pointer" [routerLink]="['/references/ref-clients/client-groupe']" [queryParams]="{codeGroupe: dataItem.codeClientGroupe}" >
      <app-copy-cell [value]="dataItem.codeClientGroupe"></app-copy-cell>
     </span>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="clientGroupe.raisonSociale" title="Raison Sociale" [width]="200"></kendo-grid-column>
<kendo-grid-column field="clientGroupe.nomPharmacien" title="Nom Pharmacien" [width]="200"></kendo-grid-column>
<kendo-grid-column field="clientGroupe.ville.libelle" title="Ville" [width]="200"></kendo-grid-column>
<kendo-grid-column field="clientGroupe.classification" title="Classification" [width]="100"></kendo-grid-column>
<kendo-grid-column field="anomalie" title="Anomalie" [width]="200">
  <ng-template kendoGridCellTemplate let-dataItem>
    <span *ngIf="dataItem.anomalie.includes('Doublon probable avec')" class="text-primary text-decoration-underline k-cursor-pointer" 
      [routerLink]="['/references/ref-clients/client-groupe']" 
      [queryParams]="{codeGroupe: dataItem.anomalie.split(' ').pop()}" >
      {{dataItem.anomalie}}
    </span>
    <span *ngIf="!dataItem.anomalie.includes('Doublon probable avec')">
      {{dataItem.anomalie}}
    </span>
  </ng-template>
</kendo-grid-column>
  <kendo-grid-column field="dateCreationAnomalie" title="Date Anomalie" [width]="120">
    <ng-template kendoGridCellTemplate let-dataItem>
      {{dataItem.dateCreationAnomalie | date:'dd/MM/yyyy HH:mm:ss'}}
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="statut" title="Statut" [width]="80"></kendo-grid-column>
  <kendo-grid-column title="Actions" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="dataItem.statut === 'ouverte' ? 'check' : 'close'"
      [backgroundColor]="dataItem.statut === 'ouverte' ? 'success' : 'danger'"
      [extendClass]="'circle-lg'" (click)="changerStatutAnomalie(dataItem)">

      </app-action-icon>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
    let-total="total">
    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
      [navigation]="navigation" style="width: 100%;"
      (pageChange)="pageChange($event)"></wph-grid-custom-pager>
  </ng-template>
</kendo-grid>