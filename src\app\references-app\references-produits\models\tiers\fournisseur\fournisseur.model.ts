import { TypeTiers } from 'src/app/references-app/references-produits/enums/tiers/TypeTiers.enum';
import { TypeFournisseur } from 'src/app/references-app/references-produits/enums/tiers/TypeFournisseur.enum';

import { FournisseurParametre } from './fournisseurParametre.model';
import { Ville } from 'src/app/references-app/references-produits/models/common/ville.model';
import { IndicateurFournisseur } from './indicateur-fournisseur.model';
import { Tiers } from '../tiers.model';
import { GammeFournisseur } from './gammeFournisseur.model';


export class Fournisseur {
    adr1?: string;
    adr2?: string;
    audited?: boolean;
    codeFournisseur?: string;
    email?: string;
    indicateur?: IndicateurFournisseur;
    fournisseurParametre?: FournisseurParametre;
    id?: number;
    numTelephone?: string;
    raisonSociale?: string;
    typeFrn?: TypeFournisseur;
    typeTiers?: TypeTiers;
    userModifiable?: boolean;
    ville?: Ville;
    tiersId?: number;
    enableEnvoiHub?: boolean;
    noeudDestinataireId?: number;
    listGammes?: GammeFournisseur[];

    suggestion?: any
}

