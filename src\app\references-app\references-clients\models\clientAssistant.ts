// Define types for the match object
export interface MatchAssociation {
    code_client_groupe: string;
    raison_sociale: string;
    nom_pharmacien: string;
    adresse1: string;
    adresse2: string | null;
    ice: string;
    localite: string;
    nom_pharmacien_normalized?: string;
    raison_sociale_normalized?: string;
    patente: string;
    tag: string | null;
    telephone: string;
    ville: string;
    type: string | null;
}

// Define types for the client object
interface IClientAssociationCriteria {
    code_site?: number;
    code_client?: number;
    raison_sociale: string;
    nom_pharmacien: string;
    ville: string;
}

type AssociationType = "association_classique" | "association_floue";


// Define types for the association object
export interface ClientAssistantResponse {
    client: ClientAssistantCriteria;
    type: AssociationType;
    matches: MatchAssociation[];
}




export interface ClientAssociationsRepresentation {
    client: ClientAssistantCriteria;
    type: AssociationType | null;
    matches: MatchAssociation[];
    selectedAssociation?: Partial<MatchAssociation>;
}

export class ClientAssistantCriteria implements IClientAssociationCriteria {
    code_site?: number;
    code_client?: number;
    raison_sociale: string;
    nom_pharmacien: string;
    ville: string;

    constructor(clientAssociationCriteria: Partial<IClientAssociationCriteria>) {
        Object.assign(this, clientAssociationCriteria);
    }
}