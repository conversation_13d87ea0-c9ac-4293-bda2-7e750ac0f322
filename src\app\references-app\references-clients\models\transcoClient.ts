interface ITranscoClient {
  backupCodeClientGroupe: number;
  clientId: number;
  codeClientGroupe: number;
  codeClientSite: number;
  dateValidationForExport: string;
  id: number;
  isNewByVerification: boolean;
  codeSite: number;
  tag: string;
  validatedForExport: boolean;
}

interface ITranscoClientCriteria {
  backupCodeClientGroupe?: string;
  codeClientSite: number;
  tag : string;
  validatedForExport: boolean;
  codeSite:number;
}

export class TranscoClientCriteria implements ITranscoClientCriteria{
    backupCodeClientGroupe?: string;
    codeClientSite: number;
    tag : string;
    validatedForExport: boolean;
    codeSite:number;
    constructor(transcoClientCriteria:Partial<ITranscoClientCriteria>){
        Object.assign(this,transcoClientCriteria);
    }
}


export class TranscoClient implements ITranscoClient{
    backupCodeClientGroupe: number;
    clientId: number;
    codeClientGroupe: number;
    codeClientSite: number;
    dateValidationForExport: string;
    id: number;
    isNewByVerification: boolean;
    codeSite: number;
    tag: string;
    validatedForExport: boolean;

    constructor(transcoClient:Partial<ITranscoClient>){
        Object.assign(this,transcoClient)
    }

}



