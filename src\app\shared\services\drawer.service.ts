import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface DrawerConfig {
  id: string;
  isOpen: boolean;
  title?: string;
  position?: 'left' | 'right' | 'top' | 'bottom';
  size?: 'small' | 'medium' | 'large' | 'full';
  width?: string;
  height?: string;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class DrawerService {
  private drawers = new Map<string, BehaviorSubject<DrawerConfig>>();

  constructor() { }

  /**
   * Register a new drawer
   */
  register(config: DrawerConfig): Observable<DrawerConfig> {
    if (!this.drawers.has(config.id)) {
      this.drawers.set(config.id, new BehaviorSubject<DrawerConfig>(config));
    }
    return this.drawers.get(config.id)!.asObservable();
  }

  /**
   * Open a drawer by ID
   */
  open(id: string, data?: any): void {
    const drawer = this.drawers.get(id);
    if (drawer) {
      const currentConfig = drawer.value;
      drawer.next({
        ...currentConfig,
        isOpen: true,
        data: data || currentConfig.data
      });
    }
  }

  /**
   * Close a drawer by ID
   */
  close(id: string): void {
    const drawer = this.drawers.get(id);
    if (drawer) {
      const currentConfig = drawer.value;
      drawer.next({
        ...currentConfig,
        isOpen: false
      });
    }
  }

  /**
   * Toggle a drawer by ID
   */
  toggle(id: string, data?: any): void {
    const drawer = this.drawers.get(id);
    if (drawer) {
      const currentConfig = drawer.value;
      if (currentConfig.isOpen) {
        this.close(id);
      } else {
        this.open(id, data);
      }
    }
  }

  /**
   * Update drawer configuration
   */
  updateConfig(id: string, config: Partial<DrawerConfig>): void {
    const drawer = this.drawers.get(id);
    if (drawer) {
      const currentConfig = drawer.value;
      drawer.next({
        ...currentConfig,
        ...config
      });
    }
  }

  /**
   * Get drawer state
   */
  getDrawer(id: string): Observable<DrawerConfig> | null {
    return this.drawers.get(id)?.asObservable() || null;
  }

  /**
   * Check if drawer is open
   */
  isOpen(id: string): boolean {
    const drawer = this.drawers.get(id);
    return drawer ? drawer.value.isOpen : false;
  }

  /**
   * Close all drawers
   */
  closeAll(): void {
    this.drawers.forEach((drawer) => {
      const currentConfig = drawer.value;
      if (currentConfig.isOpen) {
        drawer.next({
          ...currentConfig,
          isOpen: false
        });
      }
    });
  }

  /**
   * Unregister a drawer
   */
  unregister(id: string): void {
    const drawer = this.drawers.get(id);
    if (drawer) {
      drawer.complete();
      this.drawers.delete(id);
    }
  }

  /**
   * Get all registered drawer IDs
   */
  getRegisteredDrawers(): string[] {
    return Array.from(this.drawers.keys());
  }
}
