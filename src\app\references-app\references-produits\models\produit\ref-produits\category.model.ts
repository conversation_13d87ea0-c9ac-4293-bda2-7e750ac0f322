interface ICategorie {
  codeCategorie: string;
  dateCreation: string;
  dateDernModif: string;
  dateSuppression: string;
  estActif: string;
  forceSuggestionPrd: boolean;
  id: number;
  libelleCategorie: string;
  tenantId: number;
}


export class Categorie implements ICategorie {
    codeCategorie: string;
    dateCreation: string;
    dateDernModif: string;
    dateSuppression: string;
    estActif: string;
    forceSuggestionPrd: boolean;
    id: number;
    libelleCategorie: string;
    tenantId: number;
    
    constructor(category?: Partial<ICategorie>) {
       Object.assign(this, category);

    }
}