import { ModePaiement } from "src/app/references-app/references-produits/enums/common/ModePaiement.enum"
import { SousStatutFluxFin } from "src/app/references-app/references-produits/enums/common/SousStatutFluxFin.enum"
import { Statut } from "src/app/references-app/references-produits/enums/common/Statut.enum"
import { FluxFin } from "../../common/fluxFin.model"
import { Operateur } from "../../common/operateur.model"
import { Tiers } from "../../tiers/tiers.model"
import { TypeDepense } from "./typeDepense.model"




export class Depense {
    id?: number
    fluxFin: FluxFin
    dateDepense: Date
    dateDebut?: Date
    dateFin?: Date
    libelleDepense: string
    typeDepense: TypeDepense
    montantTva: number
    montantHt: number
    montantTtc: number
    dateOperation?: Date
    fournisseur?: Tiers
    statut?: Statut
    operateur?: Operateur
    modeFlux?: ModePaiement // filter
    sousStatut?: SousStatutFluxFin //filter

    constructor() {
        this.fluxFin = new FluxFin()
    }

}