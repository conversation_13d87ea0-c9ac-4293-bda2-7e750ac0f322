import { Component, OnInit } from '@angular/core';
import { WinclientAnomaliesService } from '../../../Services/winclient.anomalies.service';
import { GridDataResult, RowClassArgs } from '@progress/kendo-angular-grid';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { AlertService } from 'src/app/shared/services/alert.service';
import { Anomalie, AnomalieCriteria, StatutAnomalie } from '../../../models/anomalie.model';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-list-anomalies-client-groupe',
  templateUrl: './list-anomalies-client-groupe.component.html',
  styleUrls: ['./list-anomalies-client-groupe.component.scss']
})
export class ListAnomaliesClientGroupeComponent implements OnInit {

  anomalies: GridDataResult = {
    data: [],
    total: 0
  };

  navigation: Pagination = {
    skip: 0,
    pageSize: 25,
  };

  selectedStatut: string = 'TOUS';
  anomalieCriteria: AnomalieCriteria = new AnomalieCriteria({});
  anomalieFilterForm: FormGroup;

  constructor(
    private anomaliesService: WinclientAnomaliesService,
    private alertService: AlertService,
    private userInputService: UserInputService,
    private router: Router,
    private fb: FormBuilder
    ,
  ) { }

  ngOnInit() {
    this.getAllAnomalies();
  }

  getAllAnomalies() {
    this.anomaliesService.searchAnomalies(this.navigation,this.anomalieCriteria).subscribe(res => {
      this.anomalies.data = res.content;
      this.anomalies.total = res.totalElements;
    });
  }

  changerStatutAnomalie(anomalie: Anomalie) {
    this.clearClickedItem();
    anomalie['isClicked'] = true;
    
    this.userInputService.confirm("Confirmer le changement de statut ?",`Voulez-vous vraiment changer ${anomalie.statut === StatutAnomalie.FREMEE ? 'ouvrir' : 'fermer'} cette anomalie ?`,"Oui","Non").then(result => {
      if(result){
        const newStatus = anomalie.statut === StatutAnomalie.FREMEE ? StatutAnomalie.OUVERTE : StatutAnomalie.FREMEE;
        this.anomaliesService.changerStatutAnomalie(anomalie.id, newStatus).subscribe(res => {
          this.getAllAnomalies();
        });
      }
    }).catch(() => { });
  }

  changeStatutFilter(filterValue: string) {
    this.selectedStatut = filterValue;
    this.anomalieCriteria = new AnomalieCriteria({
      statut: filterValue === 'TOUS' ? null : filterValue === 'OUVERT' ? StatutAnomalie.OUVERTE : StatutAnomalie.FREMEE
    });
    this.navigation.skip = 0;
    this.getAllAnomalies();
  }

  lancerBatch() {
    this.anomaliesService.lancerBatch().subscribe(res => {
      this.alertService.success("Le batch a été lancé avec succès");
      this.getAllAnomalies();
    });
  }

  goToClientGroupe(anomalie: Anomalie) {
    this.clearClickedItem();
    anomalie['isClicked'] = true;
    this.router.navigate(['/references/ref-clients/client-groupe'], { queryParams: { codeGroupe: anomalie.codeClientGroupe } });
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getAllAnomalies();
  }

  rowClass(args: RowClassArgs) {
    if (args.dataItem?.isClicked) {
      return { 'highlight-row-clicked': true };
    }
    return '';
  }

  clearClickedItem() {
    this.anomalies.data.forEach(item => {
      delete item['isClicked'];
    });
  }
}
