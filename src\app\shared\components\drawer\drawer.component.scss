// Drawer Component Styles
// Accessible, responsive drawer with animations

// Variables
$drawer-z-index: 1050;
$backdrop-z-index: 1040;
$drawer-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
$drawer-border-radius: 8px;
$animation-duration: 0.3s;
$animation-easing: cubic-bezier(0.25, 0.8, 0.25, 1);

// Backdrop
.drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: $backdrop-z-index;
  opacity: 0;
  transition: opacity $animation-duration $animation-easing;
  pointer-events: none;

  &--visible {
    opacity: 1;
    pointer-events: auto;
  }
}

// Main drawer container
.drawer {
  position: fixed;
  z-index: $drawer-z-index;
  background: var(--bs-body-bg, #ffffff);
  box-shadow: $drawer-shadow;
  transition: transform $animation-duration $animation-easing;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // Position variants
  &--left {
    top: 0;
    left: 0;
    height: 100%;
    transform: translateX(-100%);
    border-top-right-radius: $drawer-border-radius;
    border-bottom-right-radius: $drawer-border-radius;
  }

  &--right {
    top: 0;
    right: 0;
    height: 100%;
    transform: translateX(100%);
    border-top-left-radius: $drawer-border-radius;
    border-bottom-left-radius: $drawer-border-radius;
  }

  &--top {
    top: 0;
    left: 0;
    width: 100%;
    transform: translateY(-100%);
    border-bottom-left-radius: $drawer-border-radius;
    border-bottom-right-radius: $drawer-border-radius;
  }

  &--bottom {
    bottom: 0;
    left: 0;
    width: 100%;
    transform: translateY(100%);
    border-top-left-radius: $drawer-border-radius;
    border-top-right-radius: $drawer-border-radius;
  }

  // Size variants for left/right drawers
  &--left,
  &--right {
    &.drawer--small {
      width: 320px;
      max-width: 90vw;
    }

    &.drawer--medium {
      width: 480px;
      max-width: 90vw;
    }

    &.drawer--large {
      width: 640px;
      max-width: 90vw;
    }

    &.drawer--full {
      width: 100vw;
    }
  }

  // Size variants for top/bottom drawers
  &--top,
  &--bottom {
    &.drawer--small {
      height: 240px;
      max-height: 50vh;
    }

    &.drawer--medium {
      height: 360px;
      max-height: 60vh;
    }

    &.drawer--large {
      height: 480px;
      max-height: 80vh;
    }

    &.drawer--full {
      height: 100vh;
    }
  }

  // Open state
  &--open {
    transform: translate(0, 0);
  }

  // No animation variant
  &--no-animation {
    transition: none;
  }

  // Focus outline for accessibility
  &:focus {
    outline: 2px solid var(--bs-primary, #0d6efd);
    outline-offset: -2px;
  }
}

// Drawer content wrapper
.drawer__content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

// Header
.drawer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--bs-border-color, #dee2e6);
  background: var(--bs-light, #f8f9fa);
  flex-shrink: 0;

  &-content {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0; // Allow text truncation
  }
}

.drawer__title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--bs-body-color, #212529);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.drawer__close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  padding: 0;
  margin-left: 1rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 50%;
  color: var(--bs-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  flex-shrink: 0;

  &:hover {
    background: var(--bs-secondary-bg, #e9ecef);
    color: var(--bs-body-color, #212529);
  }

  &:focus {
    outline: 2px solid var(--bs-primary, #0d6efd);
    outline-offset: 2px;
  }

  &:active {
    transform: scale(0.95);
  }

  i {
    font-size: 1.25rem;
    line-height: 1;
  }
}

// Body
.drawer__body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem;
  
  // Custom scrollbar for webkit browsers
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bs-light, #f8f9fa);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--bs-secondary, #6c757d);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--bs-dark, #212529);
  }
}

// Footer
.drawer__footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--bs-border-color, #dee2e6);
  background: var(--bs-light, #f8f9fa);
  flex-shrink: 0;
}

// Screen reader only content
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// Body scroll prevention
:global(body.drawer-open) {
  overflow: hidden;
}

// Responsive adjustments
@media (max-width: 768px) {
  .drawer {
    &--left,
    &--right {
      &.drawer--small,
      &.drawer--medium,
      &.drawer--large {
        width: 100vw;
        max-width: none;
      }
    }

    &--top,
    &--bottom {
      &.drawer--small,
      &.drawer--medium,
      &.drawer--large {
        height: 100vh;
        max-height: none;
      }
    }
  }

  .drawer__header {
    padding: 0.75rem 1rem;
  }

  .drawer__body {
    padding: 1rem;
  }

  .drawer__footer {
    padding: 0.75rem 1rem;
  }

  .drawer__title {
    font-size: 1.125rem;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .drawer,
  .drawer-backdrop {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .drawer {
    border: 2px solid;
  }

  .drawer__close-button {
    border: 1px solid;
  }
}

// Dark mode support (if using CSS custom properties)
@media (prefers-color-scheme: dark) {
  .drawer {
    background: var(--bs-dark, #212529);
    color: var(--bs-light, #f8f9fa);
  }

  .drawer__header,
  .drawer__footer {
    background: var(--bs-secondary, #6c757d);
    border-color: var(--bs-border-color-translucent, rgba(255, 255, 255, 0.15));
  }
}
