export type ProductStats =  {
    totalProdBaseGroupeSansCodeWinplus: number;
    derniereMajParSite: {
      codeSite: number;
      libelleSource: string;
      dateMajFormatted: string;
    }[];
    totalProdSiteSansCodeGroupe: number;
  }

interface IStatsCriteria {
    dateAu: string;
    dateDu: string;
}

export class StatsCriteria implements IStatsCriteria {
    dateAu: string;
    dateDu: string;
  
    constructor(data: Partial<IStatsCriteria>) {
      Object.assign(this, data);
    }
  }