import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { GridDataResult } from '@progress/kendo-angular-grid';
  import { FormControl, FormGroup, Validators } from '@angular/forms';
import { UserInputService } from 'src/app/shared/services/user-input.service';
 import { AlertService } from 'src/app/shared/services/alert.service';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { WinClientRegion } from '../../../models/region';
import { WinclientRegionService } from '../../../Services/winclient.region.service';

@Component({
  selector: 'app-list-region',
  templateUrl: './list-region.component.html',
  styleUrls: ['./list-region.component.scss']
})
export class ListRegionComponent implements OnInit {

  @ViewChild('updateOrCreateRegionModal') updateOrCreateRegionModal: TemplateRef<any>;

  modalMode : "EDIT" | "CREATE" = "CREATE";
  submited = false;
  modalRef : NgbModalRef;

  regionForm = new FormGroup({
      id: new FormControl(null),
      libRegion: new FormControl('', [Validators.required,Validators.maxLength(30)])
    });
  

  navigation : Pagination = {
    pageSize :21,
    skip:0,
  }

  regions:GridDataResult = {
    data:[] as WinClientRegion[],
    total:0
  };




  constructor(
    private modalService: NgbModal,
    private userInputService:UserInputService,
    private regionServ: WinclientRegionService,
    private alertService:AlertService
  ) { }

  ngOnInit() {
    this.getListRegions(0);
  }


  
  formErrors(field:string){
    return this.regionForm.controls[field];
  }

  getListRegions(page=0){
    this.navigation.skip = page * this.navigation.pageSize;
    this.regionServ.getAllRegionPaginated({page}).subscribe(res => {
      this.regions.data = res.content;
      this.regions.total = res.totalElements;
    });
 
  }

  openCreateRegion() {
    this.modalMode = "CREATE";
    this.modalRef = this.modalService.open(this.updateOrCreateRegionModal, { size: 'lg' });
    this.regionForm.reset();
  }


  openEditRegion(region:WinClientRegion){
    this.modalMode = "EDIT";
    this.regionForm.patchValue(region);
    this.modalRef = this.modalService.open(this.updateOrCreateRegionModal, { size: 'lg' });
  }


  confirmDeleteRegion(region:WinClientRegion){
    this.userInputService.confirm("Confirmer la suppression de la Region ?","Voulez-vous vraiment supprimer cette Region ?","Supprimer","Annuler").then(result => {
      if(result){
        this.deleteVille(region);
      }
    }).catch(() => { });
  }

  saveOrUpdateRegion(){
    this.submited = true;
    if(this.regionForm.invalid){
      this.alertService.error("Veuillez remplir tous les champs");
      return;
    }


    const region = this.regionForm.value;
    console.log("region from form", region)

    if(this.modalMode == "EDIT"){
      this.updateRegion(region);
    }else{
      this.createRegion(region);
    }
    this.modalRef?.close();
  }




  private deleteVille(ville:any){
        this.regionServ.deleteRegion(ville.id).subscribe(res => {
          this.alertService.success("La Region a été supprimée avec succès");
          this.getListRegions();
        });
    }
  
    private updateRegion(region:WinClientRegion){
      
      const payload = new WinClientRegion({
        libRegion : region.libRegion
      });
  
      this.regionServ.updateRegion(region.id,payload).subscribe(res => {
        this.getListRegions();
        this.alertService.success("La Region a été modifiée avec succès");
      });
    }
  
    private createRegion(region:WinClientRegion){
  
      const payload = new WinClientRegion({
        libRegion : region.libRegion
      });
      
      this.regionServ.createRegion(payload).subscribe(res => {
        this.getListRegions();
      });
    }


}
