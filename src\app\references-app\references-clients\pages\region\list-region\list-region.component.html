<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Regios</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <button type="button" class="btn btn-primary"   (click)="openCreateRegion()"
         >
         <i class="mdi mdi-plus"></i>
          Nouveau
        </button>
      </div>
    </div>
  </div>
</div>

<kendo-grid [data]="regions" style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid ref-grid">
  <kendo-grid-column field="libRegion" title="Libéllé Region" class="text-start" [headerClass]="'text-start'"></kendo-grid-column>
  <kendo-grid-column>
    <ng-template kendoGridCellTemplate let-dataItem >
      <app-action-icon [icon]="'pencil'" [extendClass]="'circle-lg'" (click)="openEditRegion(dataItem)">

      </app-action-icon>
      <app-action-icon [icon]="'close'" [extendClass]="'circle-lg'" [backgroundColor]="'danger'" (click)="confirmDeleteRegion(dataItem)">

      </app-action-icon>
    </ng-template>
  </kendo-grid-column>
</kendo-grid>



<ng-template #updateOrCreateRegionModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      <span *ngIf="modalMode == 'CREATE'">Créer une nouvelle Region</span>
      <span *ngIf="modalMode == 'EDIT'">Modifier la Region</span>
    </h4>
    <button type="button" class="cross-button" (click)="modal.close()" >
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="regionForm">
      <div class="mb-3">
        <label for="nom" class="form-label">Nom de Region<span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="nom" formControlName="libRegion" [ngClass]="{'is-invalid':submited && formErrors('libRegion').errors}"
         name="nom" placeholder="Nom de Region">
        <span class="text-danger" *ngIf="submited && formErrors('libRegion').errors?.required">Ce champs est obligatoire</span>
        <span class="text-danger" *ngIf="submited && formErrors('libRegion').errors?.maxlength">Le nom de la ville est trop long (30 caractères max)</span>

      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary"  (click)="modal.close()" >Annuler</button>
    <button type="button" class="btn btn-primary" (click)="saveOrUpdateRegion()" >
      {{modalMode == "CREATE" ? "Enregistrer" : "Modifier "}}
    </button>
  </div>
</ng-template>
