interface IDci {
  code: string;
  id: number;
  libelleDci: string;
}

interface IDciCriteria {
    libelle: string;

}

export class Dci implements IDci {
  code: string = '';
  id: number = 0;
  libelleDci: string = '';

  constructor(data?: Partial<IDci>) {
    Object.assign(this, data);
  }
}


export class DciCriteria implements IDciCriteria {
  libelle: string = '';

  constructor(data?: Partial<IDciCriteria>) {
    Object.assign(this, data);
  }
}
