import { Directive, EventEmitter, Input, Output } from '@angular/core';

// export type SortColumn = keyof  | '';
export type SortDirection = 'asc' | 'desc' | '';
const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };
export interface SortEvent {
  column: string;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  host: {
    '[class.sorting_asc]': 'direction === "asc"',
    '[class.sorting_desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbSortableHeaderDirective {

  @Input() sortable: string = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  constructor () {
  }

  /**
   *  toggles sort direction
   */
  rotate(): void {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }

}
