interface IProduitSite {
  id?: number;
  codeSite?: number;
  idSource?: string;
  libelleSource?: string;
  codeWinplus?: string;
  laboratoireLabel?: string;
  categorie?: string;
  formeGalenique?: string;
  dci?: string;
  designation?: string;
  codeBarre?: string;
  codeBarre2?: string;
  prixVenteStd?: number;
  prixAchatStd?: number;
  tvaLabel?: string;
  estRbrsblBase?: boolean;
  pbrP?: number;
  estOblgPrescription?: boolean;
  estMarche?: boolean;
  laboratoireType?: string;
  dosage?: string;
  nomVolume?: string;
  presentation?: string;
  nomRacine?: string;
  nomPoids?: string;
  prixHosp?: number;
  estToxique?: boolean;
  estFabrique?: boolean;
  estTpaBase?: boolean;
  estPrinceps?: boolean;
  estPsychotrope?: boolean;
  typeProcess?: string;
  tauxRemb?: number;
  pbrH?: number;
  dateFinCom?: string;
  estVendable?: boolean;
  estStockable?: boolean;
  estPrixmarque?: boolean;
  margePercent?: string;
  ftLabel?: string;
  dateCreation?: string;
  dateMaj?: string;
  dateSuppression?: string;
  gamme?: string;
  actifVente?: boolean;
  actifStock?: boolean;
  typeArticle?: string;
  groupeMarchandise?: string;
  dateFinCommercialisation?: string;
  laboratoireFabriquant?: string;
  codeGroupe?: string;
  verified?: boolean;
  uniteVente?: string;
  colisage?: string;
  qteColisage?: number;
  pfht?: number;
  produitExtensions?: IProduitExtensions;
  dateDeclarationForProcess?: string;
}

interface IProduitExtensions {
  id?: number;
  code?: number;
  codeSpecialEchange?: string;
  codeGroupe?: string;
  dateEnvoi?: string;
  estAncien?: string;
  dateMajCreation?: string;
  dateMajModification?: string;
}


export class ProduitExtensions implements IProduitExtensions {
  id: number;
  code: number;
  codeSpecialEchange: string;
  codeGroupe: string;
  dateEnvoi: string;
  estAncien: string;
  dateMajCreation: string;
  dateMajModification: string;

  constructor(data: Partial<IProduitExtensions>) {
    Object.assign(this, data);
  }
}

export class ProduitSite implements IProduitSite {
  actifStock: boolean;
  actifVente: boolean;
  categorie: string;
  codeBarre: string;
  codeBarre2: string;
  codeGroupe: string;
  codeSite: number;
  codeWinplus: string;
  colisage: string;
  dateCreation: string;
  dateDeclarationForProcess: string;
  dateFinCom: string;
  dateFinCommercialisation: string;
  dateMaj: string;
  dateSuppression: string;
  dci: string;
  designation: string;
  dosage: string;
  estFabrique: boolean;
  estMarche: boolean;
  estOblgPrescription: boolean;
  estPrinceps: boolean;
  estPrixmarque: boolean;
  estPsychotrope: boolean;
  estRbrsblBase: boolean;
  estStockable: boolean;
  estToxique: boolean;
  estTpaBase: boolean;
  estVendable: boolean;
  formeGalenique: string;
  ftLabel: string;
  gamme: string;
  groupeMarchandise: string;
  idSource: string;
  laboratoireFabriquant: string;
  laboratoireLabel: string;
  laboratoireType: string;
  libelleSource: string;
  margePercent: string;
  nomPoids: string;
  nomRacine: string;
  nomVolume: string;
  pbrH: number;
  pbrP: number;
  pfht: number;
  presentation: string;
  prixAchatStd: number;
  prixHosp: number;
  prixVenteStd: number;
  produitExtensions: IProduitExtensions;
  qteColisage: number;
  tauxRemb: number;
  tvaLabel: string;
  typeArticle: string;
  typeProcess: string;
  uniteVente: string;
  verified: boolean;

  constructor(data: Partial<IProduitSite>) {
    Object.assign(this, data);
  }

}
