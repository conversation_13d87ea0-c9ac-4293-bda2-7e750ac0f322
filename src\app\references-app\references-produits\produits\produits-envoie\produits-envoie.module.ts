import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SharedModule } from 'src/app/shared/shared.module';
import { ReferentialModule } from 'src/app/references-app/referential/referential.module';
import { GridModule } from '@progress/kendo-angular-grid';
import { NgbAccordionModule, NgbCollapseModule, NgbDatepickerModule, NgbDropdownModule, NgbModalModule, NgbTypeaheadModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Select2Module } from 'ng-select2-component';
import { NgxMaskModule } from 'ngx-mask';

import { InputsModule } from '@progress/kendo-angular-inputs';
import { ProduitsWinplusRoutingModule } from './produits-envoie-routing.module';
import { ListProduitsEnvoieComponent } from './list-produits-envoie/list-produits-envoie.component';

@NgModule({
  declarations: [
    ListProduitsEnvoieComponent,

  ],
  imports: [
    CommonModule,
    ProduitsWinplusRoutingModule,
    ReferentialModule,
    NgbDatepickerModule,
    NgbTypeaheadModule,
    ReactiveFormsModule,
    NgbDropdownModule,
    FormsModule,
    InputsModule,
    Select2Module,
    NgbModalModule,
    NgbAccordionModule,
    NgbCollapseModule,
    NgxMaskModule.forChild(),
    SharedModule,
    GridModule,
    NgbNavModule,
  ]
})
export class ProduitsEnvoieModule { }
