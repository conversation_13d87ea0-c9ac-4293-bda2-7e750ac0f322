import { Directive, Input, Output, EventEmitter, HostListener, ElementRef, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appCopyToClipboard]'
})
export class CopyToClipboardDirective {
  @Input('appCopyToClipboard') textToCopy: string = '';
  @Output() copied = new EventEmitter<string>();
  
  constructor(private el: ElementRef, private renderer: Renderer2) {}

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent): void {
    event.stopPropagation();
    
    if (!this.textToCopy) return;
    
    navigator.clipboard.writeText(this.textToCopy.trim())
      .then(() => {
        this.copied.emit(this.textToCopy.trim());
       })
      .catch(err => {
        console.error('Failed to copy: ', err);
       });
  }

  
}