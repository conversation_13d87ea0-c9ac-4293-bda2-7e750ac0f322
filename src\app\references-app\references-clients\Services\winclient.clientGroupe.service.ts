import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ClientGroupeCriteria } from '../models/clientGroupe';
import { ClientGroupe } from '../models/clientGroupe';
import { environment as env } from 'src/environments/environment';
import { Page } from '../../referential/models/Page/page.model';
@Injectable({
  providedIn: 'root'
})
export class ClientGroupeService {

  constructor(private http: HttpClient) { }
  
  searchClientGroupe(clientGroupeCriteria: Partial<ClientGroupeCriteria>) {
    return this.http.post<Page<ClientGroupe>>(`${env.winclient_base_url}/api/winclient/client-groupe/search`, clientGroupeCriteria,{
      params:{
        page:clientGroupeCriteria.page,
        size:clientGroupeCriteria.size
      }
    });
  }
  
  getAllClientGroupePaginated({page =0}:{page:number}) {
    return this.http.get<Page<ClientGroupe>>(`${env.winclient_base_url}/api/winclient/client-groupe`, { 
        params:{page}
     });
  }


  getClientGroupeById(id: number) {
    return this.http.get<ClientGroupe>(`${env.winclient_base_url}/api/winclient/client-groupe/${id}`);
  }

  createClientGroupe(clientGroupe: Omit<ClientGroupe,"id">) {
    return this.http.post<ClientGroupe>(`${env.winclient_base_url}/api/winclient/client-groupe`, clientGroupe);
  }

  updateClientGroupe(id:number,clientGroupe: Omit<ClientGroupe,"id">) {
    return this.http.put<ClientGroupe>(`${env.winclient_base_url}/api/winclient/client-groupe/${id}`, clientGroupe);
  }

  deleteClientGroupe(id: number) {
    return this.http.delete<ClientGroupe>(`${env.winclient_base_url}/api/winclient/client-groupe/${id}`);
  }


  
  
 
}