<!-- floating-window.component.html -->
<div *ngIf="isShown" class="floating-window" [style.top]="top + 'px'" [style.left]="left + 'px'" [style.width]="width + 'px'" [style.height]="height + 'px'" [style.z-index]="zIndex">
  <div class="floating-window-header" (mousedown)="onDragStart($event)">
    <div class="floating-window-title">Paramètres Police</div>
    <div class="floating-window-close" (click)="closeWindow()">Close</div>
  </div>
  <div class="floating-window-content">
    <!-- Add your content here, e.g., a slider and label -->
   <div class="d-flex gap-1 align-items-center">
    <div class="flex-grow-1">
      <input type="range" min="0.5" max="1.5" class="w-100" [step]="scaleStep" (input)="onScaleChange($event)" [value]="scale" >
    </div>
    <div class="info">{{scale}}</div>
   </div>
   <div class="d-flex gap-2">
    <div class="d-flex align-items-center position-relative">
    <label for="scale" class="ms-1 position-absolute">Scale Steps :</label>  
      <input type="number" id="scale" name="scale" style="padding-left: 124px;" step="0.01" title="Reset Scale Steps" class="form-control form-control-sm" placeholder="steps" [value]="scaleStep" (change)="onScaleStepChange($event)">
    </div>
    <button (click)="ResetScale()" title="Reset The Sacele to 1"  class="btn py-1">reset</button>
   </div>
  </div>
  <div class="floating-window-resize-handle" (mousedown)="startResize($event)"></div>
</div>
