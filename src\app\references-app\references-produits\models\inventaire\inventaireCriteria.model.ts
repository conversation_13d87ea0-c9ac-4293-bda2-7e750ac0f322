import { StatutInventaire } from 'src/app/references-app/references-produits/enums/inventaire/StatutInventaire.enum';
import { MethodeInventaire } from 'src/app/references-app/references-produits/enums/inventaire/MethodeInventaire.enum';

// import { Moment } from 'moment';



export class InventaireCriteria { 
    dateCreationMax?: any;
    dateCreationMin?: any;
    dateInventaireMax?: any;
    dateInventaireMin?: any;
    libelleDepot?: string;
    methode?: MethodeInventaire;
    numeroInventaire?: number;
    statutInventaire?: StatutInventaire;
}

