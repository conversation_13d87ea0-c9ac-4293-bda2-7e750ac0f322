import { TypeRemiseVente } from 'src/app/references-app/references-produits/enums/vente/TypeRemiseVente.enum';
import { ConfigurationGestionRemise } from 'src/app/references-app/references-produits/enums/parametre/ConfigurationGestionRemise.enum';



export class ParametresRemise { 
    audited?: boolean;
    gestionRemise?: ConfigurationGestionRemise;
    listeTypesRemiseOrdonnees?: TypeRemiseVente[];
    userModifiable?: boolean;
}


