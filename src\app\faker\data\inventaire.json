{"inventaires": [{"id": 1, "ref": 3205, "dateCreation": "2021-07-19T07:07:44Z", "date": "2021-10-08T23:12:01Z", "perimetre": "M", "methode": "R", "statut": "VP"}, {"id": 2, "ref": 4833, "dateCreation": "2022-06-07T15:14:24Z", "date": "2022-05-02T12:32:50Z", "perimetre": "M", "methode": "R", "statut": "B"}, {"id": 3, "ref": 9335, "dateCreation": "2021-11-18T14:18:18Z", "date": "2022-01-27T12:43:52Z", "perimetre": "M", "methode": "S", "statut": "A"}, {"id": 4, "ref": 4318, "dateCreation": "2022-01-15T07:29:43Z", "date": "2021-10-28T23:17:09Z", "perimetre": "M", "methode": "S", "statut": "A"}, {"id": 5, "ref": 6176, "dateCreation": "2022-05-21T17:19:10Z", "date": "2021-11-10T00:26:46Z", "perimetre": "M", "methode": "R", "statut": "B"}, {"id": 6, "ref": 5263, "dateCreation": "2022-01-06T03:03:11Z", "date": "2021-09-15T06:23:22Z", "perimetre": "Z", "methode": "C", "statut": "B"}, {"id": 7, "ref": 1789, "dateCreation": "2021-09-19T21:01:20Z", "date": "2022-04-23T21:22:51Z", "perimetre": "Z", "methode": "FT", "statut": "A"}, {"id": 8, "ref": 2504, "dateCreation": "2022-05-12T05:33:52Z", "date": "2022-06-03T16:51:26Z", "perimetre": "G", "methode": "L", "statut": "VT"}, {"id": 9, "ref": 8964, "dateCreation": "2021-09-11T04:56:18Z", "date": "2021-08-28T23:36:12Z", "perimetre": "G", "methode": "FT", "statut": "VP"}, {"id": 10, "ref": 2048, "dateCreation": "2022-05-12T03:11:31Z", "date": "2021-10-30T20:45:18Z", "perimetre": "Z", "methode": "CB", "statut": "VP"}, {"id": 11, "ref": 2776, "dateCreation": "2022-02-24T15:15:56Z", "date": "2021-12-07T04:16:52Z", "perimetre": "G", "methode": "R", "statut": "B"}, {"id": 12, "ref": 1689, "dateCreation": "2022-04-09T18:38:54Z", "date": "2022-06-11T03:23:12Z", "perimetre": "M", "methode": "F", "statut": "B"}, {"id": 13, "ref": 2948, "dateCreation": "2022-03-18T14:26:27Z", "date": "2022-06-27T15:35:41Z", "perimetre": "Z", "methode": "CB", "statut": "B"}, {"id": 14, "ref": 1719, "dateCreation": "2022-06-15T00:44:48Z", "date": "2022-06-14T01:04:24Z", "perimetre": "G", "methode": "R", "statut": "A"}, {"id": 15, "ref": 6146, "dateCreation": "2021-07-23T09:41:07Z", "date": "2022-01-01T02:52:45Z", "perimetre": "M", "methode": "R", "statut": "B"}, {"id": 16, "ref": 7084, "dateCreation": "2021-10-14T19:58:25Z", "date": "2021-08-08T12:13:20Z", "perimetre": "Z", "methode": "FT", "statut": "VT"}, {"id": 17, "ref": 1576, "dateCreation": "2021-12-01T16:27:36Z", "date": "2022-03-06T22:33:24Z", "perimetre": "M", "methode": "R", "statut": "VT"}, {"id": 18, "ref": 2034, "dateCreation": "2022-02-20T02:46:42Z", "date": "2021-08-13T14:10:57Z", "perimetre": "Z", "methode": "FT", "statut": "VT"}, {"id": 19, "ref": 4944, "dateCreation": "2022-05-28T13:32:28Z", "date": "2021-10-28T10:55:33Z", "perimetre": "Z", "methode": "S", "statut": "VT"}, {"id": 20, "ref": 2688, "dateCreation": "2021-08-08T20:18:48Z", "date": "2021-11-12T00:07:33Z", "perimetre": "Z", "methode": "F", "statut": "VT"}, {"id": 21, "ref": 1237, "dateCreation": "2021-08-09T03:53:47Z", "date": "2021-12-09T00:21:57Z", "perimetre": "G", "methode": "C", "statut": "VP"}, {"id": 22, "ref": 3034, "dateCreation": "2021-08-24T07:25:29Z", "date": "2021-09-13T22:06:48Z", "perimetre": "M", "methode": "L", "statut": "B"}, {"id": 23, "ref": 3570, "dateCreation": "2022-05-08T13:03:01Z", "date": "2021-08-20T04:02:45Z", "perimetre": "G", "methode": "FT", "statut": "B"}, {"id": 24, "ref": 9473, "dateCreation": "2022-05-17T09:34:25Z", "date": "2021-10-30T15:43:33Z", "perimetre": "G", "methode": "F", "statut": "B"}, {"id": 25, "ref": 1088, "dateCreation": "2021-10-10T10:58:40Z", "date": "2022-01-15T00:29:17Z", "perimetre": "G", "methode": "R", "statut": "B"}, {"id": 26, "ref": 6489, "dateCreation": "2021-11-15T09:46:48Z", "date": "2022-05-19T05:33:55Z", "perimetre": "Z", "methode": "CB", "statut": "VP"}, {"id": 27, "ref": 6459, "dateCreation": "2022-04-10T08:52:35Z", "date": "2022-03-18T05:41:12Z", "perimetre": "M", "methode": "CB", "statut": "A"}, {"id": 28, "ref": 6965, "dateCreation": "2021-10-08T16:31:06Z", "date": "2021-10-04T14:19:07Z", "perimetre": "G", "methode": "C", "statut": "VT"}, {"id": 29, "ref": 5776, "dateCreation": "2021-09-11T16:03:53Z", "date": "2022-04-22T02:29:33Z", "perimetre": "G", "methode": "S", "statut": "A"}, {"id": 30, "ref": 2262, "dateCreation": "2021-11-14T21:24:54Z", "date": "2022-01-09T22:51:14Z", "perimetre": "Z", "methode": "F", "statut": "B"}, {"id": 31, "ref": 7994, "dateCreation": "2021-07-31T01:59:37Z", "date": "2022-04-02T00:52:16Z", "perimetre": "G", "methode": "C", "statut": "VP"}, {"id": 32, "ref": 7449, "dateCreation": "2022-03-09T17:05:24Z", "date": "2022-04-18T18:25:57Z", "perimetre": "Z", "methode": "CB", "statut": "A"}, {"id": 33, "ref": 6599, "dateCreation": "2021-11-22T22:44:51Z", "date": "2021-10-06T20:38:09Z", "perimetre": "G", "methode": "CB", "statut": "VT"}, {"id": 34, "ref": 1038, "dateCreation": "2021-08-10T14:00:54Z", "date": "2021-10-30T17:44:20Z", "perimetre": "M", "methode": "FT", "statut": "A"}, {"id": 35, "ref": 8161, "dateCreation": "2021-09-26T07:05:33Z", "date": "2022-01-20T20:46:05Z", "perimetre": "G", "methode": "FT", "statut": "VT"}, {"id": 36, "ref": 2122, "dateCreation": "2022-05-24T12:21:23Z", "date": "2022-06-26T11:00:21Z", "perimetre": "G", "methode": "L", "statut": "VT"}, {"id": 37, "ref": 9531, "dateCreation": "2022-04-03T13:22:07Z", "date": "2021-09-13T21:15:49Z", "perimetre": "G", "methode": "CB", "statut": "VT"}, {"id": 38, "ref": 3434, "dateCreation": "2021-09-07T22:56:46Z", "date": "2022-05-09T00:34:19Z", "perimetre": "Z", "methode": "F", "statut": "VP"}, {"id": 39, "ref": 4323, "dateCreation": "2022-05-10T12:41:41Z", "date": "2021-11-16T05:56:06Z", "perimetre": "M", "methode": "F", "statut": "VP"}, {"id": 40, "ref": 8235, "dateCreation": "2021-08-22T11:14:58Z", "date": "2022-06-14T16:30:32Z", "perimetre": "M", "methode": "L", "statut": "VP"}, {"id": 41, "ref": 5942, "dateCreation": "2021-08-31T22:40:05Z", "date": "2021-11-17T03:47:46Z", "perimetre": "Z", "methode": "F", "statut": "VT"}, {"id": 42, "ref": 2078, "dateCreation": "2021-11-13T05:02:12Z", "date": "2022-05-18T04:59:47Z", "perimetre": "Z", "methode": "F", "statut": "VT"}, {"id": 43, "ref": 8190, "dateCreation": "2021-12-22T08:28:20Z", "date": "2021-12-25T23:20:04Z", "perimetre": "M", "methode": "R", "statut": "VT"}, {"id": 44, "ref": 5717, "dateCreation": "2022-01-26T15:57:07Z", "date": "2021-10-23T21:55:43Z", "perimetre": "Z", "methode": "L", "statut": "A"}, {"id": 45, "ref": 5555, "dateCreation": "2022-01-13T15:35:23Z", "date": "2021-08-21T22:20:44Z", "perimetre": "M", "methode": "L", "statut": "VP"}, {"id": 46, "ref": 1216, "dateCreation": "2021-09-24T02:08:46Z", "date": "2022-07-15T03:43:57Z", "perimetre": "Z", "methode": "F", "statut": "VT"}, {"id": 47, "ref": 7640, "dateCreation": "2021-09-07T00:31:36Z", "date": "2021-12-27T17:42:19Z", "perimetre": "G", "methode": "FT", "statut": "A"}, {"id": 48, "ref": 5965, "dateCreation": "2021-11-09T13:07:00Z", "date": "2021-07-24T18:55:21Z", "perimetre": "Z", "methode": "FT", "statut": "A"}, {"id": 49, "ref": 6902, "dateCreation": "2021-12-03T10:35:05Z", "date": "2021-08-02T20:54:24Z", "perimetre": "Z", "methode": "C", "statut": "B"}, {"id": 50, "ref": 8800, "dateCreation": "2021-09-27T01:22:03Z", "date": "2021-11-14T05:33:03Z", "perimetre": "M", "methode": "C", "statut": "A"}, {"id": 51, "ref": 2188, "dateCreation": "2022-02-17T14:21:22Z", "date": "2021-11-13T04:29:43Z", "perimetre": "Z", "methode": "C", "statut": "VP"}, {"id": 52, "ref": 1832, "dateCreation": "2022-01-29T02:17:00Z", "date": "2022-02-21T04:20:43Z", "perimetre": "M", "methode": "FT", "statut": "VT"}, {"id": 53, "ref": 6041, "dateCreation": "2022-06-30T00:10:54Z", "date": "2021-10-21T00:48:53Z", "perimetre": "M", "methode": "S", "statut": "VP"}, {"id": 54, "ref": 2943, "dateCreation": "2021-11-25T15:53:17Z", "date": "2022-03-30T05:16:23Z", "perimetre": "G", "methode": "F", "statut": "B"}, {"id": 55, "ref": 7392, "dateCreation": "2021-11-05T19:58:56Z", "date": "2022-01-28T15:44:54Z", "perimetre": "M", "methode": "FT", "statut": "A"}, {"id": 56, "ref": 2613, "dateCreation": "2022-07-03T06:17:11Z", "date": "2022-03-09T09:34:54Z", "perimetre": "Z", "methode": "C", "statut": "VP"}, {"id": 57, "ref": 5637, "dateCreation": "2021-09-05T00:25:26Z", "date": "2022-06-30T04:58:26Z", "perimetre": "M", "methode": "FT", "statut": "A"}, {"id": 58, "ref": 6315, "dateCreation": "2022-05-08T14:26:20Z", "date": "2022-01-03T01:26:50Z", "perimetre": "M", "methode": "FT", "statut": "B"}, {"id": 59, "ref": 4649, "dateCreation": "2022-02-19T17:12:31Z", "date": "2021-11-15T13:11:58Z", "perimetre": "M", "methode": "CB", "statut": "VP"}, {"id": 60, "ref": 3705, "dateCreation": "2021-08-11T06:51:29Z", "date": "2022-01-13T08:36:03Z", "perimetre": "Z", "methode": "FT", "statut": "B"}, {"id": 61, "ref": 6288, "dateCreation": "2021-07-30T14:43:13Z", "date": "2022-03-16T01:31:48Z", "perimetre": "Z", "methode": "L", "statut": "B"}, {"id": 62, "ref": 8406, "dateCreation": "2022-06-23T20:22:41Z", "date": "2022-06-30T14:31:54Z", "perimetre": "M", "methode": "CB", "statut": "VP"}, {"id": 63, "ref": 4495, "dateCreation": "2021-10-26T04:27:35Z", "date": "2022-01-03T17:47:26Z", "perimetre": "M", "methode": "C", "statut": "VT"}, {"id": 64, "ref": 4177, "dateCreation": "2022-06-14T20:01:29Z", "date": "2022-05-30T01:04:06Z", "perimetre": "G", "methode": "FT", "statut": "B"}, {"id": 65, "ref": 5620, "dateCreation": "2022-02-27T12:51:29Z", "date": "2021-12-20T21:06:46Z", "perimetre": "G", "methode": "S", "statut": "VP"}, {"id": 66, "ref": 1594, "dateCreation": "2022-04-21T13:35:11Z", "date": "2022-05-14T00:34:32Z", "perimetre": "G", "methode": "S", "statut": "VP"}, {"id": 67, "ref": 1159, "dateCreation": "2022-01-02T21:23:12Z", "date": "2022-04-25T19:28:49Z", "perimetre": "G", "methode": "FT", "statut": "B"}, {"id": 68, "ref": 3244, "dateCreation": "2022-04-08T07:07:37Z", "date": "2022-07-06T06:11:47Z", "perimetre": "G", "methode": "FT", "statut": "VT"}, {"id": 69, "ref": 5562, "dateCreation": "2021-08-26T21:07:58Z", "date": "2021-12-01T02:11:10Z", "perimetre": "Z", "methode": "R", "statut": "A"}, {"id": 70, "ref": 1833, "dateCreation": "2021-11-15T08:26:46Z", "date": "2021-09-21T00:46:51Z", "perimetre": "G", "methode": "F", "statut": "VT"}, {"id": 71, "ref": 4048, "dateCreation": "2021-07-27T16:22:56Z", "date": "2022-03-13T15:37:55Z", "perimetre": "M", "methode": "FT", "statut": "B"}, {"id": 72, "ref": 7524, "dateCreation": "2021-12-20T00:53:06Z", "date": "2022-05-12T09:11:04Z", "perimetre": "Z", "methode": "S", "statut": "VP"}, {"id": 73, "ref": 1271, "dateCreation": "2022-07-03T15:56:50Z", "date": "2021-10-31T20:55:08Z", "perimetre": "Z", "methode": "C", "statut": "B"}, {"id": 74, "ref": 6497, "dateCreation": "2021-10-20T08:52:27Z", "date": "2021-11-12T04:32:44Z", "perimetre": "M", "methode": "C", "statut": "VT"}, {"id": 75, "ref": 8366, "dateCreation": "2022-01-19T16:51:06Z", "date": "2021-09-16T17:30:24Z", "perimetre": "M", "methode": "FT", "statut": "VT"}, {"id": 76, "ref": 7142, "dateCreation": "2022-01-19T05:13:06Z", "date": "2022-03-16T00:22:32Z", "perimetre": "M", "methode": "S", "statut": "VT"}, {"id": 77, "ref": 7661, "dateCreation": "2021-10-18T11:07:11Z", "date": "2021-11-27T00:58:10Z", "perimetre": "G", "methode": "F", "statut": "VT"}, {"id": 78, "ref": 8595, "dateCreation": "2021-07-19T20:32:27Z", "date": "2022-03-04T05:39:29Z", "perimetre": "Z", "methode": "F", "statut": "VT"}, {"id": 79, "ref": 2837, "dateCreation": "2021-08-16T05:53:08Z", "date": "2021-12-10T12:46:24Z", "perimetre": "Z", "methode": "L", "statut": "A"}, {"id": 80, "ref": 4985, "dateCreation": "2021-09-19T03:44:59Z", "date": "2021-10-16T11:49:34Z", "perimetre": "G", "methode": "R", "statut": "A"}, {"id": 81, "ref": 9818, "dateCreation": "2022-06-19T08:45:41Z", "date": "2021-08-15T12:25:49Z", "perimetre": "Z", "methode": "C", "statut": "A"}, {"id": 82, "ref": 9598, "dateCreation": "2022-03-12T03:42:18Z", "date": "2022-01-30T18:47:10Z", "perimetre": "G", "methode": "L", "statut": "VT"}, {"id": 83, "ref": 9815, "dateCreation": "2021-10-19T01:20:49Z", "date": "2022-06-23T17:46:39Z", "perimetre": "M", "methode": "L", "statut": "VT"}, {"id": 84, "ref": 4456, "dateCreation": "2022-03-03T07:18:19Z", "date": "2022-06-26T02:55:37Z", "perimetre": "Z", "methode": "CB", "statut": "VP"}, {"id": 85, "ref": 7324, "dateCreation": "2022-01-24T05:47:05Z", "date": "2021-08-28T12:26:22Z", "perimetre": "Z", "methode": "L", "statut": "B"}, {"id": 86, "ref": 6519, "dateCreation": "2021-12-10T01:10:18Z", "date": "2022-04-05T23:35:14Z", "perimetre": "G", "methode": "R", "statut": "VT"}, {"id": 87, "ref": 6398, "dateCreation": "2022-03-30T11:32:52Z", "date": "2021-11-27T08:28:13Z", "perimetre": "G", "methode": "C", "statut": "B"}, {"id": 88, "ref": 2512, "dateCreation": "2021-10-26T17:17:24Z", "date": "2022-01-12T17:37:52Z", "perimetre": "M", "methode": "L", "statut": "B"}, {"id": 89, "ref": 2199, "dateCreation": "2021-09-23T00:48:59Z", "date": "2022-04-27T13:53:22Z", "perimetre": "M", "methode": "S", "statut": "VT"}, {"id": 90, "ref": 3115, "dateCreation": "2022-02-27T08:36:14Z", "date": "2022-07-11T01:24:17Z", "perimetre": "G", "methode": "FT", "statut": "A"}, {"id": 91, "ref": 1506, "dateCreation": "2021-12-05T02:21:04Z", "date": "2022-03-11T13:36:39Z", "perimetre": "M", "methode": "FT", "statut": "VP"}, {"id": 92, "ref": 2427, "dateCreation": "2022-01-19T05:22:53Z", "date": "2021-09-07T11:31:26Z", "perimetre": "Z", "methode": "R", "statut": "VP"}, {"id": 93, "ref": 7153, "dateCreation": "2021-10-29T20:20:39Z", "date": "2021-09-27T20:39:13Z", "perimetre": "G", "methode": "R", "statut": "VT"}, {"id": 94, "ref": 3280, "dateCreation": "2022-05-16T16:58:44Z", "date": "2021-11-12T23:16:00Z", "perimetre": "M", "methode": "CB", "statut": "B"}, {"id": 95, "ref": 6882, "dateCreation": "2021-12-19T18:18:55Z", "date": "2022-01-14T12:29:05Z", "perimetre": "M", "methode": "L", "statut": "B"}, {"id": 96, "ref": 7189, "dateCreation": "2021-07-22T00:07:34Z", "date": "2021-08-07T14:50:24Z", "perimetre": "M", "methode": "FT", "statut": "VT"}, {"id": 97, "ref": 3714, "dateCreation": "2021-10-12T20:17:26Z", "date": "2021-07-23T16:41:13Z", "perimetre": "Z", "methode": "CB", "statut": "VP"}, {"id": 98, "ref": 8515, "dateCreation": "2022-05-22T01:05:47Z", "date": "2022-05-02T05:13:49Z", "perimetre": "Z", "methode": "CB", "statut": "B"}, {"id": 99, "ref": 7224, "dateCreation": "2022-06-19T15:15:24Z", "date": "2021-09-20T13:54:54Z", "perimetre": "G", "methode": "L", "statut": "B"}, {"id": 100, "ref": 2091, "dateCreation": "2021-07-28T12:15:22Z", "date": "2022-05-24T18:23:51Z", "perimetre": "Z", "methode": "CB", "statut": "VP"}, {"id": 101, "ref": 1694, "dateCreation": "2021-07-20T09:28:08Z", "date": "2022-06-21T16:27:53Z", "perimetre": "Z", "methode": "S", "statut": "B"}, {"id": 102, "ref": 9763, "dateCreation": "2021-11-17T22:38:17Z", "date": "2021-11-08T08:52:24Z", "perimetre": "M", "methode": "CB", "statut": "A"}, {"id": 103, "ref": 4123, "dateCreation": "2021-09-13T18:27:41Z", "date": "2021-08-17T17:07:49Z", "perimetre": "M", "methode": "CB", "statut": "A"}, {"id": 104, "ref": 6099, "dateCreation": "2022-03-09T04:11:29Z", "date": "2021-11-23T01:31:15Z", "perimetre": "Z", "methode": "F", "statut": "VP"}, {"id": 105, "ref": 8675, "dateCreation": "2022-06-30T01:03:45Z", "date": "2021-12-16T22:02:02Z", "perimetre": "M", "methode": "FT", "statut": "B"}, {"id": 106, "ref": 6204, "dateCreation": "2021-11-22T05:04:52Z", "date": "2022-05-08T06:48:32Z", "perimetre": "G", "methode": "S", "statut": "VP"}, {"id": 107, "ref": 9391, "dateCreation": "2022-02-20T14:02:15Z", "date": "2021-11-01T04:05:46Z", "perimetre": "G", "methode": "C", "statut": "B"}, {"id": 108, "ref": 3131, "dateCreation": "2021-11-16T11:41:44Z", "date": "2021-08-02T04:35:14Z", "perimetre": "M", "methode": "F", "statut": "A"}, {"id": 109, "ref": 6414, "dateCreation": "2021-08-19T14:08:13Z", "date": "2022-02-22T01:01:29Z", "perimetre": "G", "methode": "L", "statut": "A"}, {"id": 110, "ref": 2227, "dateCreation": "2021-07-18T19:06:39Z", "date": "2021-07-24T19:24:49Z", "perimetre": "M", "methode": "R", "statut": "VT"}, {"id": 111, "ref": 5199, "dateCreation": "2022-06-13T22:18:17Z", "date": "2021-11-09T18:35:41Z", "perimetre": "G", "methode": "CB", "statut": "VP"}, {"id": 112, "ref": 4527, "dateCreation": "2021-10-18T13:15:47Z", "date": "2021-11-04T12:05:04Z", "perimetre": "G", "methode": "CB", "statut": "VP"}, {"id": 113, "ref": 8653, "dateCreation": "2022-05-15T20:33:23Z", "date": "2022-01-21T19:48:09Z", "perimetre": "M", "methode": "F", "statut": "A"}, {"id": 114, "ref": 2049, "dateCreation": "2022-05-09T02:58:54Z", "date": "2022-05-20T17:58:46Z", "perimetre": "G", "methode": "FT", "statut": "VP"}, {"id": 115, "ref": 2853, "dateCreation": "2022-03-17T06:55:10Z", "date": "2021-12-10T09:53:21Z", "perimetre": "M", "methode": "FT", "statut": "A"}, {"id": 116, "ref": 9822, "dateCreation": "2021-12-09T14:43:29Z", "date": "2021-08-18T01:41:55Z", "perimetre": "M", "methode": "F", "statut": "A"}, {"id": 117, "ref": 5370, "dateCreation": "2021-09-09T10:00:55Z", "date": "2021-09-23T00:58:20Z", "perimetre": "Z", "methode": "FT", "statut": "VP"}, {"id": 118, "ref": 1243, "dateCreation": "2022-06-15T22:29:28Z", "date": "2022-05-10T13:42:01Z", "perimetre": "Z", "methode": "S", "statut": "VT"}, {"id": 119, "ref": 5473, "dateCreation": "2021-10-19T09:02:11Z", "date": "2022-07-14T00:31:06Z", "perimetre": "G", "methode": "L", "statut": "B"}, {"id": 120, "ref": 4965, "dateCreation": "2022-07-01T21:03:14Z", "date": "2021-10-10T21:05:41Z", "perimetre": "G", "methode": "F", "statut": "B"}, {"id": 121, "ref": 4044, "dateCreation": "2022-05-20T20:00:37Z", "date": "2022-06-09T06:52:05Z", "perimetre": "G", "methode": "C", "statut": "VP"}, {"id": 122, "ref": 6146, "dateCreation": "2021-09-10T16:46:53Z", "date": "2022-01-13T07:02:55Z", "perimetre": "M", "methode": "R", "statut": "B"}, {"id": 123, "ref": 7083, "dateCreation": "2021-11-06T10:45:48Z", "date": "2022-02-09T18:13:55Z", "perimetre": "M", "methode": "CB", "statut": "VP"}, {"id": 124, "ref": 3805, "dateCreation": "2021-08-29T10:23:38Z", "date": "2021-08-09T14:55:13Z", "perimetre": "M", "methode": "C", "statut": "VP"}, {"id": 125, "ref": 4845, "dateCreation": "2022-03-31T11:38:54Z", "date": "2022-06-22T14:40:25Z", "perimetre": "G", "methode": "S", "statut": "VT"}, {"id": 126, "ref": 4925, "dateCreation": "2021-08-07T05:30:04Z", "date": "2021-08-18T14:32:38Z", "perimetre": "M", "methode": "R", "statut": "A"}, {"id": 127, "ref": 9099, "dateCreation": "2021-08-07T06:36:27Z", "date": "2022-05-13T07:44:05Z", "perimetre": "G", "methode": "R", "statut": "VT"}, {"id": 128, "ref": 3002, "dateCreation": "2022-06-04T01:30:16Z", "date": "2022-05-23T01:51:40Z", "perimetre": "Z", "methode": "L", "statut": "VP"}, {"id": 129, "ref": 1530, "dateCreation": "2022-03-13T17:41:41Z", "date": "2021-07-31T22:30:01Z", "perimetre": "Z", "methode": "R", "statut": "VP"}, {"id": 130, "ref": 3792, "dateCreation": "2021-09-28T01:29:09Z", "date": "2021-07-28T18:22:37Z", "perimetre": "G", "methode": "FT", "statut": "A"}, {"id": 131, "ref": 5535, "dateCreation": "2022-06-13T10:26:39Z", "date": "2021-12-23T05:16:31Z", "perimetre": "Z", "methode": "CB", "statut": "VT"}, {"id": 132, "ref": 6082, "dateCreation": "2021-07-18T12:54:56Z", "date": "2021-07-26T14:57:48Z", "perimetre": "G", "methode": "R", "statut": "B"}, {"id": 133, "ref": 9020, "dateCreation": "2022-06-01T08:05:53Z", "date": "2021-09-23T01:59:19Z", "perimetre": "Z", "methode": "C", "statut": "VP"}, {"id": 134, "ref": 6530, "dateCreation": "2021-10-23T15:24:17Z", "date": "2022-02-02T13:07:22Z", "perimetre": "M", "methode": "S", "statut": "VP"}, {"id": 135, "ref": 3569, "dateCreation": "2022-07-03T12:02:24Z", "date": "2022-05-16T06:41:11Z", "perimetre": "G", "methode": "S", "statut": "A"}, {"id": 136, "ref": 2495, "dateCreation": "2021-12-29T01:38:40Z", "date": "2021-10-18T22:49:21Z", "perimetre": "Z", "methode": "S", "statut": "VP"}, {"id": 137, "ref": 8793, "dateCreation": "2022-07-01T00:29:46Z", "date": "2022-05-20T13:25:59Z", "perimetre": "Z", "methode": "S", "statut": "VT"}, {"id": 138, "ref": 9925, "dateCreation": "2021-11-30T17:58:08Z", "date": "2022-01-20T10:56:04Z", "perimetre": "Z", "methode": "S", "statut": "VP"}, {"id": 139, "ref": 6950, "dateCreation": "2022-06-15T14:38:39Z", "date": "2022-04-26T01:17:25Z", "perimetre": "Z", "methode": "C", "statut": "VP"}, {"id": 140, "ref": 9675, "dateCreation": "2021-12-19T05:41:42Z", "date": "2022-06-15T09:58:41Z", "perimetre": "M", "methode": "F", "statut": "A"}, {"id": 141, "ref": 8028, "dateCreation": "2021-09-11T10:25:27Z", "date": "2021-11-19T08:38:47Z", "perimetre": "G", "methode": "C", "statut": "VP"}, {"id": 142, "ref": 3546, "dateCreation": "2022-05-01T00:17:41Z", "date": "2022-06-19T07:09:21Z", "perimetre": "Z", "methode": "S", "statut": "A"}, {"id": 143, "ref": 5783, "dateCreation": "2022-06-16T15:30:47Z", "date": "2021-10-12T22:50:59Z", "perimetre": "Z", "methode": "L", "statut": "VT"}, {"id": 144, "ref": 6955, "dateCreation": "2022-04-03T21:14:06Z", "date": "2022-04-20T15:27:26Z", "perimetre": "M", "methode": "C", "statut": "VP"}, {"id": 145, "ref": 9113, "dateCreation": "2022-01-03T19:57:22Z", "date": "2022-02-20T02:28:52Z", "perimetre": "Z", "methode": "L", "statut": "VT"}, {"id": 146, "ref": 6527, "dateCreation": "2022-05-21T14:34:09Z", "date": "2021-08-20T22:06:37Z", "perimetre": "Z", "methode": "FT", "statut": "VP"}, {"id": 147, "ref": 1436, "dateCreation": "2021-09-21T01:25:13Z", "date": "2021-09-22T02:48:29Z", "perimetre": "G", "methode": "C", "statut": "B"}, {"id": 148, "ref": 3333, "dateCreation": "2021-12-09T02:44:19Z", "date": "2022-06-28T03:03:07Z", "perimetre": "Z", "methode": "R", "statut": "VT"}, {"id": 149, "ref": 7830, "dateCreation": "2021-10-18T13:10:00Z", "date": "2021-10-01T19:18:36Z", "perimetre": "Z", "methode": "L", "statut": "VT"}, {"id": 150, "ref": 5764, "dateCreation": "2022-04-19T06:25:55Z", "date": "2022-04-09T19:38:35Z", "perimetre": "G", "methode": "F", "statut": "A"}], "formes": [{"id": 1, "libelle": "AEROSOLS USAGE INTERNE"}, {"id": 2, "libelle": "AMPOULES BUVABLES"}, {"id": 3, "libelle": "PRODUITS DIETETIQUES"}, {"id": 4, "libelle": "PRODUITS  COSMETIQUES"}, {"id": 5, "libelle": "VACCINS"}, {"id": 6, "libelle": "SERUMS"}], "rayons": [{"id": 1, "libelle": "AEROSO"}, {"id": 2, "libelle": "CP R1"}, {"id": 3, "libelle": "FRIGO"}, {"id": 4, "libelle": "VETRIN"}], "categories": [{"id": 1, "libelle": "SPECIALITE A 0 %"}, {"id": 2, "libelle": "SPECIALITES A 20%"}, {"id": 3, "libelle": "PARAPHARMACIE A 20%"}, {"id": 4, "libelle": "DIETETIQUE"}, {"id": 5, "libelle": "AUTRES"}], "labos": [{"id": 1, "libelle": "GHIPHAR"}, {"id": 2, "libelle": "SYNTHEMEDIC"}, {"id": 3, "libelle": "SOTHEMA"}, {"id": 4, "libelle": "BOTTU"}, {"id": 5, "libelle": "PHARMA 5"}], "detailInventaireListes": [{"id": 1, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 4, "labo": 5, "inventaire": 64}, {"id": 2, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 5, "labo": 2, "inventaire": 117}, {"id": 3, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 2, "labo": 4, "inventaire": 16}, {"id": 4, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 2, "labo": 1, "inventaire": 65}, {"id": 5, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 1, "labo": 5, "inventaire": 72}, {"id": 6, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 2, "categorie": 4, "labo": 3, "inventaire": 96}, {"id": 7, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 2, "categorie": 5, "labo": 3, "inventaire": 47}, {"id": 8, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 2, "categorie": 1, "labo": 5, "inventaire": 114}, {"id": 9, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 5, "labo": 1, "inventaire": 88}, {"id": 10, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 2, "categorie": 1, "labo": 1, "inventaire": 72}, {"id": 11, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 1, "labo": 5, "inventaire": 64}, {"id": 12, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 3, "labo": 3, "inventaire": 113}, {"id": 13, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 1, "labo": 3, "inventaire": 129}, {"id": 14, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 4, "labo": 4, "inventaire": 7}, {"id": 15, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 2, "labo": 2, "inventaire": 60}, {"id": 16, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 4, "labo": 5, "inventaire": 78}, {"id": 17, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 2, "labo": 5, "inventaire": 22}, {"id": 18, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 1, "labo": 3, "inventaire": 127}, {"id": 19, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 2, "labo": 3, "inventaire": 9}, {"id": 20, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 4, "labo": 5, "inventaire": 118}, {"id": 21, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 3, "labo": 5, "inventaire": 35}, {"id": 22, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 4, "labo": 3, "inventaire": 146}, {"id": 23, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 4, "labo": 1, "inventaire": 25}, {"id": 24, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 2, "labo": 1, "inventaire": 10}, {"id": 25, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 4, "labo": 5, "inventaire": 132}, {"id": 26, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 3, "labo": 5, "inventaire": 125}, {"id": 27, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 5, "labo": 4, "inventaire": 101}, {"id": 28, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 1, "labo": 5, "inventaire": 52}, {"id": 29, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 3, "labo": 3, "inventaire": 27}, {"id": 30, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 3, "labo": 3, "inventaire": 42}, {"id": 31, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 2, "categorie": 1, "labo": 1, "inventaire": 22}, {"id": 32, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 2, "categorie": 5, "labo": 2, "inventaire": 144}, {"id": 33, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 2, "labo": 2, "inventaire": 5}, {"id": 34, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 3, "labo": 2, "inventaire": 95}, {"id": 35, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 4, "labo": 2, "inventaire": 63}, {"id": 36, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 1, "labo": 3, "inventaire": 128}, {"id": 37, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 2, "labo": 2, "inventaire": 4}, {"id": 38, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 1, "labo": 1, "inventaire": 98}, {"id": 39, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 2, "labo": 1, "inventaire": 6}, {"id": 40, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 4, "labo": 1, "inventaire": 134}, {"id": 41, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 2, "labo": 1, "inventaire": 113}, {"id": 42, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 2, "labo": 5, "inventaire": 22}, {"id": 43, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 5, "labo": 4, "inventaire": 143}, {"id": 44, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 5, "labo": 2, "inventaire": 89}, {"id": 45, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 2, "labo": 4, "inventaire": 54}, {"id": 46, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 2, "categorie": 5, "labo": 5, "inventaire": 63}, {"id": 47, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 5, "labo": 4, "inventaire": 29}, {"id": 48, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 2, "labo": 1, "inventaire": 59}, {"id": 49, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 2, "labo": 2, "inventaire": 93}, {"id": 50, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 2, "labo": 4, "inventaire": 103}, {"id": 51, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 4, "labo": 4, "inventaire": 9}, {"id": 52, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 1, "labo": 2, "inventaire": 38}, {"id": 53, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 3, "labo": 5, "inventaire": 111}, {"id": 54, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 5, "labo": 4, "inventaire": 13}, {"id": 55, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 2, "labo": 3, "inventaire": 137}, {"id": 56, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 3, "categorie": 3, "labo": 1, "inventaire": 138}, {"id": 57, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 2, "categorie": 1, "labo": 3, "inventaire": 27}, {"id": 58, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 4, "labo": 5, "inventaire": 89}, {"id": 59, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 1, "categorie": 5, "labo": 2, "inventaire": 149}, {"id": 60, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 4, "labo": 3, "inventaire": 72}, {"id": 61, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 2, "labo": 3, "inventaire": 136}, {"id": 62, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 1, "labo": 4, "inventaire": 134}, {"id": 63, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 3, "labo": 1, "inventaire": 112}, {"id": 64, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 2, "labo": 2, "inventaire": 18}, {"id": 65, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 5, "labo": 4, "inventaire": 96}, {"id": 66, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 5, "labo": 3, "inventaire": 83}, {"id": 67, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 1, "labo": 5, "inventaire": 127}, {"id": 68, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 5, "labo": 4, "inventaire": 40}, {"id": 69, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 2, "categorie": 2, "labo": 2, "inventaire": 11}, {"id": 70, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 3, "categorie": 5, "labo": 5, "inventaire": 20}, {"id": 71, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 4, "labo": 1, "inventaire": 105}, {"id": 72, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 1, "labo": 2, "inventaire": 136}, {"id": 73, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 1, "labo": 1, "inventaire": 96}, {"id": 74, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 4, "labo": 4, "inventaire": 129}, {"id": 75, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 2, "labo": 1, "inventaire": 56}, {"id": 76, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 2, "categorie": 5, "labo": 4, "inventaire": 46}, {"id": 77, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 5, "labo": 4, "inventaire": 57}, {"id": 78, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 1, "categorie": 2, "labo": 5, "inventaire": 125}, {"id": 79, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 1, "labo": 2, "inventaire": 137}, {"id": 80, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 2, "labo": 4, "inventaire": 33}, {"id": 81, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 3, "labo": 5, "inventaire": 33}, {"id": 82, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 3, "labo": 5, "inventaire": 100}, {"id": 83, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 2, "categorie": 2, "labo": 4, "inventaire": 42}, {"id": 84, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 1, "labo": 4, "inventaire": 65}, {"id": 85, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 5, "labo": 4, "inventaire": 149}, {"id": 86, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 3, "labo": 1, "inventaire": 9}, {"id": 87, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 5, "labo": 2, "inventaire": 64}, {"id": 88, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 2, "labo": 5, "inventaire": 67}, {"id": 89, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 5, "labo": 3, "inventaire": 11}, {"id": 90, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 2, "labo": 2, "inventaire": 74}, {"id": 91, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 3, "labo": 1, "inventaire": 114}, {"id": 92, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 5, "labo": 1, "inventaire": 26}, {"id": 93, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 2, "labo": 2, "inventaire": 118}, {"id": 94, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 1, "labo": 5, "inventaire": 76}, {"id": 95, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 2, "labo": 2, "inventaire": 69}, {"id": 96, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 2, "labo": 2, "inventaire": 113}, {"id": 97, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 5, "labo": 4, "inventaire": 21}, {"id": 98, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 4, "labo": 4, "inventaire": 96}, {"id": 99, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 4, "labo": 3, "inventaire": 129}, {"id": 100, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 2, "labo": 3, "inventaire": 101}, {"id": 101, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 1, "labo": 2, "inventaire": 140}, {"id": 102, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 5, "labo": 5, "inventaire": 93}, {"id": 103, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 1, "labo": 5, "inventaire": 16}, {"id": 104, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 5, "labo": 3, "inventaire": 32}, {"id": 105, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 1, "labo": 4, "inventaire": 36}, {"id": 106, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 3, "categorie": 1, "labo": 4, "inventaire": 131}, {"id": 107, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 3, "labo": 2, "inventaire": 42}, {"id": 108, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 3, "labo": 5, "inventaire": 5}, {"id": 109, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 2, "categorie": 4, "labo": 2, "inventaire": 5}, {"id": 110, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 5, "labo": 2, "inventaire": 129}, {"id": 111, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 4, "labo": 4, "inventaire": 68}, {"id": 112, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 2, "labo": 1, "inventaire": 93}, {"id": 113, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 4, "labo": 2, "inventaire": 107}, {"id": 114, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 3, "labo": 3, "inventaire": 54}, {"id": 115, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 4, "labo": 4, "inventaire": 64}, {"id": 116, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 3, "labo": 4, "inventaire": 150}, {"id": 117, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 5, "labo": 5, "inventaire": 34}, {"id": 118, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 5, "labo": 3, "inventaire": 68}, {"id": 119, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 1, "labo": 4, "inventaire": 136}, {"id": 120, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 5, "labo": 1, "inventaire": 4}, {"id": 121, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 5, "labo": 2, "inventaire": 39}, {"id": 122, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 1, "labo": 1, "inventaire": 87}, {"id": 123, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 5, "labo": 3, "inventaire": 89}, {"id": 124, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 1, "labo": 4, "inventaire": 53}, {"id": 125, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 4, "labo": 4, "inventaire": 64}, {"id": 126, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 4, "labo": 1, "inventaire": 130}, {"id": 127, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 5, "labo": 5, "inventaire": 113}, {"id": 128, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 3, "labo": 3, "inventaire": 144}, {"id": 129, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 5, "labo": 1, "inventaire": 63}, {"id": 130, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 2, "categorie": 5, "labo": 3, "inventaire": 8}, {"id": 131, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 2, "categorie": 4, "labo": 5, "inventaire": 17}, {"id": 132, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 5, "labo": 2, "inventaire": 42}, {"id": 133, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 5, "labo": 5, "inventaire": 33}, {"id": 134, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 5, "labo": 4, "inventaire": 51}, {"id": 135, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 5, "labo": 2, "inventaire": 10}, {"id": 136, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 4, "labo": 4, "inventaire": 63}, {"id": 137, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 4, "labo": 5, "inventaire": 27}, {"id": 138, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 2, "labo": 4, "inventaire": 77}, {"id": 139, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 3, "labo": 5, "inventaire": 82}, {"id": 140, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 3, "labo": 2, "inventaire": 17}, {"id": 141, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 1, "labo": 4, "inventaire": 96}, {"id": 142, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 4, "labo": 4, "inventaire": 105}, {"id": 143, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 2, "labo": 4, "inventaire": 129}, {"id": 144, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 3, "labo": 5, "inventaire": 111}, {"id": 145, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 3, "labo": 1, "inventaire": 134}, {"id": 146, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 3, "labo": 5, "inventaire": 120}, {"id": 147, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 1, "labo": 1, "inventaire": 68}, {"id": 148, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 1, "labo": 4, "inventaire": 23}, {"id": 149, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 3, "labo": 1, "inventaire": 33}, {"id": 150, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 2, "labo": 5, "inventaire": 44}, {"id": 151, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 1, "categorie": 3, "labo": 5, "inventaire": 101}, {"id": 152, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 1, "labo": 4, "inventaire": 111}, {"id": 153, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 5, "labo": 3, "inventaire": 33}, {"id": 154, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 2, "labo": 2, "inventaire": 101}, {"id": 155, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 2, "categorie": 5, "labo": 3, "inventaire": 74}, {"id": 156, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 3, "labo": 5, "inventaire": 134}, {"id": 157, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 3, "labo": 3, "inventaire": 95}, {"id": 158, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 2, "labo": 3, "inventaire": 146}, {"id": 159, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 2, "categorie": 4, "labo": 2, "inventaire": 86}, {"id": 160, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 4, "labo": 2, "inventaire": 21}, {"id": 161, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 3, "labo": 4, "inventaire": 28}, {"id": 162, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 1, "labo": 5, "inventaire": 88}, {"id": 163, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 4, "labo": 3, "inventaire": 62}, {"id": 164, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 1, "labo": 1, "inventaire": 148}, {"id": 165, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 5, "labo": 5, "inventaire": 100}, {"id": 166, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 5, "labo": 1, "inventaire": 72}, {"id": 167, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 1, "categorie": 5, "labo": 2, "inventaire": 100}, {"id": 168, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 2, "labo": 3, "inventaire": 17}, {"id": 169, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 1, "categorie": 2, "labo": 2, "inventaire": 66}, {"id": 170, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 2, "labo": 3, "inventaire": 6}, {"id": 171, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 2, "labo": 1, "inventaire": 25}, {"id": 172, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 5, "labo": 1, "inventaire": 2}, {"id": 173, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 2, "labo": 5, "inventaire": 62}, {"id": 174, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 3, "labo": 3, "inventaire": 78}, {"id": 175, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 4, "labo": 1, "inventaire": 32}, {"id": 176, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 3, "categorie": 1, "labo": 5, "inventaire": 99}, {"id": 177, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 4, "categorie": 4, "labo": 4, "inventaire": 61}, {"id": 178, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 5, "labo": 4, "inventaire": 132}, {"id": 179, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 1, "categorie": 1, "labo": 2, "inventaire": 106}, {"id": 180, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 1, "categorie": 2, "labo": 5, "inventaire": 89}, {"id": 181, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 2, "labo": 2, "inventaire": 53}, {"id": 182, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 3, "categorie": 2, "labo": 2, "inventaire": 50}, {"id": 183, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 3, "labo": 2, "inventaire": 76}, {"id": 184, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 3, "categorie": 4, "labo": 4, "inventaire": 24}, {"id": 185, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 1, "categorie": 4, "labo": 4, "inventaire": 104}, {"id": 186, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 4, "labo": 1, "inventaire": 40}, {"id": 187, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 2, "categorie": 3, "labo": 1, "inventaire": 130}, {"id": 188, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 2, "labo": 5, "inventaire": 39}, {"id": 189, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 3, "categorie": 4, "labo": 2, "inventaire": 89}, {"id": 190, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 1, "categorie": 5, "labo": 2, "inventaire": 73}, {"id": 191, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 2, "rayon": 4, "categorie": 5, "labo": 5, "inventaire": 112}, {"id": 192, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 3, "labo": 3, "inventaire": 4}, {"id": 193, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 3, "categorie": 2, "labo": 1, "inventaire": 87}, {"id": 194, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 4, "rayon": 4, "categorie": 3, "labo": 4, "inventaire": 56}, {"id": 195, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 6, "rayon": 4, "categorie": 1, "labo": 5, "inventaire": 54}, {"id": 196, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 1, "categorie": 3, "labo": 4, "inventaire": 102}, {"id": 197, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 1, "rayon": 4, "categorie": 5, "labo": 4, "inventaire": 67}, {"id": 198, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 4, "labo": 1, "inventaire": 135}, {"id": 199, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 3, "rayon": 2, "categorie": 3, "labo": 4, "inventaire": 97}, {"id": 200, "segmentStart": "A", "segmentEnd": "Z", "inventaireProduits": [1, 2, 3], "forme": 5, "rayon": 4, "categorie": 4, "labo": 1, "inventaire": 12}], "inventaireProduits": [{"id": 1, "produit": 38, "forme": 4, "rayon": 4, "labo": 4, "ft": 4, "categorie": 2, "codeBar": "68327-011", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 2, "produit": 24, "forme": 1, "rayon": 1, "labo": 3, "ft": 2, "categorie": 3, "codeBar": "0591-2466", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 3, "produit": 44, "forme": 1, "rayon": 2, "labo": 2, "ft": 4, "categorie": 3, "codeBar": "41250-034", "qte": 21, "prix": 68, "datePeremption": "2022-05-23T13:16:24Z", "seuilMin": 5, "seuilMax": 12}, {"id": 4, "produit": 34, "forme": 4, "rayon": 3, "labo": 3, "ft": 1, "categorie": 2, "codeBar": "55111-534", "qte": 35, "prix": 47, "datePeremption": "2021-10-05T06:39:10Z", "seuilMin": 4, "seuilMax": 18}, {"id": 5, "produit": 7, "forme": 4, "rayon": 2, "labo": 1, "ft": 3, "categorie": 2, "codeBar": "65643-416", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 6, "produit": 14, "forme": 3, "rayon": 1, "labo": 2, "ft": 2, "categorie": 4, "codeBar": "15338-170", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 7, "produit": 45, "forme": 1, "rayon": 2, "labo": 3, "ft": 3, "categorie": 2, "codeBar": "63029-505", "qte": 15, "prix": 41, "datePeremption": "2021-12-04T23:48:21Z", "seuilMin": 7, "seuilMax": 16}, {"id": 8, "produit": 38, "forme": 4, "rayon": 3, "labo": 4, "ft": 2, "categorie": 4, "codeBar": "42291-209", "qte": 26, "prix": 49, "datePeremption": "2021-08-30T05:01:33Z", "seuilMin": 7, "seuilMax": 13}, {"id": 9, "produit": 7, "forme": 1, "rayon": 4, "labo": 4, "ft": 3, "categorie": 3, "codeBar": "0781-1048", "qte": 15, "prix": 55, "datePeremption": "2022-05-25T17:09:32Z", "seuilMin": 3, "seuilMax": 20}, {"id": 10, "produit": 26, "forme": 2, "rayon": 3, "labo": 1, "ft": 1, "categorie": 4, "codeBar": "43478-241", "qte": 32, "prix": 28, "datePeremption": "2022-07-13T21:22:57Z", "seuilMin": 6, "seuilMax": 19}, {"id": 11, "produit": 25, "forme": 1, "rayon": 4, "labo": 2, "ft": 1, "categorie": 2, "codeBar": "53499-2872", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 12, "produit": 12, "forme": 1, "rayon": 2, "labo": 3, "ft": 3, "categorie": 3, "codeBar": "67046-461", "qte": 34, "prix": 10, "datePeremption": "2021-08-05T07:09:59Z", "seuilMin": 3, "seuilMax": 11}, {"id": 13, "produit": 21, "forme": 2, "rayon": 2, "labo": 2, "ft": 4, "categorie": 1, "codeBar": "30142-318", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 14, "produit": 50, "forme": 3, "rayon": 1, "labo": 4, "ft": 4, "categorie": 4, "codeBar": "60764-001", "qte": 1, "prix": 15, "datePeremption": "2022-06-28T06:36:56Z", "seuilMin": 9, "seuilMax": 14}, {"id": 15, "produit": 32, "forme": 4, "rayon": 1, "labo": 1, "ft": 3, "categorie": 2, "codeBar": "65785-160", "qte": 43, "prix": 39, "datePeremption": "2021-08-28T22:47:59Z", "seuilMin": 6, "seuilMax": 20}, {"id": 16, "produit": 4, "forme": 1, "rayon": 1, "labo": 2, "ft": 1, "categorie": 4, "codeBar": "54569-2996", "qte": 13, "prix": 12, "datePeremption": "2022-07-16T12:03:11Z", "seuilMin": 6, "seuilMax": 11}, {"id": 17, "produit": 5, "forme": 4, "rayon": 2, "labo": 3, "ft": 4, "categorie": 1, "codeBar": "0378-2721", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 18, "produit": 43, "forme": 2, "rayon": 3, "labo": 1, "ft": 4, "categorie": 4, "codeBar": "0093-0012", "qte": 36, "prix": 11, "datePeremption": "2021-12-31T04:36:17Z", "seuilMin": 2, "seuilMax": 10}, {"id": 19, "produit": 15, "forme": 2, "rayon": 3, "labo": 4, "ft": 2, "categorie": 3, "codeBar": "57344-003", "qte": 50, "prix": 22, "datePeremption": "2022-04-20T02:04:34Z", "seuilMin": 5, "seuilMax": 12}, {"id": 20, "produit": 48, "forme": 1, "rayon": 3, "labo": 4, "ft": 1, "categorie": 1, "codeBar": "36800-105", "qte": 50, "prix": 74, "datePeremption": "2022-01-30T20:42:00Z", "seuilMin": 3, "seuilMax": 10}, {"id": 21, "produit": 27, "forme": 4, "rayon": 3, "labo": 4, "ft": 3, "categorie": 1, "codeBar": "0832-0111", "qte": 17, "prix": 22, "datePeremption": "2021-08-21T21:04:08Z", "seuilMin": 2, "seuilMax": 15}, {"id": 22, "produit": 38, "forme": 4, "rayon": 4, "labo": 3, "ft": 2, "categorie": 1, "codeBar": "0517-4201", "qte": 41, "prix": 54, "datePeremption": "2022-05-26T00:07:54Z", "seuilMin": 6, "seuilMax": 14}, {"id": 23, "produit": 39, "forme": 3, "rayon": 1, "labo": 4, "ft": 2, "categorie": 3, "codeBar": "24236-777", "qte": 3, "prix": 59, "datePeremption": "2021-11-26T21:02:22Z", "seuilMin": 10, "seuilMax": 20}, {"id": 24, "produit": 49, "forme": 2, "rayon": 4, "labo": 2, "ft": 3, "categorie": 1, "codeBar": "10096-0149", "qte": 39, "prix": 34, "datePeremption": "2021-09-04T22:17:28Z", "seuilMin": 5, "seuilMax": 18}, {"id": 25, "produit": 35, "forme": 3, "rayon": 3, "labo": 4, "ft": 1, "categorie": 3, "codeBar": "0591-0343", "qte": 45, "prix": 40, "datePeremption": "2021-09-17T10:29:50Z", "seuilMin": 4, "seuilMax": 12}, {"id": 26, "produit": 18, "forme": 3, "rayon": 4, "labo": 1, "ft": 4, "categorie": 3, "codeBar": "53808-0849", "qte": 10, "prix": 58, "datePeremption": "2021-10-07T09:02:24Z", "seuilMin": 8, "seuilMax": 10}, {"id": 27, "produit": 43, "forme": 1, "rayon": 4, "labo": 1, "ft": 3, "categorie": 3, "codeBar": "37205-155", "qte": 23, "prix": 30, "datePeremption": "2022-02-20T12:50:20Z", "seuilMin": 4, "seuilMax": 14}, {"id": 28, "produit": 19, "forme": 1, "rayon": 4, "labo": 4, "ft": 1, "categorie": 3, "codeBar": "49884-028", "qte": 16, "prix": 20, "datePeremption": "2021-10-22T15:44:53Z", "seuilMin": 4, "seuilMax": 12}, {"id": 29, "produit": 28, "forme": 4, "rayon": 1, "labo": 2, "ft": 4, "categorie": 4, "codeBar": "11673-516", "qte": 46, "prix": 35, "datePeremption": "2021-12-24T12:11:48Z", "seuilMin": 10, "seuilMax": 18}, {"id": 30, "produit": 42, "forme": 3, "rayon": 1, "labo": 4, "ft": 3, "categorie": 3, "codeBar": "68788-9542", "qte": 30, "prix": 75, "datePeremption": "2021-07-25T21:58:31Z", "seuilMin": 9, "seuilMax": 10}, {"id": 31, "produit": 5, "forme": 4, "rayon": 2, "labo": 2, "ft": 4, "categorie": 2, "codeBar": "62756-754", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 32, "produit": 12, "forme": 2, "rayon": 4, "labo": 2, "ft": 1, "categorie": 1, "codeBar": "51991-168", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 33, "produit": 47, "forme": 4, "rayon": 1, "labo": 4, "ft": 1, "categorie": 2, "codeBar": "53808-0852", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 34, "produit": 10, "forme": 3, "rayon": 2, "labo": 2, "ft": 4, "categorie": 1, "codeBar": "55154-1409", "qte": 22, "prix": 21, "datePeremption": "2021-10-21T06:35:35Z", "seuilMin": 9, "seuilMax": 10}, {"id": 35, "produit": 37, "forme": 2, "rayon": 2, "labo": 2, "ft": 1, "categorie": 2, "codeBar": "0944-2655", "qte": 9, "prix": 27, "datePeremption": "2022-07-05T00:54:03Z", "seuilMin": 3, "seuilMax": 17}, {"id": 36, "produit": 46, "forme": 1, "rayon": 2, "labo": 1, "ft": 2, "categorie": 4, "codeBar": "0093-8943", "qte": 1, "prix": 79, "datePeremption": "2021-12-14T21:41:38Z", "seuilMin": 4, "seuilMax": 13}, {"id": 37, "produit": 43, "forme": 1, "rayon": 4, "labo": 4, "ft": 2, "categorie": 4, "codeBar": "22700-139", "qte": 34, "prix": 29, "datePeremption": "2021-11-11T09:01:12Z", "seuilMin": 1, "seuilMax": 11}, {"id": 38, "produit": 36, "forme": 4, "rayon": 2, "labo": 3, "ft": 2, "categorie": 2, "codeBar": "58118-9924", "qte": 49, "prix": 35, "datePeremption": "2022-05-22T16:46:33Z", "seuilMin": 9, "seuilMax": 17}, {"id": 39, "produit": 43, "forme": 1, "rayon": 1, "labo": 4, "ft": 3, "categorie": 1, "codeBar": "68084-392", "qte": 38, "prix": 21, "datePeremption": "2022-05-02T01:16:03Z", "seuilMin": 9, "seuilMax": 16}, {"id": 40, "produit": 46, "forme": 1, "rayon": 3, "labo": 4, "ft": 4, "categorie": 1, "codeBar": "21695-673", "qte": 11, "prix": 55, "datePeremption": "2022-02-19T20:16:56Z", "seuilMin": 8, "seuilMax": 15}, {"id": 41, "produit": 1, "forme": 2, "rayon": 2, "labo": 4, "ft": 3, "categorie": 4, "codeBar": "64117-264", "qte": 35, "prix": 67, "datePeremption": "2021-10-25T19:33:27Z", "seuilMin": 10, "seuilMax": 16}, {"id": 42, "produit": 30, "forme": 4, "rayon": 2, "labo": 3, "ft": 3, "categorie": 1, "codeBar": "49348-981", "qte": 26, "prix": 35, "datePeremption": "2021-10-08T22:43:49Z", "seuilMin": 6, "seuilMax": 12}, {"id": 43, "produit": 39, "forme": 3, "rayon": 3, "labo": 3, "ft": 4, "categorie": 2, "codeBar": "41167-0935", "qte": 24, "prix": 62, "datePeremption": "2022-04-12T06:11:15Z", "seuilMin": 8, "seuilMax": 18}, {"id": 44, "produit": 22, "forme": 4, "rayon": 3, "labo": 3, "ft": 1, "categorie": 1, "codeBar": "21695-337", "qte": 22, "prix": 68, "datePeremption": "2021-10-07T01:42:29Z", "seuilMin": 5, "seuilMax": 15}, {"id": 45, "produit": 8, "forme": 3, "rayon": 2, "labo": 1, "ft": 3, "categorie": 1, "codeBar": "0039-0060", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 46, "produit": 12, "forme": 2, "rayon": 2, "labo": 1, "ft": 2, "categorie": 2, "codeBar": "36987-1101", "qte": 18, "prix": 61, "datePeremption": "2021-08-25T15:02:16Z", "seuilMin": 9, "seuilMax": 16}, {"id": 47, "produit": 50, "forme": 2, "rayon": 1, "labo": 4, "ft": 3, "categorie": 1, "codeBar": "65044-1911", "qte": 50, "prix": 67, "datePeremption": "2021-08-17T04:57:32Z", "seuilMin": 10, "seuilMax": 11}, {"id": 48, "produit": 42, "forme": 1, "rayon": 3, "labo": 4, "ft": 4, "categorie": 2, "codeBar": "68151-1923", "qte": 12, "prix": 61, "datePeremption": "2022-01-12T07:16:01Z", "seuilMin": 2, "seuilMax": 19}, {"id": 49, "produit": 10, "forme": 2, "rayon": 3, "labo": 3, "ft": 3, "categorie": 4, "codeBar": "58468-0121", "qte": 37, "prix": 52, "datePeremption": "2022-04-30T21:16:40Z", "seuilMin": 9, "seuilMax": 19}, {"id": 50, "produit": 13, "forme": 1, "rayon": 4, "labo": 1, "ft": 4, "categorie": 1, "codeBar": "60760-512", "qte": 12, "prix": 47, "datePeremption": "2021-11-27T09:24:27Z", "seuilMin": 5, "seuilMax": 14}, {"id": 51, "produit": 2, "forme": 4, "rayon": 1, "labo": 2, "ft": 1, "categorie": 4, "codeBar": "53808-0772", "qte": 49, "prix": 65, "datePeremption": "2022-05-20T23:40:30Z", "seuilMin": 6, "seuilMax": 19}, {"id": 52, "produit": 39, "forme": 1, "rayon": 3, "labo": 2, "ft": 4, "categorie": 1, "codeBar": "43419-372", "qte": 26, "prix": 26, "datePeremption": "2022-04-14T23:00:44Z", "seuilMin": 7, "seuilMax": 14}, {"id": 53, "produit": 15, "forme": 1, "rayon": 2, "labo": 3, "ft": 4, "categorie": 4, "codeBar": "42507-685", "qte": 22, "prix": 80, "datePeremption": "2022-03-26T11:03:40Z", "seuilMin": 1, "seuilMax": 16}, {"id": 54, "produit": 49, "forme": 3, "rayon": 3, "labo": 2, "ft": 1, "categorie": 2, "codeBar": "36987-3107", "qte": 4, "prix": 59, "datePeremption": "2022-06-15T19:55:38Z", "seuilMin": 3, "seuilMax": 15}, {"id": 55, "produit": 34, "forme": 3, "rayon": 1, "labo": 4, "ft": 2, "categorie": 1, "codeBar": "62750-026", "qte": 28, "prix": 62, "datePeremption": "2021-10-17T04:32:52Z", "seuilMin": 7, "seuilMax": 15}, {"id": 56, "produit": 38, "forme": 3, "rayon": 3, "labo": 1, "ft": 1, "categorie": 2, "codeBar": "43063-447", "qte": 43, "prix": 53, "datePeremption": "2022-04-29T17:57:22Z", "seuilMin": 1, "seuilMax": 12}, {"id": 57, "produit": 33, "forme": 2, "rayon": 4, "labo": 2, "ft": 4, "categorie": 3, "codeBar": "52891-100", "qte": 37, "prix": 48, "datePeremption": "2022-02-14T10:00:55Z", "seuilMin": 9, "seuilMax": 11}, {"id": 58, "produit": 47, "forme": 1, "rayon": 4, "labo": 4, "ft": 2, "categorie": 4, "codeBar": "62037-524", "qte": 45, "prix": 44, "datePeremption": "2022-02-18T02:37:59Z", "seuilMin": 3, "seuilMax": 12}, {"id": 59, "produit": 47, "forme": 1, "rayon": 1, "labo": 3, "ft": 1, "categorie": 1, "codeBar": "68516-4601", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 60, "produit": 19, "forme": 4, "rayon": 4, "labo": 2, "ft": 1, "categorie": 1, "codeBar": "59779-971", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 61, "produit": 2, "forme": 4, "rayon": 2, "labo": 3, "ft": 1, "categorie": 2, "codeBar": "0143-3367", "qte": 13, "prix": 55, "datePeremption": "2021-11-08T07:46:43Z", "seuilMin": 3, "seuilMax": 16}, {"id": 62, "produit": 5, "forme": 3, "rayon": 1, "labo": 1, "ft": 1, "categorie": 2, "codeBar": "37808-882", "qte": 41, "prix": 26, "datePeremption": "2021-09-27T21:23:43Z", "seuilMin": 7, "seuilMax": 19}, {"id": 63, "produit": 41, "forme": 1, "rayon": 2, "labo": 2, "ft": 4, "categorie": 1, "codeBar": "24385-982", "qte": 15, "prix": 33, "datePeremption": "2022-01-28T22:48:35Z", "seuilMin": 8, "seuilMax": 14}, {"id": 64, "produit": 35, "forme": 3, "rayon": 4, "labo": 3, "ft": 1, "categorie": 4, "codeBar": "67046-115", "qte": 49, "prix": 35, "datePeremption": "2021-09-04T10:45:11Z", "seuilMin": 3, "seuilMax": 18}, {"id": 65, "produit": 43, "forme": 4, "rayon": 1, "labo": 4, "ft": 4, "categorie": 1, "codeBar": "63850-0021", "qte": 21, "prix": 42, "datePeremption": "2021-07-28T16:39:50Z", "seuilMin": 1, "seuilMax": 19}, {"id": 66, "produit": 29, "forme": 3, "rayon": 1, "labo": 2, "ft": 4, "categorie": 4, "codeBar": "33992-1210", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 67, "produit": 37, "forme": 3, "rayon": 1, "labo": 4, "ft": 1, "categorie": 3, "codeBar": "65862-210", "qte": 46, "prix": 32, "datePeremption": "2022-03-28T16:02:50Z", "seuilMin": 2, "seuilMax": 20}, {"id": 68, "produit": 37, "forme": 1, "rayon": 1, "labo": 1, "ft": 4, "categorie": 4, "codeBar": "41520-153", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 69, "produit": 7, "forme": 3, "rayon": 2, "labo": 3, "ft": 4, "categorie": 1, "codeBar": "58318-002", "qte": 4, "prix": 16, "datePeremption": "2021-12-02T21:12:02Z", "seuilMin": 6, "seuilMax": 16}, {"id": 70, "produit": 40, "forme": 3, "rayon": 2, "labo": 3, "ft": 3, "categorie": 1, "codeBar": "54569-4467", "qte": 25, "prix": 54, "datePeremption": "2022-02-15T19:50:08Z", "seuilMin": 4, "seuilMax": 12}, {"id": 71, "produit": 41, "forme": 2, "rayon": 2, "labo": 4, "ft": 3, "categorie": 1, "codeBar": "0363-0178", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 72, "produit": 33, "forme": 4, "rayon": 1, "labo": 1, "ft": 4, "categorie": 1, "codeBar": "0440-7559", "qte": 38, "prix": 57, "datePeremption": "2022-05-26T11:14:53Z", "seuilMin": 10, "seuilMax": 19}, {"id": 73, "produit": 2, "forme": 1, "rayon": 4, "labo": 1, "ft": 1, "categorie": 1, "codeBar": "42291-625", "qte": 24, "prix": 15, "datePeremption": "2022-03-28T11:23:21Z", "seuilMin": 3, "seuilMax": 17}, {"id": 74, "produit": 34, "forme": 4, "rayon": 3, "labo": 3, "ft": 3, "categorie": 2, "codeBar": "65044-6515", "qte": 42, "prix": 75, "datePeremption": "2022-04-15T21:56:01Z", "seuilMin": 6, "seuilMax": 20}, {"id": 75, "produit": 11, "forme": 1, "rayon": 3, "labo": 2, "ft": 1, "categorie": 1, "codeBar": "43742-0415", "qte": 6, "prix": 65, "datePeremption": "2022-04-18T11:29:26Z", "seuilMin": 6, "seuilMax": 14}, {"id": 76, "produit": 38, "forme": 1, "rayon": 3, "labo": 3, "ft": 3, "categorie": 2, "codeBar": "43063-200", "qte": 2, "prix": 59, "datePeremption": "2021-11-08T09:31:59Z", "seuilMin": 6, "seuilMax": 17}, {"id": 77, "produit": 29, "forme": 3, "rayon": 1, "labo": 2, "ft": 1, "categorie": 3, "codeBar": "42291-648", "qte": 13, "prix": 12, "datePeremption": "2021-08-01T02:53:30Z", "seuilMin": 10, "seuilMax": 15}, {"id": 78, "produit": 35, "forme": 1, "rayon": 4, "labo": 2, "ft": 2, "categorie": 3, "codeBar": "10237-814", "qte": 46, "prix": 30, "datePeremption": "2022-05-16T10:15:40Z", "seuilMin": 2, "seuilMax": 11}, {"id": 79, "produit": 36, "forme": 3, "rayon": 2, "labo": 1, "ft": 1, "categorie": 2, "codeBar": "55154-2366", "qte": 1, "prix": 64, "datePeremption": "2022-05-30T09:14:27Z", "seuilMin": 7, "seuilMax": 11}, {"id": 80, "produit": 35, "forme": 1, "rayon": 4, "labo": 1, "ft": 2, "categorie": 1, "codeBar": "64762-861", "qte": 34, "prix": 49, "datePeremption": "2022-05-19T15:04:23Z", "seuilMin": 10, "seuilMax": 10}, {"id": 81, "produit": 28, "forme": 4, "rayon": 4, "labo": 2, "ft": 1, "categorie": 4, "codeBar": "68968-3125", "qte": 45, "prix": 21, "datePeremption": "2022-05-02T14:31:43Z", "seuilMin": 3, "seuilMax": 17}, {"id": 82, "produit": 33, "forme": 2, "rayon": 3, "labo": 3, "ft": 4, "categorie": 1, "codeBar": "60429-201", "qte": 11, "prix": 44, "datePeremption": "2021-09-18T18:38:14Z", "seuilMin": 4, "seuilMax": 13}, {"id": 83, "produit": 46, "forme": 1, "rayon": 2, "labo": 3, "ft": 4, "categorie": 4, "codeBar": "43386-140", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 84, "produit": 2, "forme": 1, "rayon": 4, "labo": 2, "ft": 4, "categorie": 4, "codeBar": "64117-261", "qte": 48, "prix": 69, "datePeremption": "2022-03-21T22:15:18Z", "seuilMin": 3, "seuilMax": 14}, {"id": 85, "produit": 45, "forme": 3, "rayon": 4, "labo": 2, "ft": 2, "categorie": 1, "codeBar": "55289-492", "qte": 47, "prix": 57, "datePeremption": "2022-03-12T14:37:54Z", "seuilMin": 8, "seuilMax": 12}, {"id": 86, "produit": 41, "forme": 2, "rayon": 4, "labo": 3, "ft": 1, "categorie": 3, "codeBar": "41167-0092", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 87, "produit": 47, "forme": 4, "rayon": 1, "labo": 4, "ft": 2, "categorie": 4, "codeBar": "37012-332", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 88, "produit": 21, "forme": 2, "rayon": 1, "labo": 4, "ft": 4, "categorie": 2, "codeBar": "0378-0148", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 89, "produit": 23, "forme": 4, "rayon": 4, "labo": 1, "ft": 3, "categorie": 2, "codeBar": "41163-604", "qte": 47, "prix": 76, "datePeremption": "2021-11-23T09:38:15Z", "seuilMin": 4, "seuilMax": 16}, {"id": 90, "produit": 35, "forme": 2, "rayon": 3, "labo": 3, "ft": 1, "categorie": 1, "codeBar": "55111-396", "qte": 47, "prix": 66, "datePeremption": "2022-01-03T16:34:34Z", "seuilMin": 7, "seuilMax": 16}, {"id": 91, "produit": 13, "forme": 1, "rayon": 3, "labo": 4, "ft": 3, "categorie": 3, "codeBar": "54569-5727", "qte": 15, "prix": 13, "datePeremption": "2021-08-12T12:19:37Z", "seuilMin": 9, "seuilMax": 14}, {"id": 92, "produit": 22, "forme": 1, "rayon": 2, "labo": 2, "ft": 3, "categorie": 2, "codeBar": "53329-231", "qte": 25, "prix": 24, "datePeremption": "2021-11-16T13:05:59Z", "seuilMin": 7, "seuilMax": 16}, {"id": 93, "produit": 32, "forme": 2, "rayon": 3, "labo": 3, "ft": 2, "categorie": 1, "codeBar": "44924-010", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 94, "produit": 40, "forme": 4, "rayon": 2, "labo": 3, "ft": 3, "categorie": 2, "codeBar": "49348-771", "qte": 44, "prix": 68, "datePeremption": "2021-07-20T01:22:03Z", "seuilMin": 9, "seuilMax": 18}, {"id": 95, "produit": 6, "forme": 3, "rayon": 2, "labo": 2, "ft": 4, "categorie": 4, "codeBar": "63045-001", "qte": 16, "prix": 14, "datePeremption": "2022-02-11T01:17:19Z", "seuilMin": 6, "seuilMax": 14}, {"id": 96, "produit": 19, "forme": 1, "rayon": 2, "labo": 4, "ft": 4, "categorie": 4, "codeBar": "16252-524", "qte": 4, "prix": 71, "datePeremption": "2021-09-30T04:49:10Z", "seuilMin": 9, "seuilMax": 14}, {"id": 97, "produit": 15, "forme": 3, "rayon": 4, "labo": 1, "ft": 3, "categorie": 3, "codeBar": "60760-334", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 98, "produit": 2, "forme": 3, "rayon": 1, "labo": 1, "ft": 3, "categorie": 2, "codeBar": "65862-391", "qte": 18, "prix": 77, "datePeremption": "2021-12-08T10:25:41Z", "seuilMin": 2, "seuilMax": 15}, {"id": 99, "produit": 32, "forme": 1, "rayon": 2, "labo": 2, "ft": 4, "categorie": 2, "codeBar": "10370-220", "qte": null, "prix": null, "datePeremption": null, "seuilMin": null, "seuilMax": null}, {"id": 100, "produit": 41, "forme": 1, "rayon": 3, "labo": 3, "ft": 3, "categorie": 4, "codeBar": "66336-857", "qte": 27, "prix": 79, "datePeremption": "2021-08-29T01:57:18Z", "seuilMin": 2, "seuilMax": 18}]}