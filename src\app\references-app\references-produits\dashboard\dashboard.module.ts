
import { CommonModule } from '@angular/common';
import { DashboardComponent } from './dashboard/dashboard.component';
import { SharedModule } from "src/app/shared/shared.module";
import { NgModule } from "@angular/core";
import { GridModule } from "@progress/kendo-angular-grid";

import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import {
  NgbTypeaheadModule,
  NgbModalModule,
  NgbNavModule,
  NgbDatepickerModule,
  NgbAccordionModule,
  NgbCollapseModule,
  NgbDropdownModule,
} from "@ng-bootstrap/ng-bootstrap";
import { HotkeyModule } from "angular2-hotkeys";
import { Select2Module } from "ng-select2-component";
import { NgxMaskModule } from "ngx-mask";
import { SharedFeaturesModule } from "src/app/shared-features/shared-features.module";
import { DashboardRoutingModule } from './dashboard-routing.module';
import { NgApexchartsModule } from 'ng-apexcharts';





@NgModule({
  declarations: [
    DashboardComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NgbTypeaheadModule,
    Select2Module,
    NgbModalModule,
    NgbNavModule,
    NgbDatepickerModule,
    NgbAccordionModule,
    NgbCollapseModule,
    NgxMaskModule.forChild(),
    SharedModule,
    GridModule,
    NgbDropdownModule,
    HotkeyModule,


    SharedFeaturesModule,
    DashboardRoutingModule,
    NgApexchartsModule
  ]
})
export class DashboardModule { }
