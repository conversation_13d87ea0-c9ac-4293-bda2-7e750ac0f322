import { ArreteCaisseParGroupe } from "./arreteCaisseParGroupe.model"
import { SyntheseArreteCaisseParPaiement } from "./syntheseArreteCaisseParPaiement.model"


export class ArreteCaisse {
    caissesParCategories: ArreteCaisseParGroupe[]
    caissesParTypes: ArreteCaisseParGroupe[]
    syntheseArreteCaisse: SyntheseArreteCaisseVente


    ///
    totalCat?: SyntheseArreteCaisseParPaiement
    totalType?: SyntheseArreteCaisseParPaiement

    depenses?: number
}






class SyntheseArreteCaisseVente {
    totalMontantRemiseVente: number
    totalMontantRemisesReglement: number

    totalMontantTpaPartClient: number
    totalMontantTpaPartOrganisme: number
    totalMontantTpaReglements: number


    totalMontantTva: number


    totalMontantVenteBrutTtc: number
    totalMontantVenteBrutTtcComptant: number
    totalMontantVenteBrutTtcCredit: number
    totalMontantVenteBrutTtcDiffere: number

    totalMontantVenteNetTtc: number


    totalMontantVenteNetTtcComptant: number
    totalMontantVenteNetTtcCredit: number
    totalMontantVenteNetTtcDiffere: number


    totalNombreVentes: number
}

