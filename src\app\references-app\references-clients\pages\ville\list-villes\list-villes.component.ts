import { Navigation } from 'swiper/core';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { FormControl, FormGroup, Validators } from '@angular/forms';
 import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { AlertService } from 'src/app/shared/services/alert.service';
import { WinClientVille } from '../../../models/ville';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { WinclientVilleService } from '../../../Services/winclient.ville.service';

@Component({
  selector: 'app-list-villes',
  templateUrl: './list-villes.component.html',
  styleUrls: ['./list-villes.component.scss']
})
export class ListVillesComponent implements OnInit {


  @ViewChild('updateOrCreateVilleModal') updateOrCreateVilleModal: TemplateRef<any>;

  modalMode : "EDIT" | "CREATE" = "CREATE";
  submited = false;
  currentEditingVille:any;
  modalRef : NgbModalRef;
  villesFilters:WinClientVille[] = [];

  navigation : Pagination = {
    pageSize :21,
    skip:0,
  }

  // villes:GridDataResult = {
  //   data:[] as WinClientVille[],
  //   total:0
  // };

  villes :WinClientVille[] = [];

  villeForm = new FormGroup({
    id: new FormControl(null),
    libelle: new FormControl('', [Validators.required,Validators.maxLength(30)])
  });

  
  constructor(
    private modalService: NgbModal,
    private userInputService:UserInputService,
    private villeService:WinclientVilleService,
    private alertService:AlertService
  ) { }

  ngOnInit() {


    this.getListVilles(0);
  }

  getListVilles(page=0){
    this.navigation.skip = page * this.navigation.pageSize;
    this.villeService.getAllVillesPaginated({page}).subscribe(res => {
      this.villes = res.content;
      // this.villes.total = res.totalElements;
      this.villesFilters = res.content;
      this.villes = this.villes.sort((a,b) => a.libelle.localeCompare(b.libelle));
    });
 
  }


  


  openCreateVille() {
    this.modalMode = "CREATE";
    this.modalRef = this.modalService.open(this.updateOrCreateVilleModal, { size: 'lg' });
    this.villeForm.reset();
  }


  openEditVille(ville:WinClientVille){
    this.modalMode = "EDIT";
    this.villeForm.patchValue(ville);
    console.log("ville", this.villeForm.value);
    this.modalRef = this.modalService.open(this.updateOrCreateVilleModal, { size: 'lg' });
  }


   formErrors(field:string){
    return this.villeForm.controls[field];
  }


  confirmDeleteVille(ville:any){
    this.userInputService.confirm("Confirmer la suppression de la ville ?","Voulez-vous vraiment supprimer cette ville ?","Supprimer","Annuler").then(result => {
      if(result){
        this.deleteVille(ville);
      }
    }).catch(() => { });
  }

  saveOrUpdateVille(){
    this.submited = true;
    if(this.villeForm.invalid){
      this.alertService.error("Veuillez remplir tous les champs");
      return;
    }


    const ville = this.villeForm.value;

    if(this.modalMode == "EDIT"){
      this.updateVille(ville);
    }else{
      this.createVille(ville);
    }
    this.modalRef?.close();
  }

  filterVille(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.villes = this.villesFilters.filter((ville) =>
      ville.libelle.toLowerCase().includes(searchTerm)
    );
  }

  pageChange(skip: number){
    this.navigation.skip =skip;
    const page = Math.ceil(skip / this.navigation.pageSize);
    // this.getListVilles(page);
  }


  private deleteVille(ville:any){
      this.villeService.deleteVille(ville.id).subscribe(res => {
        this.alertService.success("La ville a été supprimée avec succès");
        this.getListVilles();
      });
  }

  private updateVille(ville:any){
    
    const payload = new WinClientVille({
      libelle : ville.libelle
    });

    this.villeService.updateVille(ville.id,payload).subscribe(res => {
      this.getListVilles();
      this.alertService.success("La ville a été modifiée avec succès");
    });
  }

  private createVille(ville:any){

    const payload = new WinClientVille({
      libelle : ville.libelle
    });
    
    this.villeService.createVille(payload).subscribe(res => {
      this.getListVilles();
    });
  }

}      

