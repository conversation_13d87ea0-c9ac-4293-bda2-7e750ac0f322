import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ConfirmPasswordComponent } from './confirm-password/confirm-password.component';
import { LockScreenComponent } from './lock-screen/lock-screen.component';
import { LoginComponent } from './login/login.component';
import { LogoutComponent } from './logout/logout.component';
import { PasswordResetComponent } from './password-reset/password-reset.component';
import { SignupComponent } from './signup/signup.component';
import { AuthGuardService } from '../shared/services/auth-guard.service';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'login'
  },
  {


    path: 'login',
    component: LoginComponent,


  },
  // {
  //   path: 'login/user',
  //   component: LoginComponent,
  //   // TODO:   hafsa  check this line up plz    
  //   // TODO:   I commented it, there was issue when tenant_login success   and  going to home page   cannot go /logi/user   routing infinite loop
  // },




  // {
  //   path: "login",
  //   children: [
  //     {
  //       path: "", component: LoginComponent
  //     },
  //     {
  //       path: ":step",
  //       component: LoginComponent,
  //     },
  //   ]
  // },

  {
    path: 'signup',
    component: SignupComponent
  },
  {
    path: 'confirm',
    component: ConfirmPasswordComponent
  },
  {
    path: 'reset-password',
    component: PasswordResetComponent
  },
  {
    path: 'logout',
    component: LogoutComponent
  },
  // {
  //   path: 'logout/tenant',
  //   component: LogoutComponent
  // },
  {
    path: 'lock-screen',
    component: LockScreenComponent
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule { }
