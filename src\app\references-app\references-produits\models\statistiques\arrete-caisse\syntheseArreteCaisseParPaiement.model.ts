export class SyntheseArreteCaisseParPaiement {
    montantCheque: number = 0

    montantCredit: number = 0

    montantEspece: number = 0

    montantTiersPayant: number = 0

    montantTotal: number = 0

    montantTpe: number = 0

    montantVirement: number = 0

    montantDiffere: number = 0

    montantTpeVirement: number = 0

    montantCreditDiffere: number = 0

    /// percentage

    pourcentageCheque: number = 0

    pourcentageCredit: number = 0

    pourcentageDiffere: number = 0

    pourcentageEspece: number = 0 //

    pourcentageTiersPayant: number = 0

    pourcentageCreditDiffere: number = 0

    pourcentageTotal: number = 0

    pourcentageTpe: number = 0

    pourcentageTpeVirement: number = 0

    pourcentageVirement: number = 0



}
