<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Localite</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
      <!-- add a search input here -->
        <div class="input-group">
          <input type="search" class="form-control" placeholder="Rechercher une localite"  (input)="filterLocalite($event)"    />
          <button class="btn btn-primary">
            <i class="mdi mdi-magnify"></i>
          </button>
        </div>

      </div>
    </div>
  </div>
</div>

<kendo-grid [kendoGridBinding]="localite" 
style="height: calc(100vh - 130px);border-radius: 10px;" 
class="winClient-stats-grid ref-grid"
[pageable]="true"
[pageSize]="navigation.pageSize"
[skip]="navigation.skip"
 >
  <kendo-grid-column [width]="150" field="libLocalite" title="Libellé Localite" class="text-start" [headerClass]="'text-start'"></kendo-grid-column>
  <kendo-grid-column [width]="70">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="'pencil'" [extendClass]="'circle-lg'" (click)="openEditLocalite(dataItem)">
      </app-action-icon>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>



<ng-template #updateOrCreateLocaliteModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      <span *ngIf="modalMode == 'CREATE'">Créer une nouvelle ville</span>
      <span *ngIf="modalMode == 'EDIT'">Modifier la ville</span>
    </h4>
    <button type="button" class="cross-button" (click)="modal.close()" >
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="localiteForm" appFocusTrap>
      <div class="mb-3">
        <label for="nom" class="form-label">Localite Libelle<span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="nom" formControlName="libLocalite" [ngClass]="{'is-invalid':submited && formErrors('libLocalite').errors}"
         name="nom" placeholder="libelle de la localite">
        <span class="text-danger" *ngIf="submited && formErrors('libLocalite').errors?.required">Ce champs est obligatoire</span>
        <span class="text-danger" *ngIf="submited && formErrors('libLocalite').errors?.maxlength">Le nom de la ville est trop long (30 caractères max)</span>

      </div>
      <div class="mb-3">
        <label for="nom" class="form-label">Province<span class="text-danger">*</span></label>
        <input type="search" class="form-control" id="nom" formControlName="province" 
        [ngClass]="{'is-invalid':submited && formErrors('province').errors}"
        [ngbTypeahead]="searchProvinceTypeahead" [inputFormatter]="provinceFormatter" [resultFormatter]="provinceFormatter" [editable]="false"
         name="nom" placeholder="Province">
         <span class="text-danger" *ngIf="submited && formErrors('province').errors?.required">Ce champs est obligatoire</span>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary"  (click)="modal.close()" >Annuler</button>
    <button type="button" class="btn btn-primary" (click)="saveOrUpdateLocalite()" >
      {{modalMode == "CREATE" ? "Enregistrer" : "Modifier "}}
    </button>
  </div>
</ng-template>