import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ClientAssistantCriteria, ClientAssistantResponse } from "../models/clientAssistant";
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WinclientClientAssistanService {
  constructor(private httpClient: HttpClient) { }


  searchClientAssistan(clientAssistanCriteria: ClientAssistantCriteria[]) {
    return this.httpClient.post<ClientAssistantResponse[]>(`${env.winclient_base_url}/api/winclient/tac/association-client`, clientAssistanCriteria);
  }
}