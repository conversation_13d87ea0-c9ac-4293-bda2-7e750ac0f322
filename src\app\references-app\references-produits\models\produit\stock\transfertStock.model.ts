import { Statut } from "src/app/references-app/references-produits/enums/common/Statut.enum";
import { ProduitQuantite } from "../base/produitQuantite.model";
import { Depot } from "./depot.model";
import { Operateur } from "../../common/operateur.model";

export class TransfertStock {
  zoneSource?: Depot;

  zoneDestination?: Depot;

  produitQuantites?: ProduitQuantite[];
  ////
  dateAnnulation?: string
  dateCreation?: string
  id?: number
  numTransfert?: number
  qteTotale?: number
  statut?: Statut
  userAnnulation?: Operateur
  userCreation?: Operateur
  userValidation?: Operateur




  constructor() {
    this.produitQuantites = [];
  }
}
