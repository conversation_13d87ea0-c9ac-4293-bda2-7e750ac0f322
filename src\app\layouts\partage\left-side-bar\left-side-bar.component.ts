import { SIDEBAR_WIDTH_FIXED, SIDEBAR_WIDTH_CONDENSED } from './../models/layout.model';
import { AfterViewInit, Component, Input, OnInit, Renderer2, HostListener } from '@angular/core';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';
import { ModalDismissReasons, NgbCollapse, NgbDropdown, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthenticationService } from 'src/app/core/service/auth.service';
import { EventService } from 'src/app/core/service/event.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { MENU } from '../config/menu-meta';
import { MenuItem } from '../models/menu.model';
import { findAllParent, findMenuItem } from '../utils';
import { AuthChangeService } from 'src/app/references-app/references-produits/Services/config/auth-change.service';
import { WinoffreService } from 'src/app/shared/services/winoffre.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-left-side-bar',
  templateUrl: './left-side-bar.component.html',
  styleUrls: ['./left-side-bar.component.scss']
})
export class LeftSideBarComponent implements OnInit, AfterViewInit {
  today = new Date();
  @Input() navClasses: string | undefined;
  @Input() includeUserProfile: boolean = false;
  @Input() hideLogo: boolean = false;
  isInitialized: boolean = false;
  closeResult?: string;
  leftSidebarClass = 'sidebar-enable';
  activeMenuItems: string[] = [];
  loggedInUser: any = {};


  menuItems: MenuItem[] = [];
  parentKeySelected: string[] = [];


  constructor(
    private router: Router,
    private demoauthService: AuthenticationService,
    private authService: AuthService,
    private eventService: EventService,
    private authchangeService: AuthChangeService,
    private _renderer: Renderer2,
    private alertService: AlertService,
    private modalService: NgbModal) {
    router.events.forEach((event) => {
      if (event instanceof NavigationEnd) {
        this._activateMenu(); //actiavtes menu
      }
    });
  }

  ngOnInit(): void {
    this.initMenu();
    this.loggedInUser = this.demoauthService.currentUser();
    this.changePositionSubMenu();
    this.authchangeService.data$.subscribe(data => {
      this.initMenu();
    })
  }


  open(content: any) {
    this.modalService.open(content, {}).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult = `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  /**
   * On view init - activating menuitems
   */
  ngAfterViewInit() {
    setTimeout(() => {
      this._activateMenu();
    });
  }

  /**
   * initialize menuitems
   */
  initMenu(): void {
    this.menuItems = this.filterMenuByHabilitations(MENU);

    // remove parent that has no children to preview 
    this.menuItems = this.menuItems.filter(aa => {
      return (!aa.children || (aa.children && aa.children.length > 0))
    })
  }

  filterMenuByHabilitations(menu: MenuItem[]) {

    if (!menu) {
      return menu;
    }

    let result: MenuItem[] = [];

    for (const mi of menu) {
      let mustAdd = false;

      if (mi.authorities && mi.authorities.length) {
        if (this.authService.hasAnyAuthority(mi.authorities) == true
          && (environment?.environmentName && (Array.isArray(mi.hideInEnvironment) ?
            !mi.hideInEnvironment.includes(environment?.environmentName)
            : mi.hideInEnvironment != environment?.environmentName))) {
          mustAdd = true;
        }
      }
      else {
        mustAdd = true;
      }


      if (mustAdd) {
        if (mi.children) {
          mi.children = this.filterMenuByHabilitations(mi.children);
        }

        result.push(mi);
      }
    }

    return result;
  }



  /**
   * activates menu
   */
  _activateMenu(): void {
    const div = document.getElementById('leftside-menu-container');
    let matchingMenuItem = null;

    if (div) {
      let items: any = div.getElementsByClassName('side-nav-link-ref');
      for (let i = 0; i < items.length; ++i) {
        if (window.location.pathname === items[i].pathname || window.location.pathname === items[i].pathname + '/') {
          matchingMenuItem = items[i];
          break;
        }
      }

      if (matchingMenuItem) {
        const mid = matchingMenuItem.getAttribute('data-menu-key');
        const activeMt = findMenuItem(this.menuItems, mid);
        if (activeMt) {

          const matchingObjs = [activeMt['key'], ...findAllParent(this.menuItems, activeMt)];

          this.activeMenuItems = matchingObjs;

          this.menuItems.forEach((menu: MenuItem) => {
            menu.collapsed = !matchingObjs.includes(menu.key!);
          });
        }
      }
    }
  }

  scrollToOpenedMenu() {
    let menuActiveKey = ''
    this.menuItems.forEach((menu) => {
      if (!menu.collapsed) {
        menuActiveKey = menu.key
      }
    })

    const ActivetedMenuLink = document.querySelector(`[data-menu-key="${menuActiveKey}"]`)
    ActivetedMenuLink?.scrollIntoView()
  }

  /**
   * toggles open menu
   * @param menuItem clicked menuitem
   * @param collapse collpase instance
   */
  toggleMenuItem(menuItem: MenuItem, collapse: NgbCollapse): void {

    // If the menu item has a submenu, toggle its collapse state
    if (this.hasSubmenu(menuItem)) {
      collapse.toggle();
    }

    // Collapse all other menu items except for the parent of the current menu item
    this.menuItems.forEach((menu: MenuItem) => {
      // Check if the menu is not the current menu item and does not have the current menu item as a parent
      if (menu !== menuItem && !this.isParentOf(menuItem, menu)) {
        menu.collapsed = true;
      }
    });

    // If the menu item has a parent, ensure the parent is not collapsed
    if (menuItem.parentKey) {
      const parentMenuItem = this.findMenuItemByKey(this.menuItems, menuItem.parentKey);
      if (parentMenuItem) {
        parentMenuItem.collapsed = false;
      }
    }

    this.openMenu()

    this.scrollToOpenedMenu();
  }




  // Helper function to find a menu item by its key
  findMenuItemByKey(menuItems: MenuItem[], key: string): MenuItem | undefined {
    for (const menuItem of menuItems) {
      if (menuItem.key === key) {
        return menuItem;
      }
      if (menuItem.children) {
        const found = this.findMenuItemByKey(menuItem.children, key);
        if (found) {
          return found;
        }
      }
    }
    return undefined;
  }

  // Helper function to check if a menu item is a parent of another menu item
  isParentOf(child: MenuItem, parent: MenuItem): boolean {
    if (child.parentKey === parent.key) {
      return true;
    }
    if (child.parentKey) {
      const grandParent = this.findMenuItemByKey(this.menuItems, child.parentKey);
      if (grandParent) {
        return this.isParentOf(grandParent, parent);
      }
    }
    return false;
  }

  singleItemClick(item) {

    if (!item?.parentKey) {

      this.menuItems.forEach(menu => {
        menu.collapsed = true;
      })

    }

  }


  showParentSiblings(menuItem: MenuItem) {
    let temp = [this.parentKeySelected[0], menuItem.key];

    if (menuItem.parentKey && this.parentKeySelected.includes(menuItem.parentKey)) { // add siblings👩🏼‍🤝‍👩🏻 (add family👨‍👩‍👧‍👦 members)

      this.parentKeySelected = temp;

    } else { // new parent🧔 (new array, new family👨‍👩‍👧‍👦)

      this.parentKeySelected = [menuItem.key];
    }

  }

  /**
   * Returns true or false if given menu item has child or not
   * @param item menuItem
   */
  hasSubmenu(menu: MenuItem): boolean {
    return menu.children ? true : false;
  }


  /**
   * Hides the menubar
   */
  hideMenu() {
    document.body.classList.remove('sidebar-enable');
  }

  //ANCHOR: CHANGE POSITION OF SUBMENU (MENU WITH CHILDRENS) WHEN SCREEN HEIGHT IS SMALLER THAN 920PX
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.changePositionSubMenu();
  }


  changePositionSubMenu() {
    setTimeout(() => {
      let secondLevelMenu = document.getElementsByClassName('side-nav-second-level');
      for (let i = secondLevelMenu.length - 1; i >= Math.floor(this.menuItems.length / 2); i--) {

        if (window.innerHeight <= 920) {
          // secondLevelMenu.item(i).classList.add('has-enough-space');
        } else {
          // secondLevelMenu.item(i).classList.remove('has-enough-space');
        }
      }

    });

  }


  toggleScroll(status) {
    //Find Sidebar content wrapper Element 
    let sidebarWrapper = document.getElementsByClassName('simplebar-content-wrapper')[0];
    if (status == 'scroll' && window.innerHeight <= 670) {
      sidebarWrapper.setAttribute('style', `height:auto; overflow:hidden scroll !important;bottom:${sidebarWrapper.scrollTop}px`);
    } else {
      sidebarWrapper.setAttribute('style', `height:auto; overflow:visible !important; bottom:${sidebarWrapper.scrollTop}px`);
    }

  }

  removeHoverOnItems() {
    let docEle = document.querySelectorAll('li.link-hovered-class');
    docEle.forEach(item => {
      item.classList.remove('link-hovered-class');
      item.children.item(0).children.item(1).classList.remove('span-hovered-class');
    })
  }


  openMenu(): void {

    if (document.body.hasAttribute('data-leftbar-compact-mode') && document.body.getAttribute('data-leftbar-compact-mode') === 'condensed') {
      // document.body.removeAttribute('data-leftbar-compact-mode');
      this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_FIXED);
    }

  }





}
