import { TypeProcess } from 'src/app/references-app/references-produits/enums/produit/TypeProcess.enum';


import { ProcessIntrant } from './processIntrant.model';
import { ProcessSortie } from './processSortie.model';


export class Process {
    audited?: boolean;
    dateCreation?: any;
    id?: number;
    nomProcess?: string;
    processIntrants?: ProcessIntrant[];
    processSorties?: ProcessSortie[];
    typeProcess?: TypeProcess;
    userModifiable?: boolean;

    // TODO :: OTH
    estActif?: boolean;

    // transient properties
    coutRevient?: number;
    totalPrixVente?: number;

}

