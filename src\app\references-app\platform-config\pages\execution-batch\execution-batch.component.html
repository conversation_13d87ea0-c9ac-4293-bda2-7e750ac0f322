<!-- start page title -->
<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Exécution Batch Admin</h4>
      <div class="d-flex flex-wrap justify-content-end ">

      </div>
    </div>
  </div>
</div>
<div class=" my-2 custom-container mx-1">
  <div class="row mb-2">
    <div class="col-12 d-flex justify-content-between align-items-center">
      <h4 class="mb-0 fs-3-5 mt-0">Liste des batches disponibles</h4>
    </div>
  </div>
  <!-- Batches Grid -->
  <div class="row k-gap-y-2">
    <!-- Batch Card -->
    <div class="col-12 col-md-6 col-xl-4"  *ngFor="let batch of batches">
      <div class="card h-100 shadow-sm border-light">

        <!-- Card Header -->
        <div class="card-header bg-light d-flex align-items-center py-3">
          <div class="border-start border-3 border-primary me-3"></div>
          <h2 class="h5 mb-0 text-truncate" title="{{ batch.title }}">
            {{ batch.title }}
          </h2>
        </div>

        <!-- Card Body -->
        <div class="card-body d-flex flex-column py-4">
          <!-- Description -->
          <p class="text-muted mb-3 fs-6">
            {{ batch.description || batch.title }}
          </p>

          <!-- Status -->
          <div class="mt-auto">
            <div class="d-flex align-items-center mb-3">
              <span class="badge bg-secondary bg-opacity-10 text-secondary fs-7">
                Status
              </span>
              <app-element-status [objectStatut]="getObject(batch)" class="ms-2"></app-element-status>
            </div>

            <!-- Last Execution -->
            <p class="text-muted fs-7 mb-0" *ngIf="batch['lastExec']">
              <i class="bi bi-clock-history me-2"></i>
              Dernière exécution: {{ batch['lastExec'] | date : 'dd-MM-YYYY HH:mm' }}
            </p>
          </div>
        </div>

        <!-- Card Footer -->
        <div class="card-footer ">
          <div class="d-flex justify-content-between align-items-center">


            <!-- History Button -->
            <button class="btn btn-historique" (click)="open(timelineModal)" [disabled]="true"
              aria-label="View history">
              <i class="bi bi-archive me-2"></i>
              <span>Historique</span>

            </button>

            <!-- Execute Button -->
            <button class="btn btn-execute" (click)="openForm(batch, batchFormModal)" aria-label="Execute batch">
              <i class="mdi mdi-play-circle me-2"></i>
              <span>Exécuter</span>
            </button>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- Modal -->

</div>


<ng-template #timelineModal let-cf="close" let-df="dismiss">
  <div class="modal-header">
    <span class="vertical-line"></span>
    <h5 class="modal-title card-title">Historique des batches</h5>
    <button type="button" class="btn-close" (click)="cf('close')"></button>
  </div>
  <div id="content">
    <ul class="timeline">
      <li class="event  d-sm-flex align-items-sm-center justify-content-sm-between" *ngFor="let item of timelineItems"
        [attr.data-date]="item.date">
        <p>{{ item.description }}</p>
        <span [ngClass]="item.statusClass" class="status-pill">{{ item.status }}</span>
      </li>


    </ul>
  </div>
</ng-template>


<ng-template #batchFormModal let-modal let-df="dismiss">
  <div class="modal-header">

    <h4 class="modal-title card-title">Exécuter : {{selectedBatch.title}}</h4>
    <button type="button" class="btn-close"></button>
  </div>
  <div id="modal-body p-2">
    <div class="form-group p-2 m-2">
      <form [formGroup]="batchForm" (ngSubmit)="submitForm()">
        <div *ngFor="let field of selectedBatch?.configuration">
          <div class="mb-3" [ngSwitch]="field.type">
            <!-- Text Input -->
            <div *ngSwitchCase="'text'">
              <label for="{{field.name}}" class="form-label">{{ field.label }}</label>
              <input type="text" class="form-control" id="{{field.name}}" formControlName="{{field.name}}">
            </div>
            <!-- Number Input -->
            <div *ngSwitchCase="'number'">
              <label for="{{field.name}}" class="form-label">{{ field.label }}</label>
              <input type="number" class="form-control text-end" id="{{field.name}}" formControlName="{{field.name}}">
            </div>


            <!-- Select Input -->
            <div *ngSwitchCase="'select'">
              <label for="{{field.name}}" class="form-label">{{ field.label }}</label>
              <select class="form-select" id="{{field.name}}" formControlName="{{field.name}}">
                <option [ngValue]="null"></option>

                <option *ngFor="let option of field.options" [value]="option.value">{{ option.label }}</option>
              </select>
            </div>

            <!-- Checkbox Input -->
            <div *ngSwitchCase="'checkbox'">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="{{field.name}}" formControlName="{{field.name}}">
                <label class="form-check-label" for="{{field.name}}">
                  {{ field.label }}
                </label>
              </div>
            </div>
            <div *ngSwitchCase="'date'">
              <label [for]="field.name" class="form-label-cus">{{field.label}}</label>
              <app-date-picker [formControlName]="field.name"></app-date-picker>

            </div>
          </div>
        </div>


      </form>
    </div>


  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('close')">Quitter</button>
    <button type="submit" class="btn btn-warning" [disabled]="batchForm.invalid"
      (click)="modal.close();submitForm()">Exécuter</button>
  </div>
</ng-template>