<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Province</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
              <div class="input-group">
          <input type="search" class="form-control px-2" placeholder="Rechercher  province"   (input)="filterProvinces($event)"   />
          <button class="btn btn-primary">
            <i class="mdi mdi-magnify"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-grid [kendoGridBinding]="provinces" 
style="height: calc(100vh - 130px);border-radius: 10px;" 
class="winClient-stats-grid ref-grid"
[pageable]="true"
[pageSize]="navigation.pageSize"
[skip]="navigation.skip"
 >
  <kendo-grid-column [width]="150" field="libProvince" title="Libellé Province" class="text-start" [headerClass]="'text-start'"></kendo-grid-column>

  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>