// import { Moment } from 'moment';

import { Beneficiaire } from 'src/app/references-app/references-produits/models/tiers/client/beneficiaire.model';
import { Client } from 'src/app/references-app/references-produits/models/tiers/client/client.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';


export class EncaissementCredit { 
    audited?: boolean;
    beneficiaire?: Beneficiaire;
    client?: Client;
    date?: any;
    designation?: string;
    heure?: any;
    montant?: number;
    operateur?: Operateur;
    prix?: number;
    qt?: number;
    tauxRemise?: number;
    userModifiable?: boolean;
}
