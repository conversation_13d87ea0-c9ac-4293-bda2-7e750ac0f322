// floating-window.component.ts
import { Component, HostListener, OnInit } from '@angular/core';

@Component({
  selector: 'app-floating-window',
  templateUrl: './floating-window.component.html',
  styleUrls: ['./floating-window.component.scss']
})
export class FloatingWindowComponent implements OnInit {
  onScaleStepChange(event:any) {
    if(event.currentTarget.value === '' || event.currentTarget.value === '0' || isNaN(+event.currentTarget.value)) {
      this.scaleStep = 0.1;
      event.currentTarget.value = 0.1;
      console.log('Invalid value for scale step');
      
      return;
    }
  this.scaleStep = +event.currentTarget.value;
  }
  ResetScale() {
  this.scale = 1;
  this.changeHtmlFontScale(this.scale);
  this.saveScaleToLocalStorage();
  }
  isShown: boolean = false;
  zIndex: number = 9999999999999;
  top: number = 50;
  left: number = 50;
  width: number = 300;
  height: number = 150;
  scale: number = 1;
  scaleStep: number = 0.1;
  resizing: boolean = false;
  startX: number = 0;
  startY: number = 0;

  maxWindowWidth: number = 600;
  maxWindowHeight: number = 400;

  constructor() { }

  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {

    const key = event.key?.toLowerCase();
    
    if (event.ctrlKey && event.shiftKey && key === 'f') {
      this.toggleWindow();
    }
  }

  ngOnInit(): void {
    const scale = localStorage.getItem('scale');

    if (scale && !isNaN(+scale)) {
      this.scale = +scale;
      this.changeHtmlFontScale(this.scale);
    }
  }

  toggleWindow() {    
    this.isShown = !this.isShown;
  }

  closeWindow() {
    this.isShown = false;
  }

  onDragStart(event: MouseEvent) {
    const offsetX = event.clientX - this.left;
    const offsetY = event.clientY - this.top;

    const onMouseMove = (moveEvent: MouseEvent) => {
      let newLeft = moveEvent.clientX - offsetX;
      let newTop = moveEvent.clientY - offsetY;

      // Calculate the maximum and minimum left and top positions
      const maxLeft = window.innerWidth - this.width;
      const maxTop = window.innerHeight - this.height;
      newLeft = Math.max(0, Math.min(newLeft, maxLeft));
      newTop = Math.max(0, Math.min(newTop, maxTop));

      this.left = newLeft;
      this.top = newTop;
    };

    const onMouseUp = () => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };

    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', onMouseUp);
  }

  startResize(event: MouseEvent) {
    this.resizing = true;
    this.startX = event.clientX;
    this.startY = event.clientY;

    // Disable text selection on the body
    document.body.style.userSelect = 'none';
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    if (this.resizing) {
      const deltaX = event.clientX - this.startX;
      const deltaY = event.clientY - this.startY;

      let newWidth = this.width + deltaX;
      let newHeight = this.height + deltaY;

      // Apply maximum width and height constraints
      newWidth = Math.min(newWidth, this.maxWindowWidth);
      newHeight = Math.min(newHeight, this.maxWindowHeight);

      this.width = Math.max(300, newWidth); // minimum width
      this.height = Math.max(150, newHeight); // minimum height

      this.startX = event.clientX;
      this.startY = event.clientY;
    }
  }

  @HostListener('document:mouseup')
  onMouseUp() {
    this.resizing = false;

    // Enable text selection again after resizing ends
    document.body.style.userSelect = 'auto';
  }

  onScaleChange(event: any) {    
    this.scale = event.currentTarget.value;
    this.changeHtmlFontScale(this.scale);
    this.saveScaleToLocalStorage();
  }

  changeHtmlFontScale(scale: number) {
    document.documentElement.style.setProperty('--font-scale', scale.toString());
  }

  saveScaleToLocalStorage() {
    localStorage.setItem('scale', this.scale.toString());
  }
}
