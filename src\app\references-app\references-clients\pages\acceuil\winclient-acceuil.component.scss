$primary-color: #3686b6;
$secondary-color: #d33434;
$success-color: #2c7a18;
$warning-color: #e75e0f;
$light-gray: #f8f9fa;
$dark-gray: #495057;
$card-radius: 16px;
$transition-speed: 0.3s;


.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: rgba(17, 17, 26, 0.1) 0px 0px 16px;    
    transition: transform 0.2s;
    margin-bottom: 1.5rem;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.stat-card {
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;


    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6px;
        height: 100%;
        border-radius: 3px 0 0 3px;
      }
     
}
.primary-card {
    &::before { background-color: $primary-color; }
    .stat-icon { background-color: $primary-color; }
    .stat-value { color: $primary-color; }
  }
  
  .secondary-card {
    &::before { background-color: $secondary-color; }
    .stat-icon { background-color: $secondary-color; }
    .stat-value { color: $secondary-color; }
  }
  
  .warning-card {
    &::before { background-color: $warning-color; }
    .stat-icon { background-color: $warning-color; }
    .stat-value { color: $warning-color; }
  }
  
  .success-card {
    &::before { background-color: $success-color; }
    .stat-icon { background-color: $success-color; }
    .stat-value { color: $success-color; }
  }
.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stat-label {
    color: #6c757d;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.custom-table {
    margin: 0;
}

.custom-table th {
    background-color: #f8f9fa;
    border-top: none;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
}

 

.status-up {
    color: #2ecc71;
}

.status-pending {
    color: #f1c40f;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

.skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: pulse 1.5s ease-in-out infinite;
    border-radius: 4px;
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-loading-circle {
    @extend .skeleton-loading;
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

.skeleton-loading-block {
    @extend .skeleton-loading;
    height: 20px;
    margin-block: 28px;
}

.skeleton-block-table{
    height: 300px !important;
    margin-block: 0 !important;
}