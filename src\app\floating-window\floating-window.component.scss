/* floating-window.component.css */
.floating-window {
  position: absolute;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  font-size: 16px; /* Set the font size to a fixed value */
}

.floating-window-header {
  background-color: #f0f0f0;
  padding: 10px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  cursor: move;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.floating-window-title {
  font-weight: bold;
}

.floating-window-close {
  cursor: pointer;
}

.floating-window-content {
  padding: 10px;
  flex-grow: 1;
}

.floating-window-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: gray;
  cursor: nwse-resize;
}
