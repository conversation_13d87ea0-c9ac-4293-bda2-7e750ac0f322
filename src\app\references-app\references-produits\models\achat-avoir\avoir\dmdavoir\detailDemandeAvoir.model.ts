import { CauseDemandeAvoir } from 'src/app/references-app/references-produits/enums/achat-avoir/CauseDemandeAvoir.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Produit } from 'src/app/references-app/references-produits/models/produit/base/produit.model';
import { Taxe } from 'src/app/references-app/references-produits/models/common/taxe.model';


export class DetailDemandeAvoir {
    audited?: boolean;
    cause?: CauseDemandeAvoir;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codePrd?: string;
    ctgr?: CategorieProduit;
    dateDmd?: any;
    datePeremption?: string;
    depotId?: number;
    dsgnPrd?: string;
    frm?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    labo?: Fournisseur;
    mntLigneAchatStd?: number;
    mntLigneBrutHt?: number;
    mntLigneBrutTtc?: number;
    mntLigneNetHt?: number;
    mntLigneNetTtc?: number;
    mntLigneRemiseHt?: number;
    mntLigneRemiseTtc?: number;
    mntLigneTva?: number;
    mntLigneVenteStd?: number;
    mntUnitRemiseHt?: number;
    mntUnitRemiseTtc?: number;
    mntUnitTva?: number;
    numDmd?: number;
    numLot?: string;
    pbrH?: number;
    pbrP?: number;
    prixAchatStd?: number;
    prixAchatTtc?: number;
    prixBrutHt?: number;
    prixBrutTtc?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixNetHt?: number;
    prixNetTtc?: number;
    prixVenteStd?: number;
    prixVenteTtc?: number;
    produit?: Produit;
    qtAvoir?: number;
    qtLivre?: number;
    stockId?: string;
    tauxMarge?: number;
    tauxRemise?: number;
    tauxTva?: number;
    tva?: Taxe;
    userModifiable?: boolean;


    


    idx?: number;//* local property *//
    soldeSource?: number;//* local property *//
    solde?: number;//* local property *//
    qteAcceptee?: number;//* local property *//
    qtAvoirRefuse?: number;//* local property *//
}
