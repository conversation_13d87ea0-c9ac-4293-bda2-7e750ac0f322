import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { Dci } from 'src/app/references-app/references-produits/models/produit/medical/dci.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { IndicateurProduit } from './indicateurProduit.model';
import { ProduitBase } from './produitBase.model';
import { ProduitCodebarre } from './produitCodebarre.model';
import { ProduitParametre } from './produitParametre.model';
import { Stock } from 'src/app/references-app/references-produits/models/produit/stock/stock.model';
import { Taxe } from 'src/app/references-app/references-produits/models/common/taxe.model';
import { GammeFournisseur } from '../../tiers/fournisseur/gammeFournisseur.model';
import { Operateur } from '../../common/operateur.model';
import { Ville } from '../../common/ville.model';
import { TypeSuggestionEnum } from 'src/app/references-app/references-produits/enums/produit/type-suggestion.enum';


export class FicheProduit {
    audited?: boolean;
    categorie?: CategorieProduit;
    codeBarre?: string;
    codeBarres?: ProduitCodebarre[];
    codePrd?: string;
    dateCreation?: any;
    dateFinCom?: any;
    dci?: Dci;
    designation?: string;
    dosage?: string;
    estFabrique?: boolean;
    estMarche?: boolean;
    estOblgPrescription?: boolean;
    estPrinceps?: boolean;
    estPrixmarque?: boolean;
    estPsychotrope?: boolean;
    estRbrsblBase?: boolean;
    estStockable?: boolean;
    estToxique?: boolean;
    estTpaBase?: boolean;
    estVendable?: boolean;
    familleTarifaire?: FamilleTarifaire;
    formeGalenique?: FormeProduit;
    id?: number;
    idhash?: string;
    indicateur?: IndicateurProduit;
    laboratoire?: Fournisseur;
    nomRacine?: string;
    pbrH?: number;
    pbrP?: number;
    presentation?: string;
    prixAchatStd?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixVenteStd?: number;
    produitBase?: ProduitBase;
    produitParametre?: ProduitParametre;
    statut?: Statut;
    stocks?: Stock[];
    tauxRemb?: number;
    totalStock?: number;
    tva?: Taxe;
    typeProcess?: string;
    userModifiable?: boolean;

    gammeLabo?: GammeFournisseur

    colisage?: number;

    codeGroupe?: string

    pphTtc?: number


    // suggestion?: boolean    // TODO: HTO   remove this
    // motif?: string    // TODO: HTO   remove this






    // TODO: HTO use this
    userCreation?: Operateur;
    userValidation?: Operateur;
    userAnnulation?: Operateur;
    dateValidation?: any;
    dateAnnulation?: any;

    /*********** suggestion *************/

    isSuggestion?: boolean;
    typeSuggestion?: TypeSuggestionEnum;
    motifAnnulSuggestion?: string;
    suggestionPrdId?: number;
    suggestionCodePrd?: string;

    suggestionTenantCode?: string;
    suggestionTenantRaisonSociale?: string;
    suggestionTenantVille?: Ville;

    /************************************/


}

