<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Sites</h4>
      <div class="d-flex justify-content-end gap-2 align-items-center  flex-row">
        <div class="input-group">
          <input type="search" class="form-control px-2" placeholder="Rechercher un site" (input)="filterSites($event)" />
          <button class="btn btn-primary">
            <i class="mdi mdi-magnify"></i>
          </button>
        </div>
        <button type="button" class="btn btn-primary" style="flex-shrink: 0;"
        (click)="openCreateVille()"
         >
         <i class="mdi mdi-plus"></i>
          Nouveau
        </button>
    </div>
  </div>
</div>

<kendo-grid [kendoGridBinding]="sites" 
style="height: calc(100vh - 130px);border-radius: 10px;" 
class="winClient-stats-grid ref-grid"
 [pageable]="true" [pageSize]="navigation.pageSize" [skip]="navigation.skip"  
>
  <kendo-grid-column [width]="150" field="libelleCourt" title="Libellé Court" class="text-start" [headerClass]="'text-start'"></kendo-grid-column>
  <kendo-grid-column   field="libelleLong" title="Libellé Long" class="text-start" [headerClass]="'text-start'"></kendo-grid-column>
  <kendo-grid-column field="region.libRegion" title="Region" class="text-start" [headerClass]="'text-start'"></kendo-grid-column>
  <kendo-grid-column field="visible" title="Visible" class="text-start" [headerClass]="'text-start'">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span *ngIf="dataItem.visible" class="badge bg-success">Oui</span>
      <span *ngIf="!dataItem.visible" class="badge bg-danger">Non</span>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column [width]="70">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="'pencil'" [extendClass]="'circle-lg'" (click)="openEditVille(dataItem)">

      </app-action-icon>
      <app-action-icon [icon]="'close'" [extendClass]="'circle-lg'" [backgroundColor]="'danger'" (click)="confirmDeleteSite(dataItem.id)">

      </app-action-icon>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" 
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>


<ng-template #updateOrCreateVilleModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      <span *ngIf="modalMode == 'CREATE'">Créer un nouveau site</span>
      <span *ngIf="modalMode == 'EDIT'">Modifier le site</span>
    </h4>
    <button type="button" class="cross-button" (click)="modal.close()" >
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="siteForm">
      <div class="mb-3">
        <label for="nom" class="form-label">Libellé Court<span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="nom" formControlName="libelleCourt" [ngClass]="{'is-invalid':submited && formErrors('libelleCourt').errors}"
         name="nom" placeholder="Libellé Court">
        <span class="text-danger" *ngIf="submited && formErrors('libelleCourt').errors?.required">Ce champs est obligatoire</span>
        <span class="text-danger" *ngIf="submited && formErrors('libelleCourt').errors?.maxlength">Libellé Court est trop long (30 caractères max)</span>

      </div>
      <div class="mb-3">
        <label for="nom" class="form-label">Libellé Long<span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="nom" formControlName="libelleLong" [ngClass]="{'is-invalid':submited && formErrors('libelleLong').errors}"
         name="nom" placeholder="Libellé Long">
        <span class="text-danger" *ngIf="submited && formErrors('libelleLong').errors?.required">Ce champs est obligatoire</span>
        <span class="text-danger" *ngIf="submited && formErrors('libelleLong').errors?.maxlength">Libellé Long est trop long (30 caractères max)</span>

      </div>
      <div class="mb-3">
        <label for="nom" class="form-label">Region<span class="text-danger">*</span></label>
        <select [ngClass]="{'is-invalid':submited && formErrors('region').errors}" class="form-select" formControlName="region" [compareWith]="compareFN">
          <option [ngValue]="null">Select Region</option>
          <option *ngFor="let region of regions" [ngValue]="region">{{region.libRegion}}</option>
        </select>
        <span class="text-danger" *ngIf="submited && formErrors('region').errors?.required">Ce champs est obligatoire</span>
        <span class="text-danger" *ngIf="submited && formErrors('region').errors?.maxlength">Le nom de la ville est trop long (30 caractères max)</span>

      </div>
      <div class="mb-3">
        <div class="d-flex justify-content-start align-items-center gap-2 user-select-none">
          <div>
            <span>Visible</span>
          </div>
          <div>
            <div class="form-check form-check-inline me-0 ps-0">
              <input type="checkbox" id="visible" name="visible"
                formControlName="visible" data-switch="none" />
              <label for="visible" data-On-label="Oui" data-off-label="Non"></label>
            </div>
          </div>
        </div>
        </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary"  (click)="modal.close()" >Annuler</button>
    <button type="button" class="btn btn-primary" (click)="saveOrUpdateSite()" >
      {{modalMode == "CREATE" ? "Enregistrer" : "Modifier "}}
    </button>
  </div>
</ng-template>
