{"clients": [{"id": 1, "prenom": "Trip", "nom": "Rentcome", "email": "<EMAIL>", "cin": 1992, "ice": 2010, "telephone": "8306057642", "gsm": "9901393582", "adresse1": "6 Debs Plaza", "adresse2": "517 Westridge Drive", "ville": 10, "soldeDepart": 64625, "plafond": 141194, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [3, 5], "beneficiaires": [5, 5], "desactive": false, "identifiantsConventions": [1, 4], "solde": 9660}, {"id": 2, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1997, "ice": 2004, "telephone": "7229713776", "gsm": "9238487929", "adresse1": "2303 Fulton Avenue", "adresse2": null, "ville": null, "soldeDepart": 55692, "plafond": 68043, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [4, 2], "beneficiaires": [6, 9], "desactive": true, "identifiantsConventions": [8, 1], "solde": 2668}, {"id": 3, "prenom": "Dur", "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2000, "ice": 1997, "telephone": "2181133915", "gsm": "1444035888", "adresse1": "2570 Moose Pass", "adresse2": "4513 Sloan Center", "ville": 5, "soldeDepart": 153215, "plafond": 177235, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [2, 1], "beneficiaires": [1, 5], "desactive": false, "identifiantsConventions": [9, 6], "solde": 6589}, {"id": 4, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1990, "ice": 2010, "telephone": "9796582351", "gsm": "8902452460", "adresse1": "666 Grayhawk Way", "adresse2": null, "ville": null, "soldeDepart": 163856, "plafond": 76815, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [3, 6], "beneficiaires": [1, 1], "desactive": false, "identifiantsConventions": [8, 1], "solde": 4021}, {"id": 5, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1993, "ice": 2004, "telephone": "2787764167", "gsm": "1654962407", "adresse1": "081 Vera Park", "adresse2": null, "ville": null, "soldeDepart": 121743, "plafond": 26660, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [6, 9], "beneficiaires": [1, 6], "desactive": false, "identifiantsConventions": [5, 6], "solde": 2240}, {"id": 6, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1989, "ice": 2001, "telephone": "5326268987", "gsm": "4904451935", "adresse1": "07134 Schmedeman Street", "adresse2": "357 Talisman Point", "ville": 7, "soldeDepart": 104285, "plafond": 180907, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [1, 3], "beneficiaires": [9, 8], "desactive": false, "identifiantsConventions": [3, 5], "solde": 1016}, {"id": 7, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2000, "ice": 2001, "telephone": "8902753599", "gsm": "4706455795", "adresse1": "7436 Ludington Trail", "adresse2": "16738 Continental Avenue", "ville": 6, "soldeDepart": 2416, "plafond": 2199, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [3, 3], "beneficiaires": [9, 6], "desactive": false, "identifiantsConventions": [6, 2], "solde": 8414}, {"id": 8, "prenom": "Link", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1997, "ice": 2005, "telephone": "8209933958", "gsm": "8873311203", "adresse1": "7 Village Green Crossing", "adresse2": "27 Chinook Parkway", "ville": 6, "soldeDepart": 99317, "plafond": 174192, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [2, 1], "beneficiaires": [7, 5], "desactive": false, "identifiantsConventions": [3, 7], "solde": 9031}, {"id": 9, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1993, "ice": 1997, "telephone": "5045607890", "gsm": "6975263664", "adresse1": "619 Bluestem Center", "adresse2": null, "ville": null, "soldeDepart": 6037, "plafond": 56496, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [6, 1], "beneficiaires": [5, 4], "desactive": false, "identifiantsConventions": [5, 5], "solde": 6457}, {"id": 10, "prenom": "Orin", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2011, "ice": 2004, "telephone": "5355455049", "gsm": "6444602514", "adresse1": "89229 Northridge Alley", "adresse2": null, "ville": null, "soldeDepart": 197566, "plafond": 134328, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [9, 8], "beneficiaires": [2, 6], "desactive": false, "identifiantsConventions": [1, 3], "solde": 1448}, {"id": 11, "prenom": "<PERSON><PERSON>", "nom": "Crenage", "email": "<EMAIL>", "cin": 2005, "ice": 2008, "telephone": "3293996677", "gsm": "6283491818", "adresse1": "5 La Follette Junction", "adresse2": "932 Sachtjen Trail", "ville": 3, "soldeDepart": 20219, "plafond": 173438, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [7, 7], "beneficiaires": [1, 2], "desactive": false, "identifiantsConventions": [3, 5], "solde": 1371}, {"id": 12, "prenom": "<PERSON><PERSON>", "nom": "<PERSON>gdon", "email": "<EMAIL>", "cin": 1992, "ice": 2002, "telephone": "2375807790", "gsm": "5524310492", "adresse1": "980 Ohio Trail", "adresse2": null, "ville": null, "soldeDepart": 58687, "plafond": 84725, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [3, 4], "beneficiaires": [6, 3], "desactive": false, "identifiantsConventions": [3, 5], "solde": 6680}, {"id": 13, "prenom": "<PERSON><PERSON>", "nom": "Cumming", "email": "<EMAIL>", "cin": 2011, "ice": 1989, "telephone": "1957277029", "gsm": "9045741137", "adresse1": "816 Pine View Avenue", "adresse2": null, "ville": null, "soldeDepart": 196970, "plafond": 81969, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [1, 6], "beneficiaires": [5, 5], "desactive": false, "identifiantsConventions": [8, 2], "solde": 3241}, {"id": 14, "prenom": "<PERSON>", "nom": "Fr<PERSON>d", "email": "<EMAIL>", "cin": 1985, "ice": 1997, "telephone": "4849112459", "gsm": "5205999909", "adresse1": "42 Old Shore Terrace", "adresse2": null, "ville": null, "soldeDepart": 119276, "plafond": 150960, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [8, 3], "beneficiaires": [8, 4], "desactive": false, "identifiantsConventions": [5, 6], "solde": 3827}, {"id": 15, "prenom": "<PERSON><PERSON>", "nom": "Tripcony", "email": "<EMAIL>", "cin": 1966, "ice": 1988, "telephone": "2918571127", "gsm": "6769920124", "adresse1": "1007 American Center", "adresse2": null, "ville": null, "soldeDepart": 105864, "plafond": 187266, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [4, 1], "beneficiaires": [9, 4], "desactive": false, "identifiantsConventions": [4, 2], "solde": 1534}, {"id": 16, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2006, "ice": 1993, "telephone": "7724995252", "gsm": "6466947510", "adresse1": "1065 Brentwood Parkway", "adresse2": null, "ville": null, "soldeDepart": 76729, "plafond": 55119, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [8, 4], "beneficiaires": [4, 6], "desactive": false, "identifiantsConventions": [6, 9], "solde": 1705}, {"id": 17, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2008, "ice": 1993, "telephone": "7853672107", "gsm": "4905865163", "adresse1": "45 Knutson Avenue", "adresse2": "3795 Lyons Junction", "ville": 4, "soldeDepart": 56769, "plafond": 83599, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [1, 4], "beneficiaires": [7, 8], "desactive": false, "identifiantsConventions": [7, 8], "solde": 7397}, {"id": 18, "prenom": "<PERSON><PERSON>", "nom": "Hamlyn", "email": "<EMAIL>", "cin": 2009, "ice": 2006, "telephone": "3656888906", "gsm": "8426328261", "adresse1": "8092 School Center", "adresse2": null, "ville": null, "soldeDepart": 113505, "plafond": 182059, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [9, 1], "beneficiaires": [9, 5], "desactive": false, "identifiantsConventions": [9, 1], "solde": 6939}, {"id": 19, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1991, "ice": 1994, "telephone": "2585988564", "gsm": "9593088676", "adresse1": "04 Almo Way", "adresse2": null, "ville": null, "soldeDepart": 56185, "plafond": 75546, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [1, 2], "beneficiaires": [1, 2], "desactive": false, "identifiantsConventions": [6, 2], "solde": 1107}, {"id": 20, "prenom": "<PERSON>", "nom": "Stitson", "email": "<EMAIL>", "cin": 2012, "ice": 1992, "telephone": "9884334110", "gsm": "8864188711", "adresse1": "9000 Southridge Terrace", "adresse2": "2852 Esch Drive", "ville": 7, "soldeDepart": 122947, "plafond": 12223, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [5, 5], "beneficiaires": [4, 5], "desactive": false, "identifiantsConventions": [2, 2], "solde": 9641}, {"id": 21, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1983, "ice": 2012, "telephone": "2862821076", "gsm": "1761600723", "adresse1": "301 Elka Lane", "adresse2": null, "ville": null, "soldeDepart": 109618, "plafond": 4940, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [8, 4], "beneficiaires": [9, 1], "desactive": false, "identifiantsConventions": [8, 3], "solde": 4240}, {"id": 22, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Bein", "email": "<EMAIL>", "cin": 2003, "ice": 2002, "telephone": "9794707336", "gsm": "6616286016", "adresse1": "430 Welch Way", "adresse2": null, "ville": null, "soldeDepart": 112990, "plafond": 128977, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [9, 5], "beneficiaires": [8, 6], "desactive": false, "identifiantsConventions": [1, 5], "solde": 6556}, {"id": 23, "prenom": "<PERSON>", "nom": "Tiptaft", "email": "<EMAIL>", "cin": 1996, "ice": 2012, "telephone": "5045377318", "gsm": "2939208686", "adresse1": "35849 Merry Road", "adresse2": null, "ville": null, "soldeDepart": 163300, "plafond": 179522, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [1, 5], "beneficiaires": [5, 5], "desactive": false, "identifiantsConventions": [8, 7], "solde": 9900}, {"id": 24, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Studholme", "email": "<EMAIL>", "cin": 1999, "ice": 2003, "telephone": "4306089225", "gsm": "6626385858", "adresse1": "8538 North Junction", "adresse2": null, "ville": null, "soldeDepart": 199656, "plafond": 85797, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [5, 6], "beneficiaires": [2, 5], "desactive": false, "identifiantsConventions": [5, 5], "solde": 4896}, {"id": 25, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2001, "ice": 1988, "telephone": "3423345304", "gsm": "1157109131", "adresse1": "3561 Express Pass", "adresse2": "0247 Sommers Alley", "ville": 3, "soldeDepart": 98468, "plafond": 76185, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [6, 7], "beneficiaires": [1, 6], "desactive": false, "identifiantsConventions": [3, 5], "solde": 9134}, {"id": 26, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2009, "ice": 2006, "telephone": "7962051386", "gsm": "4675203987", "adresse1": "51 Ridgeview Road", "adresse2": "195 Caliangt Place", "ville": 4, "soldeDepart": 1354, "plafond": 163786, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [7, 5], "beneficiaires": [4, 8], "desactive": false, "identifiantsConventions": [7, 6], "solde": 9133}, {"id": 27, "prenom": "<PERSON><PERSON>", "nom": "O'<PERSON>", "email": "<EMAIL>", "cin": 2006, "ice": 1984, "telephone": "1608405721", "gsm": "5825606297", "adresse1": "32 Glendale Plaza", "adresse2": null, "ville": null, "soldeDepart": 71512, "plafond": 42451, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [9, 6], "beneficiaires": [2, 6], "desactive": false, "identifiantsConventions": [9, 9], "solde": 9337}, {"id": 28, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1992, "ice": 2001, "telephone": "3324617978", "gsm": "2139354176", "adresse1": "9755 Del Sol Park", "adresse2": null, "ville": null, "soldeDepart": 20507, "plafond": 26308, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [1, 1], "beneficiaires": [4, 5], "desactive": false, "identifiantsConventions": [3, 9], "solde": 4184}, {"id": 29, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2012, "ice": 1993, "telephone": "9043369207", "gsm": "7425315873", "adresse1": "935 Garrison Point", "adresse2": null, "ville": null, "soldeDepart": 98393, "plafond": 132062, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [6, 7], "beneficiaires": [2, 7], "desactive": false, "identifiantsConventions": [4, 7], "solde": 3880}, {"id": 30, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2001, "ice": 2008, "telephone": "6908583509", "gsm": "4685257142", "adresse1": "020 Pleasure Terrace", "adresse2": null, "ville": null, "soldeDepart": 140860, "plafond": 70513, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [8, 4], "beneficiaires": [2, 6], "desactive": false, "identifiantsConventions": [4, 2], "solde": 9197}, {"id": 31, "prenom": "<PERSON>", "nom": "<PERSON>gley", "email": "<EMAIL>", "cin": 2002, "ice": 2011, "telephone": "9816709135", "gsm": "1278337323", "adresse1": "5 Sycamore Alley", "adresse2": null, "ville": null, "soldeDepart": 119543, "plafond": 25083, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [4, 2], "beneficiaires": [2, 5], "desactive": false, "identifiantsConventions": [1, 4], "solde": 5321}, {"id": 32, "prenom": "Camile", "nom": "Lineen", "email": "<EMAIL>", "cin": 1969, "ice": 2000, "telephone": "5924275983", "gsm": "7284071828", "adresse1": "145 Forest Dale Circle", "adresse2": null, "ville": null, "soldeDepart": 38069, "plafond": 179311, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [1, 1], "beneficiaires": [5, 2], "desactive": false, "identifiantsConventions": [3, 4], "solde": 5758}, {"id": 33, "prenom": "<PERSON>", "nom": "Corby", "email": "<EMAIL>", "cin": 2011, "ice": 1967, "telephone": "8839691272", "gsm": "8843179766", "adresse1": "2 Quincy Park", "adresse2": null, "ville": null, "soldeDepart": 163157, "plafond": 118864, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [9, 7], "beneficiaires": [2, 9], "desactive": false, "identifiantsConventions": [1, 5], "solde": 9195}, {"id": 34, "prenom": "<PERSON><PERSON>", "nom": "Boules", "email": "<EMAIL>", "cin": 2002, "ice": 2012, "telephone": "9159875421", "gsm": "4439505153", "adresse1": "0301 Oakridge Hill", "adresse2": "2966 Glendale Plaza", "ville": 1, "soldeDepart": 199111, "plafond": 80342, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [4, 3], "beneficiaires": [2, 2], "desactive": false, "identifiantsConventions": [3, 5], "solde": 5799}, {"id": 35, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1990, "ice": 2007, "telephone": "3495214409", "gsm": "5106862094", "adresse1": "95409 Moulton Place", "adresse2": "9 Glacier Hill Lane", "ville": 10, "soldeDepart": 99159, "plafond": 143954, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [5, 5], "beneficiaires": [6, 6], "desactive": false, "identifiantsConventions": [5, 1], "solde": 5580}, {"id": 36, "prenom": "R<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1997, "ice": 1994, "telephone": "1626647709", "gsm": "9026000926", "adresse1": "5 Almo Junction", "adresse2": null, "ville": null, "soldeDepart": 193377, "plafond": 189820, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [5, 8], "beneficiaires": [6, 6], "desactive": false, "identifiantsConventions": [5, 4], "solde": 3741}, {"id": 37, "prenom": "<PERSON><PERSON>", "nom": "Gilbride", "email": "<EMAIL>", "cin": 1993, "ice": 1998, "telephone": "6248483784", "gsm": "6998170681", "adresse1": "2 Sachtjen Junction", "adresse2": null, "ville": null, "soldeDepart": 167825, "plafond": 88030, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [5, 5], "beneficiaires": [3, 5], "desactive": false, "identifiantsConventions": [3, 6], "solde": 2646}, {"id": 38, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2002, "ice": 1994, "telephone": "7679272589", "gsm": "3556165328", "adresse1": "499 Moose Plaza", "adresse2": "22 South Road", "ville": 5, "soldeDepart": 51465, "plafond": 133991, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [9, 9], "beneficiaires": [1, 1], "desactive": false, "identifiantsConventions": [2, 4], "solde": 2029}, {"id": 39, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1987, "ice": 2010, "telephone": "7104970450", "gsm": "3505380611", "adresse1": "019 Raven Point", "adresse2": "650 Ramsey Place", "ville": 5, "soldeDepart": 148505, "plafond": 171612, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [3, 1], "beneficiaires": [2, 8], "desactive": false, "identifiantsConventions": [7, 7], "solde": 2555}, {"id": 40, "prenom": "Urs<PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2003, "ice": 2005, "telephone": "5705118408", "gsm": "9695445750", "adresse1": "309 Stephen Junction", "adresse2": "7659 Manufacturers Drive", "ville": 10, "soldeDepart": 192429, "plafond": 160130, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [5, 3], "beneficiaires": [4, 1], "desactive": false, "identifiantsConventions": [8, 3], "solde": 9606}, {"id": 41, "prenom": "Bern<PERSON>", "nom": "Giacomuzzi", "email": "<EMAIL>", "cin": 2003, "ice": 2012, "telephone": "2496870408", "gsm": "5115319542", "adresse1": "1 East Road", "adresse2": null, "ville": null, "soldeDepart": 163208, "plafond": 18329, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [6, 2], "beneficiaires": [2, 1], "desactive": false, "identifiantsConventions": [1, 7], "solde": 9900}, {"id": 42, "prenom": "<PERSON><PERSON>", "nom": "Penvarden", "email": "<EMAIL>", "cin": 1999, "ice": 1995, "telephone": "3214301009", "gsm": "6844452636", "adresse1": "74 Clemons Terrace", "adresse2": null, "ville": null, "soldeDepart": 110573, "plafond": 37089, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [2, 3], "beneficiaires": [2, 2], "desactive": false, "identifiantsConventions": [8, 1], "solde": 3093}, {"id": 43, "prenom": "Luz", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1992, "ice": 2005, "telephone": "6865199103", "gsm": "3057221187", "adresse1": "571 Arrowood Point", "adresse2": null, "ville": null, "soldeDepart": 70435, "plafond": 142104, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [4, 6], "beneficiaires": [8, 8], "desactive": false, "identifiantsConventions": [7, 5], "solde": 3005}, {"id": 44, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1970, "ice": 1997, "telephone": "1513308522", "gsm": "8638442368", "adresse1": "13 Bartillon Park", "adresse2": null, "ville": null, "soldeDepart": 17346, "plafond": 148596, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [6, 6], "beneficiaires": [7, 6], "desactive": false, "identifiantsConventions": [1, 9], "solde": 2030}, {"id": 45, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1971, "ice": 2012, "telephone": "5693989244", "gsm": "6387125010", "adresse1": "73 Ridge Oak Hill", "adresse2": "89796 Kedzie Drive", "ville": 6, "soldeDepart": 182286, "plafond": 80366, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [1, 2], "beneficiaires": [9, 9], "desactive": false, "identifiantsConventions": [1, 5], "solde": 3977}, {"id": 46, "prenom": "<PERSON>", "nom": "<PERSON>", "email": "<EMAIL>", "cin": 1992, "ice": 1993, "telephone": "7718511169", "gsm": "5925679418", "adresse1": "9917 Michigan Center", "adresse2": "8 Columbus Center", "ville": 1, "soldeDepart": 137894, "plafond": 43199, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [1, 3], "beneficiaires": [8, 9], "desactive": false, "identifiantsConventions": [1, 5], "solde": 9227}, {"id": 47, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2009, "ice": 2008, "telephone": "8539078414", "gsm": "4268995468", "adresse1": "6841 Bashford Alley", "adresse2": "73532 Brickson Park Way", "ville": 10, "soldeDepart": 119065, "plafond": 63598, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [3, 8], "beneficiaires": [5, 2], "desactive": false, "identifiantsConventions": [8, 7], "solde": 7036}, {"id": 48, "prenom": "<PERSON><PERSON>", "nom": "Mounfield", "email": "<EMAIL>", "cin": 2009, "ice": 2003, "telephone": "1975856829", "gsm": "9565825317", "adresse1": "8 Loomis Road", "adresse2": "1 Oak Alley", "ville": 3, "soldeDepart": 51720, "plafond": 27011, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [3, 2], "beneficiaires": [1, 6], "desactive": false, "identifiantsConventions": [7, 4], "solde": 1410}, {"id": 49, "prenom": "Nettle", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2008, "ice": 2010, "telephone": "2957508430", "gsm": "6055235527", "adresse1": "8188 Memorial Street", "adresse2": null, "ville": null, "soldeDepart": 153455, "plafond": 16650, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [3, 8], "beneficiaires": [1, 1], "desactive": false, "identifiantsConventions": [5, 2], "solde": 4504}, {"id": 50, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1997, "ice": 1995, "telephone": "8267890385", "gsm": "6972306675", "adresse1": "16385 7th Center", "adresse2": null, "ville": null, "soldeDepart": 19813, "plafond": 85959, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [7, 7], "beneficiaires": [6, 3], "desactive": false, "identifiantsConventions": [1, 8], "solde": 8550}, {"id": 51, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2007, "ice": 1994, "telephone": "2875209790", "gsm": "7401642036", "adresse1": "1220 Kinsman Way", "adresse2": null, "ville": null, "soldeDepart": 23493, "plafond": 8881, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [3, 5], "beneficiaires": [4, 8], "desactive": false, "identifiantsConventions": [7, 3], "solde": 8797}, {"id": 52, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1996, "ice": 1992, "telephone": "8207124208", "gsm": "8458904336", "adresse1": "43 Troy Junction", "adresse2": null, "ville": null, "soldeDepart": 91505, "plafond": 196422, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [3, 1], "beneficiaires": [4, 3], "desactive": false, "identifiantsConventions": [4, 8], "solde": 6680}, {"id": 53, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1996, "ice": 2004, "telephone": "9449921855", "gsm": "4631075586", "adresse1": "3 Acker Pass", "adresse2": null, "ville": null, "soldeDepart": 42429, "plafond": 99677, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [8, 7], "beneficiaires": [9, 4], "desactive": false, "identifiantsConventions": [9, 2], "solde": 3997}, {"id": 54, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1996, "ice": 1998, "telephone": "8735259357", "gsm": "1093816972", "adresse1": "6321 Leroy Parkway", "adresse2": null, "ville": null, "soldeDepart": 40365, "plafond": 78930, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [2, 5], "beneficiaires": [8, 3], "desactive": false, "identifiantsConventions": [7, 4], "solde": 7730}, {"id": 55, "prenom": "Lisetta", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1995, "ice": 1992, "telephone": "7966804241", "gsm": "9679751202", "adresse1": "68141 Eastwood Center", "adresse2": "19 Almo Parkway", "ville": 5, "soldeDepart": 54803, "plafond": 61835, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [5, 8], "beneficiaires": [2, 5], "desactive": false, "identifiantsConventions": [5, 7], "solde": 7169}, {"id": 56, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1988, "ice": 2012, "telephone": "6783447805", "gsm": "1671591153", "adresse1": "55098 Orin Road", "adresse2": null, "ville": null, "soldeDepart": 139463, "plafond": 120632, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [7, 3], "beneficiaires": [2, 6], "desactive": false, "identifiantsConventions": [4, 5], "solde": 8143}, {"id": 57, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2012, "ice": 1997, "telephone": "4612244973", "gsm": "9436163585", "adresse1": "695 Talisman Drive", "adresse2": "4 Victoria Alley", "ville": 5, "soldeDepart": 147283, "plafond": 23330, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [1, 2], "beneficiaires": [6, 9], "desactive": false, "identifiantsConventions": [3, 6], "solde": 1339}, {"id": 58, "prenom": "Del", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2000, "ice": 2003, "telephone": "2513113545", "gsm": "4307764148", "adresse1": "011 Hudson Hill", "adresse2": "95328 Forster Park", "ville": 2, "soldeDepart": 64617, "plafond": 170250, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [9, 5], "beneficiaires": [1, 1], "desactive": false, "identifiantsConventions": [1, 1], "solde": 4122}, {"id": 59, "prenom": "Bone", "nom": "Brekonridge", "email": "<EMAIL>", "cin": 2004, "ice": 1993, "telephone": "4232050974", "gsm": "9802225974", "adresse1": "067 Sloan Way", "adresse2": null, "ville": null, "soldeDepart": 187142, "plafond": 186069, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [2, 1], "beneficiaires": [6, 8], "desactive": false, "identifiantsConventions": [4, 6], "solde": 7931}, {"id": 60, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1993, "ice": 1992, "telephone": "7544165739", "gsm": "6559671769", "adresse1": "7278 Canary Lane", "adresse2": null, "ville": null, "soldeDepart": 163784, "plafond": 163628, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [8, 4], "beneficiaires": [9, 9], "desactive": false, "identifiantsConventions": [2, 7], "solde": 8540}, {"id": 61, "prenom": "Sargent", "nom": "Yellowlees", "email": "<EMAIL>", "cin": 2012, "ice": 1976, "telephone": "1927629239", "gsm": "4003858567", "adresse1": "8 Sunnyside Pass", "adresse2": null, "ville": null, "soldeDepart": 129104, "plafond": 174469, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [4, 7], "beneficiaires": [3, 5], "desactive": false, "identifiantsConventions": [5, 5], "solde": 6153}, {"id": 62, "prenom": "Mal", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2006, "ice": 2001, "telephone": "1681997930", "gsm": "3706355472", "adresse1": "33 <PERSON> Hill", "adresse2": null, "ville": null, "soldeDepart": 166465, "plafond": 6602, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [7, 8], "beneficiaires": [3, 6], "desactive": false, "identifiantsConventions": [8, 9], "solde": 4263}, {"id": 63, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2009, "ice": 1973, "telephone": "4954642947", "gsm": "5173749746", "adresse1": "82189 Superior Place", "adresse2": "8942 Crownhardt Alley", "ville": 3, "soldeDepart": 4018, "plafond": 65623, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [2, 2], "beneficiaires": [7, 6], "desactive": false, "identifiantsConventions": [2, 3], "solde": 5744}, {"id": 64, "prenom": "Flo", "nom": "Sep<PERSON>y", "email": "<EMAIL>", "cin": 1999, "ice": 1990, "telephone": "3831670252", "gsm": "2375299779", "adresse1": "462 Blackbird Court", "adresse2": null, "ville": null, "soldeDepart": 183012, "plafond": 154975, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [9, 8], "beneficiaires": [6, 3], "desactive": false, "identifiantsConventions": [7, 4], "solde": 9318}, {"id": 65, "prenom": "<PERSON><PERSON>", "nom": "Storah", "email": "<EMAIL>", "cin": 1957, "ice": 2013, "telephone": "4214973388", "gsm": "8319126157", "adresse1": "6 Scofield Park", "adresse2": null, "ville": null, "soldeDepart": 35188, "plafond": 128794, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [9, 5], "beneficiaires": [8, 9], "desactive": false, "identifiantsConventions": [9, 7], "solde": 6984}, {"id": 66, "prenom": "Tersina", "nom": "Fosten", "email": "<EMAIL>", "cin": 2002, "ice": 1991, "telephone": "7713984991", "gsm": "2103531869", "adresse1": "59030 Mallard Junction", "adresse2": "4628 Thompson Plaza", "ville": 7, "soldeDepart": 12393, "plafond": 139882, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [1, 3], "beneficiaires": [1, 7], "desactive": false, "identifiantsConventions": [7, 3], "solde": 1432}, {"id": 67, "prenom": "<PERSON>", "nom": "Dix", "email": "<EMAIL>", "cin": 2007, "ice": 2007, "telephone": "6691844967", "gsm": "8892773457", "adresse1": "37474 Burrows Way", "adresse2": "74268 Nobel Center", "ville": 9, "soldeDepart": 73940, "plafond": 144819, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [6, 8], "beneficiaires": [6, 3], "desactive": false, "identifiantsConventions": [7, 4], "solde": 6383}, {"id": 68, "prenom": "<PERSON>i", "nom": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2006, "ice": 1995, "telephone": "4227274773", "gsm": "5563982865", "adresse1": "27 <PERSON><PERSON><PERSON>", "adresse2": null, "ville": null, "soldeDepart": 66837, "plafond": 30057, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [8, 8], "beneficiaires": [3, 7], "desactive": false, "identifiantsConventions": [5, 5], "solde": 9158}, {"id": 69, "prenom": "<PERSON><PERSON>", "nom": "Every", "email": "<EMAIL>", "cin": 1994, "ice": 2001, "telephone": "5352347364", "gsm": "6558459900", "adresse1": "755 Kropf Plaza", "adresse2": "5 Bultman Circle", "ville": 9, "soldeDepart": 169338, "plafond": 141274, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [1, 2], "beneficiaires": [1, 8], "desactive": false, "identifiantsConventions": [9, 5], "solde": 7318}, {"id": 70, "prenom": "Ho<PERSON>sia", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2008, "ice": 2009, "telephone": "2635820626", "gsm": "3511604116", "adresse1": "83228 Pawling Lane", "adresse2": "16 <PERSON>", "ville": 6, "soldeDepart": 158533, "plafond": 71822, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [1, 7], "beneficiaires": [7, 6], "desactive": false, "identifiantsConventions": [7, 6], "solde": 8158}, {"id": 71, "prenom": "<PERSON><PERSON>", "nom": "Le Brum", "email": "<EMAIL>", "cin": 2007, "ice": 2001, "telephone": "9991661736", "gsm": "4179898142", "adresse1": "1 Merrick Junction", "adresse2": null, "ville": null, "soldeDepart": 135216, "plafond": 55762, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [3, 9], "beneficiaires": [1, 2], "desactive": false, "identifiantsConventions": [5, 5], "solde": 2319}, {"id": 72, "prenom": "<PERSON>", "nom": "Egleton", "email": "<EMAIL>", "cin": 1989, "ice": 1997, "telephone": "6395228059", "gsm": "9803080395", "adresse1": "8465 Ruskin Terrace", "adresse2": null, "ville": null, "soldeDepart": 115466, "plafond": 42492, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [7, 5], "beneficiaires": [4, 8], "desactive": false, "identifiantsConventions": [9, 5], "solde": 3308}, {"id": 73, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1996, "ice": 2002, "telephone": "7447183710", "gsm": "5903054048", "adresse1": "22 Ridge Oak Road", "adresse2": null, "ville": null, "soldeDepart": 107054, "plafond": 173996, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [5, 5], "beneficiaires": [1, 4], "desactive": false, "identifiantsConventions": [7, 3], "solde": 2502}, {"id": 74, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Grabban", "email": "<EMAIL>", "cin": 2002, "ice": 2007, "telephone": "9896113152", "gsm": "9658696653", "adresse1": "47 Melody Circle", "adresse2": "678 Cascade Avenue", "ville": 8, "soldeDepart": 179561, "plafond": 179499, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [4, 5], "beneficiaires": [4, 3], "desactive": false, "identifiantsConventions": [5, 9], "solde": 5613}, {"id": 75, "prenom": "Edd", "nom": "E<PERSON>me", "email": "<EMAIL>", "cin": 1994, "ice": 2011, "telephone": "1849185048", "gsm": "7859076080", "adresse1": "0981 Holmberg Point", "adresse2": "40447 Kedzie Junction", "ville": 7, "soldeDepart": 64652, "plafond": 128708, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [6, 1], "beneficiaires": [2, 5], "desactive": false, "identifiantsConventions": [9, 5], "solde": 8493}, {"id": 76, "prenom": "<PERSON><PERSON>", "nom": "W<PERSON>by", "email": "<EMAIL>", "cin": 1996, "ice": 2005, "telephone": "3094378683", "gsm": "7716855678", "adresse1": "35158 Emmet Center", "adresse2": "0776 M<PERSON>rmick <PERSON>", "ville": 9, "soldeDepart": 121588, "plafond": 183583, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [1, 1], "beneficiaires": [7, 9], "desactive": false, "identifiantsConventions": [2, 8], "solde": 7877}, {"id": 77, "prenom": "<PERSON><PERSON>", "nom": "Beau<PERSON><PERSON>", "email": "<EMAIL>", "cin": 1997, "ice": 2010, "telephone": "3925949182", "gsm": "9536063691", "adresse1": "634 Welch Avenue", "adresse2": null, "ville": null, "soldeDepart": 187658, "plafond": 56139, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [5, 6], "beneficiaires": [2, 5], "desactive": false, "identifiantsConventions": [4, 8], "solde": 3216}, {"id": 78, "prenom": "<PERSON><PERSON>", "nom": "Bum", "email": "<EMAIL>", "cin": 2007, "ice": 2003, "telephone": "3805666346", "gsm": "2995396310", "adresse1": "77 Weeping Birch Hill", "adresse2": null, "ville": null, "soldeDepart": 70471, "plafond": 148683, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [2, 5], "beneficiaires": [4, 2], "desactive": false, "identifiantsConventions": [8, 9], "solde": 9958}, {"id": 79, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><EMAIL>", "cin": 2012, "ice": 2009, "telephone": "7812443173", "gsm": "5839733857", "adresse1": "5331 Blue Bill Park Road", "adresse2": "6 Kipling Parkway", "ville": 10, "soldeDepart": 107102, "plafond": 133737, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [5, 5], "beneficiaires": [6, 6], "desactive": false, "identifiantsConventions": [7, 6], "solde": 5474}, {"id": 80, "prenom": "<PERSON><PERSON>", "nom": "<PERSON>in", "email": "<EMAIL>", "cin": 2007, "ice": 2009, "telephone": "9249286025", "gsm": "1778921819", "adresse1": "852 Myrtle Park", "adresse2": null, "ville": null, "soldeDepart": 168968, "plafond": 123182, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [3, 7], "beneficiaires": [1, 1], "desactive": false, "identifiantsConventions": [1, 4], "solde": 3107}, {"id": 81, "prenom": "<PERSON>", "nom": "Doxey", "email": "<EMAIL>", "cin": 2003, "ice": 1997, "telephone": "4395117239", "gsm": "9878979116", "adresse1": "8409 Dennis Circle", "adresse2": "99759 Menomonie Street", "ville": 2, "soldeDepart": 120090, "plafond": 19552, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [7, 3], "beneficiaires": [7, 1], "desactive": false, "identifiantsConventions": [8, 3], "solde": 2464}, {"id": 82, "prenom": "<PERSON>", "nom": "Barras", "email": "<EMAIL>", "cin": 2001, "ice": 2007, "telephone": "3387694163", "gsm": "4395342784", "adresse1": "1 Roth Street", "adresse2": "58 Grayhawk Lane", "ville": 9, "soldeDepart": 19147, "plafond": 49272, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [6, 2], "beneficiaires": [1, 1], "desactive": false, "identifiantsConventions": [5, 7], "solde": 1513}, {"id": 83, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2001, "ice": 1996, "telephone": "2434036240", "gsm": "8421873095", "adresse1": "55 Warbler Plaza", "adresse2": null, "ville": null, "soldeDepart": 183119, "plafond": 87010, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [4, 3], "beneficiaires": [2, 8], "desactive": false, "identifiantsConventions": [6, 5], "solde": 4887}, {"id": 84, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2010, "ice": 2007, "telephone": "8013129766", "gsm": "3717553266", "adresse1": "1768 <PERSON>", "adresse2": "821 <PERSON>", "ville": 1, "soldeDepart": 198600, "plafond": 90070, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [1, 5], "beneficiaires": [9, 2], "desactive": false, "identifiantsConventions": [9, 3], "solde": 3107}, {"id": 85, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2001, "ice": 2010, "telephone": "4756469099", "gsm": "9462686223", "adresse1": "7140 1st Center", "adresse2": null, "ville": null, "soldeDepart": 1997, "plafond": 86025, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [6, 6], "beneficiaires": [7, 5], "desactive": false, "identifiantsConventions": [8, 8], "solde": 1392}, {"id": 86, "prenom": "Zsazsa", "nom": "Romand", "email": "<EMAIL>", "cin": 1993, "ice": 2010, "telephone": "4899698052", "gsm": "2173635631", "adresse1": "6595 Forster Court", "adresse2": "6 Sunnyside Drive", "ville": 3, "soldeDepart": 152381, "plafond": 14854, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [3, 9], "beneficiaires": [4, 5], "desactive": false, "identifiantsConventions": [2, 9], "solde": 2864}, {"id": 87, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1986, "ice": 1965, "telephone": "2065924772", "gsm": "8164435979", "adresse1": "172 Florence Trail", "adresse2": null, "ville": null, "soldeDepart": 65388, "plafond": 180749, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [3, 8], "beneficiaires": [6, 7], "desactive": false, "identifiantsConventions": [4, 1], "solde": 9764}, {"id": 88, "prenom": "Ingaborg", "nom": "Stonuary", "email": "<EMAIL>", "cin": 1994, "ice": 2013, "telephone": "5669163390", "gsm": "2004768665", "adresse1": "87237 Eastwood Avenue", "adresse2": "88 Russell Junction", "ville": 7, "soldeDepart": 181231, "plafond": 13788, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [6, 4], "beneficiaires": [5, 8], "desactive": false, "identifiantsConventions": [5, 5], "solde": 7961}, {"id": 89, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2009, "ice": 2001, "telephone": "5549237887", "gsm": "8828983092", "adresse1": "09 Ilene Center", "adresse2": null, "ville": null, "soldeDepart": 197818, "plafond": 185971, "typeRemise": "A", "tauxRemise": 1, "remisesParFts": [6, 1], "beneficiaires": [7, 7], "desactive": false, "identifiantsConventions": [5, 2], "solde": 1413}, {"id": 90, "prenom": "<PERSON>ull", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2001, "ice": 2013, "telephone": "6394094084", "gsm": "8801914045", "adresse1": "16 Division Terrace", "adresse2": null, "ville": null, "soldeDepart": 16971, "plafond": 129826, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [4, 5], "beneficiaires": [7, 2], "desactive": false, "identifiantsConventions": [6, 6], "solde": 8477}, {"id": 91, "prenom": "Jo<PERSON>", "nom": "Colbourn", "email": "<EMAIL>", "cin": 2005, "ice": 1998, "telephone": "9733958478", "gsm": "9655768186", "adresse1": "9 Logan Avenue", "adresse2": "412 Crownhardt Parkway", "ville": 2, "soldeDepart": 132023, "plafond": 129745, "typeRemise": "A", "tauxRemise": 0, "remisesParFts": [2, 6], "beneficiaires": [1, 9], "desactive": false, "identifiantsConventions": [7, 3], "solde": 6497}, {"id": 92, "prenom": "<PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2011, "ice": 2005, "telephone": "2025951631", "gsm": "6193005254", "adresse1": "6 Springview Circle", "adresse2": null, "ville": null, "soldeDepart": 39789, "plafond": 167450, "typeRemise": "F", "tauxRemise": 0, "remisesParFts": [2, 1], "beneficiaires": [5, 1], "desactive": false, "identifiantsConventions": [5, 4], "solde": 2478}, {"id": 93, "prenom": "<PERSON><PERSON>", "nom": "Repp", "email": "<EMAIL>", "cin": 2007, "ice": 2003, "telephone": "5551672308", "gsm": "1862731933", "adresse1": "04219 <PERSON>", "adresse2": null, "ville": null, "soldeDepart": 190891, "plafond": 179714, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [8, 7], "beneficiaires": [8, 3], "desactive": false, "identifiantsConventions": [7, 8], "solde": 9795}, {"id": 94, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1991, "ice": 1999, "telephone": "4687691258", "gsm": "9758219397", "adresse1": "94 Killdeer Court", "adresse2": null, "ville": null, "soldeDepart": 187550, "plafond": 137466, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [9, 9], "beneficiaires": [8, 3], "desactive": false, "identifiantsConventions": [8, 8], "solde": 6850}, {"id": 95, "prenom": "<PERSON>", "nom": "<PERSON><PERSON>", "email": "<EMAIL>", "cin": 2007, "ice": 1994, "telephone": "2788690674", "gsm": "9771497757", "adresse1": "44 Kipling Plaza", "adresse2": "307 Atwood Lane", "ville": 3, "soldeDepart": 47404, "plafond": 108106, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [4, 1], "beneficiaires": [9, 7], "desactive": false, "identifiantsConventions": [5, 3], "solde": 8669}, {"id": 96, "prenom": "<PERSON>", "nom": "Walkling", "email": "<EMAIL>", "cin": 1993, "ice": 1991, "telephone": "8502271091", "gsm": "7162732836", "adresse1": "91382 Shoshone Pass", "adresse2": "54 Graedel Drive", "ville": 7, "soldeDepart": 40280, "plafond": 9990, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [3, 4], "beneficiaires": [7, 5], "desactive": false, "identifiantsConventions": [7, 9], "solde": 4826}, {"id": 97, "prenom": "Delila", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1996, "ice": 1997, "telephone": "6614067932", "gsm": "1459651930", "adresse1": "0388 Darwin Point", "adresse2": null, "ville": null, "soldeDepart": 197727, "plafond": 185470, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [6, 6], "beneficiaires": [7, 3], "desactive": false, "identifiantsConventions": [1, 3], "solde": 7731}, {"id": 98, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 1995, "ice": 1993, "telephone": "6265906138", "gsm": "4343589759", "adresse1": "6 Vidon Circle", "adresse2": "4167 Green Park", "ville": 5, "soldeDepart": 55319, "plafond": 95282, "typeRemise": "G", "tauxRemise": 0, "remisesParFts": [5, 9], "beneficiaires": [9, 5], "desactive": false, "identifiantsConventions": [8, 7], "solde": 1022}, {"id": 99, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Bloxland", "email": "<EMAIL>", "cin": 1993, "ice": 2006, "telephone": "3629608816", "gsm": "1969542677", "adresse1": "34415 Myrtle Court", "adresse2": null, "ville": null, "soldeDepart": 25681, "plafond": 177876, "typeRemise": "G", "tauxRemise": 1, "remisesParFts": [4, 1], "beneficiaires": [5, 3], "desactive": false, "identifiantsConventions": [8, 5], "solde": 1316}, {"id": 100, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "cin": 2005, "ice": 2008, "telephone": "1566104216", "gsm": "8708643842", "adresse1": "17 Canary Avenue", "adresse2": null, "ville": null, "soldeDepart": 24521, "plafond": 47350, "typeRemise": "F", "tauxRemise": 1, "remisesParFts": [2, 4], "beneficiaires": [1, 8], "desactive": false, "identifiantsConventions": [7, 3], "solde": 2269}], "remisesParFts": [{"id": 1, "ft": 1, "tauxRemise": 0.47}, {"id": 2, "ft": 2, "tauxRemise": 0.66}, {"id": 3, "ft": 3, "tauxRemise": 0.07}, {"id": 4, "ft": 4, "tauxRemise": 0.83}, {"id": 5, "ft": 5, "tauxRemise": 0.01}], "beneficiaires": [{"id": 1, "prenom": "Adler", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 1047, "lienParente": "HDP"}, {"id": 2, "prenom": "<PERSON>", "nom": "Crann", "cin": 2318, "lienParente": "GYRO"}, {"id": 3, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 1468, "lienParente": "HRMNU"}, {"id": 4, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 3906, "lienParente": "CVLT"}, {"id": 5, "prenom": "Bone", "nom": "<PERSON><PERSON><PERSON>", "cin": 1726, "lienParente": "WTFCW"}, {"id": 6, "prenom": "Tiler", "nom": "<PERSON><PERSON>", "cin": 9209, "lienParente": "CHK"}, {"id": 7, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 5471, "lienParente": "HIHO"}, {"id": 8, "prenom": "<PERSON><PERSON>", "nom": "Chatband", "cin": 7916, "lienParente": "FMO"}, {"id": 9, "prenom": "<PERSON><PERSON>", "nom": "Spradbrow", "cin": 1876, "lienParente": "SMHI"}, {"id": 10, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 8684, "lienParente": "CBO"}, {"id": 11, "prenom": "<PERSON><PERSON>", "nom": "Sidon", "cin": 2301, "lienParente": "PES"}, {"id": 12, "prenom": "Jessamine", "nom": "Corroyer", "cin": 4495, "lienParente": "NEE^K"}, {"id": 13, "prenom": "<PERSON>erta", "nom": "Rodgier", "cin": 1048, "lienParente": "GEOS"}, {"id": 14, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Southby", "cin": 2964, "lienParente": "WFC.WS"}, {"id": 15, "prenom": "<PERSON>", "nom": "Mourge", "cin": 3685, "lienParente": "WSBC"}, {"id": 16, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Rizon", "cin": 3681, "lienParente": "MFA"}, {"id": 17, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 1634, "lienParente": "GDI"}, {"id": 18, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 6064, "lienParente": "PRMW"}, {"id": 19, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 4724, "lienParente": "NEA"}, {"id": 20, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 8351, "lienParente": "INN^B"}, {"id": 21, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 9222, "lienParente": "AHPA"}, {"id": 22, "prenom": "Torrey", "nom": "Naisbit", "cin": 8389, "lienParente": "EVGBC"}, {"id": 23, "prenom": "Chariot", "nom": "<PERSON>ore", "cin": 8930, "lienParente": "RBS^L"}, {"id": 24, "prenom": "<PERSON>", "nom": "<PERSON>", "cin": 8862, "lienParente": "VONV"}, {"id": 25, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 8448, "lienParente": "MOMO"}, {"id": 26, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 5232, "lienParente": "IGF"}, {"id": 27, "prenom": "<PERSON>", "nom": "Blonden", "cin": 6406, "lienParente": "ETH"}, {"id": 28, "prenom": "Morissa", "nom": "Stanion", "cin": 9903, "lienParente": "UTF"}, {"id": 29, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 6484, "lienParente": "RQI"}, {"id": 30, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 1381, "lienParente": "BAC^L"}, {"id": 31, "prenom": "<PERSON>", "nom": "<PERSON><PERSON>ingworth", "cin": 2608, "lienParente": "FTXH"}, {"id": 32, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 7064, "lienParente": "SCE^K"}, {"id": 33, "prenom": "<PERSON><PERSON>", "nom": "Sparham", "cin": 7373, "lienParente": "WFC^T"}, {"id": 34, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON>", "cin": 3545, "lienParente": "WPM"}, {"id": 35, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 8985, "lienParente": "CVTI"}, {"id": 36, "prenom": "<PERSON><PERSON>", "nom": "Cast", "cin": 9043, "lienParente": "ITUS"}, {"id": 37, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 3827, "lienParente": "FTLB"}, {"id": 38, "prenom": "Iseabal", "nom": "<PERSON>", "cin": 3902, "lienParente": "BPFHP"}, {"id": 39, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Bohike", "cin": 9201, "lienParente": "KCG"}, {"id": 40, "prenom": "<PERSON><PERSON>", "nom": "McGann", "cin": 3905, "lienParente": "DRIO"}, {"id": 41, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 4777, "lienParente": "FSIC"}, {"id": 42, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 9631, "lienParente": "TROW"}, {"id": 43, "prenom": "Delmore", "nom": "Mitkcov", "cin": 5889, "lienParente": "INT"}, {"id": 44, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 5676, "lienParente": "NBIX"}, {"id": 45, "prenom": "<PERSON>", "nom": "Masterman", "cin": 8175, "lienParente": "EVK"}, {"id": 46, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 2868, "lienParente": "VECO"}, {"id": 47, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 3235, "lienParente": "AB"}, {"id": 48, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 3636, "lienParente": "MER^K"}, {"id": 49, "prenom": "<PERSON>z<PERSON>ald<PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cin": 7924, "lienParente": "HABT"}, {"id": 50, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Skirven", "cin": 8602, "lienParente": "KGJI"}, {"id": 51, "prenom": "<PERSON>", "nom": "Brose", "cin": 7132, "lienParente": "FOGO"}, {"id": 52, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 4901, "lienParente": "CUBI"}, {"id": 53, "prenom": "<PERSON>", "nom": "Hen<PERSON><PERSON>", "cin": 7560, "lienParente": "OIBR.C"}, {"id": 54, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 8579, "lienParente": "FBR"}, {"id": 55, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 8746, "lienParente": "RAS^A"}, {"id": 56, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 3198, "lienParente": "WFE^A"}, {"id": 57, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 8958, "lienParente": "CHRS"}, {"id": 58, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Artingstall", "cin": 9818, "lienParente": "INBKL"}, {"id": 59, "prenom": "<PERSON><PERSON>", "nom": "Whines", "cin": 4009, "lienParente": "ADOM"}, {"id": 60, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 9614, "lienParente": "HK"}, {"id": 61, "prenom": "Buffy", "nom": "Audiss", "cin": 1441, "lienParente": "GNC"}, {"id": 62, "prenom": "<PERSON>e", "nom": "Patience", "cin": 6621, "lienParente": "DAVE"}, {"id": 63, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 8864, "lienParente": "FEP"}, {"id": 64, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 4373, "lienParente": "LKOR"}, {"id": 65, "prenom": "<PERSON><PERSON>", "nom": "Darling", "cin": 6604, "lienParente": "FF"}, {"id": 66, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Armit", "cin": 1321, "lienParente": "GS^J"}, {"id": 67, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Croutear", "cin": 7766, "lienParente": "ANY"}, {"id": 68, "prenom": "Brion", "nom": "<PERSON><PERSON><PERSON>", "cin": 6783, "lienParente": "PLAB"}, {"id": 69, "prenom": "<PERSON><PERSON>", "nom": "Greson", "cin": 8704, "lienParente": "CCV"}, {"id": 70, "prenom": "<PERSON>", "nom": "Alyoshin", "cin": 7405, "lienParente": "MUI"}, {"id": 71, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 6980, "lienParente": "GOOD"}, {"id": 72, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 7062, "lienParente": "FITBI"}, {"id": 73, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 9091, "lienParente": "PFBX"}, {"id": 74, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 8278, "lienParente": "MBFIP"}, {"id": 75, "prenom": "<PERSON><PERSON>", "nom": "Treadgold", "cin": 3570, "lienParente": "STAG^B"}, {"id": 76, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Overland", "cin": 1663, "lienParente": "GTY"}, {"id": 77, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 2874, "lienParente": "NSIT"}, {"id": 78, "prenom": "Roma", "nom": "Plank", "cin": 7636, "lienParente": "TTF"}, {"id": 79, "prenom": "Jordan", "nom": "<PERSON><PERSON>", "cin": 9927, "lienParente": "REG"}, {"id": 80, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 9385, "lienParente": "NIQ"}, {"id": 81, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 1939, "lienParente": "EA"}, {"id": 82, "prenom": "Alberta", "nom": "<PERSON><PERSON><PERSON>", "cin": 7891, "lienParente": "BBT^E"}, {"id": 83, "prenom": "Flss", "nom": "<PERSON><PERSON>", "cin": 1702, "lienParente": "SENEA"}, {"id": 84, "prenom": "Fr<PERSON><PERSON><PERSON>", "nom": "Loadwick", "cin": 7512, "lienParente": "TAPR"}, {"id": 85, "prenom": "<PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 9274, "lienParente": "MRTN"}, {"id": 86, "prenom": "Roxine", "nom": "<PERSON><PERSON>", "cin": 3116, "lienParente": "EGRX"}, {"id": 87, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Lote", "cin": 9547, "lienParente": "XRF"}, {"id": 88, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 5066, "lienParente": "JBHT"}, {"id": 89, "prenom": "<PERSON>", "nom": "<PERSON><PERSON>", "cin": 3565, "lienParente": "AXP"}, {"id": 90, "prenom": "<PERSON><PERSON><PERSON><PERSON>", "nom": "<PERSON>t", "cin": 8858, "lienParente": "ATLC"}, {"id": 91, "prenom": "Caspar", "nom": "<PERSON><PERSON><PERSON><PERSON>", "cin": 2969, "lienParente": "BG"}, {"id": 92, "prenom": "<PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 6989, "lienParente": "OFC"}, {"id": 93, "prenom": "<PERSON><PERSON><PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 6024, "lienParente": "TXRH"}, {"id": 94, "prenom": "Chip", "nom": "<PERSON><PERSON>", "cin": 2972, "lienParente": "PFK"}, {"id": 95, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 1550, "lienParente": "CYRXW"}, {"id": 96, "prenom": "<PERSON><PERSON><PERSON>", "nom": "Domengue", "cin": 4772, "lienParente": "BIIB"}, {"id": 97, "prenom": "<PERSON><PERSON>", "nom": "Tinniswood", "cin": 6748, "lienParente": "FPRX"}, {"id": 98, "prenom": "L<PERSON>tte", "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cin": 3975, "lienParente": "AAC"}, {"id": 99, "prenom": "Elysia", "nom": "Prator", "cin": 8494, "lienParente": "ANH^A"}, {"id": 100, "prenom": "Maigh<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 5083, "lienParente": "DDC"}], "operateurs": [{"id": 1, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cin": 4364, "lienParente": "IPI"}, {"id": 2, "prenom": "Malinda", "nom": "Broadbear", "cin": 1788, "lienParente": "EAI"}, {"id": 3, "prenom": "Isabella", "nom": "Babbage", "cin": 8380, "lienParente": "MGPI"}, {"id": 4, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON><PERSON>", "cin": 5804, "lienParente": "SBBP"}, {"id": 5, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 6282, "lienParente": "MMDMW"}, {"id": 6, "prenom": "<PERSON><PERSON>", "nom": "<PERSON><PERSON>", "cin": 5974, "lienParente": "SHO^F"}, {"id": 7, "prenom": "<PERSON><PERSON>", "nom": "Wedderburn", "cin": 7329, "lienParente": "TBPH"}, {"id": 8, "prenom": "<PERSON><PERSON>", "nom": "Gadsdon", "cin": 1976, "lienParente": "KSS"}, {"id": 9, "prenom": "<PERSON>", "nom": "Santo<PERSON>", "cin": 3702, "lienParente": "LUX"}, {"id": 10, "prenom": "<PERSON>", "nom": "Moden", "cin": 2089, "lienParente": "IP"}], "organismes": [{"id": 1, "raisonSociale": "Flashset"}, {"id": 2, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 3, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 4, "raisonSociale": "<PERSON><PERSON>"}, {"id": 5, "raisonSociale": "Vipe"}, {"id": 6, "raisonSociale": "Mybuzz"}, {"id": 7, "raisonSociale": "Feedspan"}, {"id": 8, "raisonSociale": "Riffpedia"}, {"id": 9, "raisonSociale": "Ski<PERSON><PERSON>"}, {"id": 10, "raisonSociale": "Zooveo"}, {"id": 11, "raisonSociale": "Flashspan"}, {"id": 12, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 13, "raisonSociale": "Blogspan"}, {"id": 14, "raisonSociale": "Yabox"}, {"id": 15, "raisonSociale": "<PERSON><PERSON>"}, {"id": 16, "raisonSociale": "Ski<PERSON><PERSON>"}, {"id": 17, "raisonSociale": "Bluejam"}, {"id": 18, "raisonSociale": "Realmix"}, {"id": 19, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 20, "raisonSociale": "Trilith"}, {"id": 21, "raisonSociale": "Jabbersphere"}, {"id": 22, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 23, "raisonSociale": "Jabberstorm"}, {"id": 24, "raisonSociale": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 25, "raisonSociale": "Cogibox"}, {"id": 26, "raisonSociale": "Linkbuzz"}, {"id": 27, "raisonSociale": "<PERSON><PERSON>"}, {"id": 28, "raisonSociale": "Yamia"}, {"id": 29, "raisonSociale": "Brows<PERSON><PERSON>"}, {"id": 30, "raisonSociale": "<PERSON><PERSON>"}, {"id": 31, "raisonSociale": "Oyoyo"}, {"id": 32, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 33, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 34, "raisonSociale": "Skyndu"}, {"id": 35, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 36, "raisonSociale": "Rhynyx"}, {"id": 37, "raisonSociale": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 38, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 39, "raisonSociale": "Dynabox"}, {"id": 40, "raisonSociale": "Demimbu"}, {"id": 41, "raisonSociale": "Jaxbean"}, {"id": 42, "raisonSociale": "<PERSON><PERSON>"}, {"id": 43, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 44, "raisonSociale": "Oyoba"}, {"id": 45, "raisonSociale": "<PERSON><PERSON>"}, {"id": 46, "raisonSociale": "Browsezoom"}, {"id": 47, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 48, "raisonSociale": "Dabtype"}, {"id": 49, "raisonSociale": "Quaxo"}, {"id": 50, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 51, "raisonSociale": "Myworks"}, {"id": 52, "raisonSociale": "<PERSON><PERSON>"}, {"id": 53, "raisonSociale": "Eamia"}, {"id": 54, "raisonSociale": "Edgeclub"}, {"id": 55, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 56, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 57, "raisonSociale": "Feedfish"}, {"id": 58, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 59, "raisonSociale": "Jabberstorm"}, {"id": 60, "raisonSociale": "Wordpedia"}, {"id": 61, "raisonSociale": "Mycat"}, {"id": 62, "raisonSociale": "Jabbersphere"}, {"id": 63, "raisonSociale": "Livetube"}, {"id": 64, "raisonSociale": "<PERSON><PERSON>"}, {"id": 65, "raisonSociale": "Browsebug"}, {"id": 66, "raisonSociale": "Photolist"}, {"id": 67, "raisonSociale": "Omba"}, {"id": 68, "raisonSociale": "Podcat"}, {"id": 69, "raisonSociale": "Blogpad"}, {"id": 70, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 71, "raisonSociale": "Twitterwire"}, {"id": 72, "raisonSociale": "<PERSON><PERSON>"}, {"id": 73, "raisonSociale": "You<PERSON>"}, {"id": 74, "raisonSociale": "Blogpad"}, {"id": 75, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 76, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 77, "raisonSociale": "Skyndu"}, {"id": 78, "raisonSociale": "Flipstorm"}, {"id": 79, "raisonSociale": "Skyndu"}, {"id": 80, "raisonSociale": "<PERSON><PERSON>"}, {"id": 81, "raisonSociale": "<PERSON><PERSON>"}, {"id": 82, "raisonSociale": "Brows<PERSON><PERSON>"}, {"id": 83, "raisonSociale": "Digitube"}, {"id": 84, "raisonSociale": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 85, "raisonSociale": "<PERSON><PERSON><PERSON>"}, {"id": 86, "raisonSociale": "Thoughtstorm"}, {"id": 87, "raisonSociale": "Youopia"}, {"id": 88, "raisonSociale": "Fivebridge"}, {"id": 89, "raisonSociale": "Thoughtsphere"}, {"id": 90, "raisonSociale": "Skiptube"}, {"id": 91, "raisonSociale": "<PERSON><PERSON>"}, {"id": 92, "raisonSociale": "<PERSON><PERSON>"}, {"id": 93, "raisonSociale": "Yabox"}, {"id": 94, "raisonSociale": "Riffpedia"}, {"id": 95, "raisonSociale": "Twitterwire"}, {"id": 96, "raisonSociale": "Wordify"}, {"id": 97, "raisonSociale": "<PERSON><PERSON>"}, {"id": 98, "raisonSociale": "Rhynyx"}, {"id": 99, "raisonSociale": "Centimia"}, {"id": 100, "raisonSociale": "Divape"}], "historiqueEncaissements": [{"id": 1, "date": "2022-05-21T07:41:16Z", "designation": "vente", "qte": 52, "prix": 6146.38, "montant": 78839.37, "tauxRemise": 0.86, "operateur": 3, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 5667.79}, {"id": 2, "date": "2022-02-08T07:08:55Z", "designation": "vente", "qte": 179, "prix": 68704.55, "montant": 92438.98, "tauxRemise": 0.01, "operateur": 9, "beneficiaire": 2, "clientId": 2, "infoVente": 1, "cumul": 61104.87}, {"id": 3, "date": "2022-05-22T19:09:06Z", "designation": "Dabtype", "qte": 195, "prix": 45045.7, "montant": 63065.07, "tauxRemise": 0.44, "operateur": 6, "beneficiaire": 4, "clientId": 3, "infoVente": 1, "cumul": 4011.53}, {"id": 4, "date": "2022-05-20T21:11:07Z", "designation": "vente", "qte": 422, "prix": 26391.82, "montant": 35294.39, "tauxRemise": 0.83, "operateur": 1, "beneficiaire": 2, "clientId": 4, "infoVente": 1, "cumul": 26039.94}, {"id": 5, "date": "2021-11-20T07:26:35Z", "designation": "vente", "qte": 422, "prix": 94271.76, "montant": 32415.84, "tauxRemise": 0.51, "operateur": 4, "beneficiaire": 5, "clientId": 5, "infoVente": 1, "cumul": 12551.35}, {"id": 6, "date": "2021-08-01T08:13:48Z", "designation": "JumpXS", "qte": 301, "prix": 65065.79, "montant": 95264.82, "tauxRemise": 0.84, "operateur": 3, "beneficiaire": 2, "clientId": 6, "infoVente": 1, "cumul": 21411.06}, {"id": 7, "date": "2022-05-13T15:35:22Z", "designation": "Tagopia", "qte": 340, "prix": 67562.69, "montant": 36377.98, "tauxRemise": 0.73, "operateur": 9, "beneficiaire": 1, "clientId": 7, "infoVente": 1, "cumul": 6191.99}, {"id": 8, "date": "2021-12-15T17:49:05Z", "designation": "Bluejam", "qte": 261, "prix": 26493.74, "montant": 88802.51, "tauxRemise": 0.54, "operateur": 10, "beneficiaire": 4, "clientId": 8, "infoVente": 1, "cumul": 84014.54}, {"id": 9, "date": "2021-06-23T07:03:41Z", "designation": "Vipe", "qte": 396, "prix": 91572.6, "montant": 63055.09, "tauxRemise": 0.33, "operateur": 1, "beneficiaire": 3, "clientId": 5, "infoVente": 1, "cumul": 84611.87}, {"id": 10, "date": "2022-01-29T20:10:53Z", "designation": "Skyble", "qte": 259, "prix": 48735.16, "montant": 66167.84, "tauxRemise": 0.01, "operateur": 7, "beneficiaire": 4, "clientId": 6, "infoVente": 1, "cumul": 96274.27}, {"id": 11, "date": "2021-10-04T05:30:48Z", "designation": "<PERSON><PERSON><PERSON>", "qte": 41, "prix": 4232.81, "montant": 85215.34, "tauxRemise": 0.92, "operateur": 9, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 1492.68}, {"id": 12, "date": "2021-07-31T18:39:05Z", "designation": "DabZ", "qte": 448, "prix": 39477.66, "montant": 16557.99, "tauxRemise": 0.64, "operateur": 1, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 38687.48}, {"id": 13, "date": "2021-10-18T15:58:04Z", "designation": "<PERSON><PERSON>", "qte": 20, "prix": 83976.09, "montant": 79938.39, "tauxRemise": 0.75, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 92697.24}, {"id": 14, "date": "2022-02-18T19:17:39Z", "designation": "Pixonyx", "qte": 496, "prix": 13090.11, "montant": 85377.15, "tauxRemise": 0.25, "operateur": 10, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 83274.98}, {"id": 15, "date": "2022-02-25T05:23:56Z", "designation": "Wikibox", "qte": 494, "prix": 73005.61, "montant": 23666.95, "tauxRemise": 0.74, "operateur": 2, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 60931.07}, {"id": 16, "date": "2021-07-04T05:47:23Z", "designation": "Skibox", "qte": 11, "prix": 57528.13, "montant": 79443.02, "tauxRemise": 0.28, "operateur": 2, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 96686.35}, {"id": 17, "date": "2021-10-03T01:40:58Z", "designation": "Camimbo", "qte": 70, "prix": 71471.91, "montant": 92082.67, "tauxRemise": 0.5, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 99822.68}, {"id": 18, "date": "2021-10-14T19:35:48Z", "designation": "Edgeblab", "qte": 88, "prix": 46863.36, "montant": 21864.37, "tauxRemise": 0.33, "operateur": 7, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 5250.05}, {"id": 19, "date": "2022-05-24T05:38:01Z", "designation": "Twitterwire", "qte": 117, "prix": 87434.34, "montant": 35586.83, "tauxRemise": 0.79, "operateur": 10, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 27649.06}, {"id": 20, "date": "2022-01-18T10:00:51Z", "designation": "Gigashots", "qte": 122, "prix": 6721.23, "montant": 23988.13, "tauxRemise": 0.72, "operateur": 8, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 77277.06}, {"id": 21, "date": "2021-06-17T00:52:09Z", "designation": "Thoughtstorm", "qte": 106, "prix": 17751.49, "montant": 11021.47, "tauxRemise": 0.22, "operateur": 1, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 49073.78}, {"id": 22, "date": "2021-07-04T05:09:55Z", "designation": "Youopia", "qte": 444, "prix": 81779.75, "montant": 60772.38, "tauxRemise": 0.41, "operateur": 3, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 48449.37}, {"id": 23, "date": "2022-01-25T23:42:43Z", "designation": "Avavee", "qte": 70, "prix": 29383.32, "montant": 94622.29, "tauxRemise": 0.74, "operateur": 5, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 27355.31}, {"id": 24, "date": "2022-05-30T02:21:41Z", "designation": "Demimbu", "qte": 363, "prix": 23644.87, "montant": 35365.39, "tauxRemise": 0.4, "operateur": 10, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 18680.29}, {"id": 25, "date": "2022-02-07T05:35:37Z", "designation": "Realbuzz", "qte": 299, "prix": 48402.06, "montant": 59470.09, "tauxRemise": 0.72, "operateur": 5, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 22522.57}, {"id": 26, "date": "2021-12-30T09:09:30Z", "designation": "Linkbridge", "qte": 457, "prix": 44039.19, "montant": 37963.63, "tauxRemise": 0.39, "operateur": 3, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 66874.15}, {"id": 27, "date": "2021-11-05T06:28:04Z", "designation": "<PERSON>", "qte": 172, "prix": 86066.81, "montant": 28657.24, "tauxRemise": 0.39, "operateur": 1, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 29909.21}, {"id": 28, "date": "2021-06-12T01:27:48Z", "designation": "Realbridge", "qte": 328, "prix": 15277.1, "montant": 24070.45, "tauxRemise": 0.4, "operateur": 9, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 65372.02}, {"id": 29, "date": "2021-10-30T09:56:57Z", "designation": "Flipopia", "qte": 17, "prix": 99503.57, "montant": 38315.06, "tauxRemise": 0.41, "operateur": 5, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 47319.23}, {"id": 30, "date": "2021-09-26T10:23:33Z", "designation": "<PERSON><PERSON><PERSON><PERSON>", "qte": 443, "prix": 43312.83, "montant": 16032.51, "tauxRemise": 0.27, "operateur": 2, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 17093.05}, {"id": 31, "date": "2021-06-20T12:48:57Z", "designation": "JumpXS", "qte": 360, "prix": 76717.28, "montant": 45585.94, "tauxRemise": 0.75, "operateur": 6, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 91443.88}, {"id": 32, "date": "2022-06-05T16:41:16Z", "designation": "Quimba", "qte": 205, "prix": 88372.02, "montant": 23206.8, "tauxRemise": 0.87, "operateur": 7, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 23205.33}, {"id": 33, "date": "2022-02-19T12:48:28Z", "designation": "<PERSON><PERSON>", "qte": 296, "prix": 27869.76, "montant": 64403.81, "tauxRemise": 0.2, "operateur": 8, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 11191.59}, {"id": 34, "date": "2021-09-04T15:40:57Z", "designation": "<PERSON><PERSON><PERSON>", "qte": 327, "prix": 11885.21, "montant": 36414.06, "tauxRemise": 0.63, "operateur": 5, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 76325.56}, {"id": 35, "date": "2021-11-20T01:35:51Z", "designation": "Ya<PERSON><PERSON>", "qte": 423, "prix": 33522.69, "montant": 7105.96, "tauxRemise": 0.94, "operateur": 6, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 11097.77}, {"id": 36, "date": "2021-11-27T07:51:43Z", "designation": "<PERSON><PERSON>", "qte": 376, "prix": 73533.87, "montant": 59000.36, "tauxRemise": 0.02, "operateur": 3, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 49769.35}, {"id": 37, "date": "2021-10-18T05:32:18Z", "designation": "Buzzster", "qte": 168, "prix": 94350.49, "montant": 77587.51, "tauxRemise": 0.25, "operateur": 6, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 48117.35}, {"id": 38, "date": "2021-08-15T08:10:02Z", "designation": "<PERSON><PERSON><PERSON>", "qte": 395, "prix": 77876.75, "montant": 46501.7, "tauxRemise": 0.84, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 66582.66}, {"id": 39, "date": "2022-02-18T01:37:41Z", "designation": "Devpoint", "qte": 41, "prix": 25315.46, "montant": 41426.29, "tauxRemise": 0.49, "operateur": 9, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 67329.93}, {"id": 40, "date": "2022-05-25T22:02:05Z", "designation": "JumpXS", "qte": 471, "prix": 942.26, "montant": 40833.64, "tauxRemise": 0.33, "operateur": 9, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 30239.57}, {"id": 41, "date": "2021-06-21T08:19:10Z", "designation": "Linkbuzz", "qte": 56, "prix": 19311.92, "montant": 46020.51, "tauxRemise": 0.2, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 31707.2}, {"id": 42, "date": "2022-05-13T05:31:39Z", "designation": "Jabbersphere", "qte": 188, "prix": 15207.35, "montant": 86588.06, "tauxRemise": 0.16, "operateur": 5, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 69992.96}, {"id": 43, "date": "2022-01-22T18:26:59Z", "designation": "<PERSON><PERSON>", "qte": 426, "prix": 60270.4, "montant": 96743.56, "tauxRemise": 0.47, "operateur": 9, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 74216.33}, {"id": 44, "date": "2021-07-24T21:31:20Z", "designation": "<PERSON><PERSON>", "qte": 115, "prix": 33045.63, "montant": 76336.15, "tauxRemise": 0.44, "operateur": 1, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 40061.24}, {"id": 45, "date": "2022-05-30T03:55:57Z", "designation": "Flashset", "qte": 0, "prix": 40553.76, "montant": 36838.04, "tauxRemise": 0.32, "operateur": 3, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 31421.34}, {"id": 46, "date": "2022-04-03T16:18:31Z", "designation": "<PERSON><PERSON>", "qte": 386, "prix": 80419.25, "montant": 46676.24, "tauxRemise": 0.28, "operateur": 4, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 78278.85}, {"id": 47, "date": "2022-04-03T16:52:47Z", "designation": "<PERSON><PERSON>", "qte": 271, "prix": 10045.58, "montant": 90941.16, "tauxRemise": 0.61, "operateur": 10, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 54893.0}, {"id": 48, "date": "2022-01-26T18:37:12Z", "designation": "<PERSON><PERSON>", "qte": 335, "prix": 79596.09, "montant": 44313.62, "tauxRemise": 0.49, "operateur": 5, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 23217.43}, {"id": 49, "date": "2022-01-26T01:41:28Z", "designation": "Reallinks", "qte": 118, "prix": 11141.85, "montant": 90569.49, "tauxRemise": 0.21, "operateur": 3, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 10242.94}, {"id": 50, "date": "2022-02-10T13:04:10Z", "designation": "Wikizz", "qte": 381, "prix": 63406.61, "montant": 85167.46, "tauxRemise": 0.74, "operateur": 10, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 44776.1}, {"id": 51, "date": "2022-03-20T01:16:11Z", "designation": "Fivechat", "qte": 258, "prix": 57133.21, "montant": 23692.72, "tauxRemise": 0.78, "operateur": 8, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 53265.15}, {"id": 52, "date": "2022-01-27T09:27:42Z", "designation": "InnoZ", "qte": 65, "prix": 53074.47, "montant": 59935.6, "tauxRemise": 0.3, "operateur": 1, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 54603.43}, {"id": 53, "date": "2022-02-21T15:04:44Z", "designation": "Buzzster", "qte": 40, "prix": 2089.7, "montant": 93550.02, "tauxRemise": 0.45, "operateur": 9, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 60917.23}, {"id": 54, "date": "2022-01-02T00:49:38Z", "designation": "<PERSON><PERSON>", "qte": 259, "prix": 10520.14, "montant": 9504.04, "tauxRemise": 0.51, "operateur": 3, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 42156.02}, {"id": 55, "date": "2022-03-24T13:12:18Z", "designation": "Jaxbean", "qte": 17, "prix": 26045.21, "montant": 60801.92, "tauxRemise": 0.04, "operateur": 10, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 72790.65}, {"id": 56, "date": "2022-02-27T19:09:19Z", "designation": "Bubblebox", "qte": 227, "prix": 45215.55, "montant": 93102.62, "tauxRemise": 0.74, "operateur": 9, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 20446.98}, {"id": 57, "date": "2022-04-20T13:39:34Z", "designation": "Skinix", "qte": 29, "prix": 95012.44, "montant": 24449.14, "tauxRemise": 0.46, "operateur": 6, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 49540.38}, {"id": 58, "date": "2021-08-23T18:54:01Z", "designation": "Photobean", "qte": 114, "prix": 92290.74, "montant": 66819.37, "tauxRemise": 0.94, "operateur": 7, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 41042.49}, {"id": 59, "date": "2021-07-04T05:40:37Z", "designation": "Rooxo", "qte": 232, "prix": 5662.92, "montant": 6353.1, "tauxRemise": 0.34, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 68363.72}, {"id": 60, "date": "2021-11-04T00:37:27Z", "designation": "Voonix", "qte": 23, "prix": 68826.23, "montant": 49446.17, "tauxRemise": 0.22, "operateur": 6, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 54239.97}, {"id": 61, "date": "2022-04-23T13:41:26Z", "designation": "Mydeo", "qte": 311, "prix": 31828.21, "montant": 72891.82, "tauxRemise": 0.37, "operateur": 5, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 35039.07}, {"id": 62, "date": "2022-02-23T20:16:11Z", "designation": "Skinte", "qte": 262, "prix": 77735.81, "montant": 16562.95, "tauxRemise": 0.37, "operateur": 7, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 1525.21}, {"id": 63, "date": "2021-11-04T23:54:31Z", "designation": "<PERSON><PERSON><PERSON>", "qte": 476, "prix": 20180.23, "montant": 90574.91, "tauxRemise": 0.31, "operateur": 8, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 97879.09}, {"id": 64, "date": "2022-03-16T19:15:00Z", "designation": "Vitz", "qte": 492, "prix": 94689.97, "montant": 81664.03, "tauxRemise": 0.77, "operateur": 3, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 35361.01}, {"id": 65, "date": "2021-12-06T16:05:37Z", "designation": "Divape", "qte": 306, "prix": 7699.6, "montant": 91281.23, "tauxRemise": 0.32, "operateur": 9, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 77492.28}, {"id": 66, "date": "2021-11-23T07:51:36Z", "designation": "Linkbuzz", "qte": 116, "prix": 19994.68, "montant": 38668.3, "tauxRemise": 0.84, "operateur": 4, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 25488.32}, {"id": 67, "date": "2022-02-20T13:41:57Z", "designation": "Voonyx", "qte": 207, "prix": 44538.52, "montant": 89555.65, "tauxRemise": 0.26, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 72232.92}, {"id": 68, "date": "2022-05-17T20:46:19Z", "designation": "Livetube", "qte": 371, "prix": 73673.45, "montant": 27517.66, "tauxRemise": 0.04, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 99520.6}, {"id": 69, "date": "2022-02-11T03:37:03Z", "designation": "Rhynoodle", "qte": 233, "prix": 21919.36, "montant": 39361.18, "tauxRemise": 0.75, "operateur": 3, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 80310.45}, {"id": 70, "date": "2021-06-20T05:21:25Z", "designation": "Jan<PERSON>", "qte": 297, "prix": 98412.99, "montant": 37816.97, "tauxRemise": 0.1, "operateur": 7, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 54239.44}, {"id": 71, "date": "2021-06-28T06:43:29Z", "designation": "<PERSON><PERSON><PERSON>", "qte": 415, "prix": 92789.83, "montant": 29850.26, "tauxRemise": 0.16, "operateur": 10, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 787.48}, {"id": 72, "date": "2021-08-03T04:02:02Z", "designation": "Skipstorm", "qte": 228, "prix": 28182.31, "montant": 32150.18, "tauxRemise": 0.23, "operateur": 5, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 74013.02}, {"id": 73, "date": "2022-01-02T08:01:50Z", "designation": "<PERSON><PERSON>", "qte": 316, "prix": 87993.39, "montant": 69471.76, "tauxRemise": 0.62, "operateur": 6, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 80859.27}, {"id": 74, "date": "2021-08-12T04:15:50Z", "designation": "<PERSON><PERSON>", "qte": 84, "prix": 85339.29, "montant": 30901.98, "tauxRemise": 0.84, "operateur": 2, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 43751.95}, {"id": 75, "date": "2021-07-26T20:21:37Z", "designation": "Topicshots", "qte": 240, "prix": 98142.57, "montant": 48376.75, "tauxRemise": 0.25, "operateur": 7, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 34783.8}, {"id": 76, "date": "2022-01-19T14:18:57Z", "designation": "Zoomzone", "qte": 411, "prix": 25574.79, "montant": 24470.74, "tauxRemise": 0.6, "operateur": 3, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 39890.83}, {"id": 77, "date": "2021-11-30T04:55:48Z", "designation": "<PERSON><PERSON>", "qte": 387, "prix": 42097.2, "montant": 84040.77, "tauxRemise": 0.85, "operateur": 5, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 5868.21}, {"id": 78, "date": "2021-09-10T01:38:41Z", "designation": "Yamia", "qte": 261, "prix": 9540.53, "montant": 71001.26, "tauxRemise": 0.12, "operateur": 2, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 2339.05}, {"id": 79, "date": "2022-04-27T15:54:23Z", "designation": "Photospace", "qte": 384, "prix": 70855.06, "montant": 88216.52, "tauxRemise": 0.85, "operateur": 1, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 39863.8}, {"id": 80, "date": "2022-03-28T09:31:01Z", "designation": "Zoovu", "qte": 214, "prix": 5176.47, "montant": 22947.69, "tauxRemise": 0.48, "operateur": 8, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 21812.94}, {"id": 81, "date": "2022-01-16T17:29:15Z", "designation": "Lazz", "qte": 410, "prix": 70351.72, "montant": 62413.27, "tauxRemise": 0.96, "operateur": 7, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 96524.24}, {"id": 82, "date": "2021-12-16T05:52:00Z", "designation": "Tavu", "qte": 407, "prix": 26371.34, "montant": 69241.38, "tauxRemise": 0.32, "operateur": 7, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 17469.61}, {"id": 83, "date": "2021-11-16T22:25:22Z", "designation": "Wordpedia", "qte": 145, "prix": 39501.7, "montant": 10598.63, "tauxRemise": 0.29, "operateur": 1, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 25770.91}, {"id": 84, "date": "2022-03-26T12:25:55Z", "designation": "Voonyx", "qte": 69, "prix": 36163.22, "montant": 80182.96, "tauxRemise": 0.31, "operateur": 2, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 71311.32}, {"id": 85, "date": "2021-07-03T10:10:18Z", "designation": "Buzzshare", "qte": 243, "prix": 23444.79, "montant": 8794.28, "tauxRemise": 0.03, "operateur": 3, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 83489.13}, {"id": 86, "date": "2022-06-04T11:19:35Z", "designation": "Realbridge", "qte": 238, "prix": 43064.81, "montant": 73730.44, "tauxRemise": 0.68, "operateur": 4, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 35349.18}, {"id": 87, "date": "2021-11-08T01:49:35Z", "designation": "Aivee", "qte": 224, "prix": 55588.54, "montant": 92800.2, "tauxRemise": 0.0, "operateur": 5, "beneficiaire": 1, "clientId": 1, "infoVente": 1, "cumul": 72352.48}, {"id": 88, "date": "2021-07-22T06:17:08Z", "designation": "JumpXS", "qte": 392, "prix": 17032.61, "montant": 80340.11, "tauxRemise": 0.07, "operateur": 5, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 86990.38}, {"id": 89, "date": "2021-10-28T12:09:43Z", "designation": "Brows<PERSON><PERSON>", "qte": 239, "prix": 10097.84, "montant": 42861.17, "tauxRemise": 0.32, "operateur": 1, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 43120.7}, {"id": 90, "date": "2022-02-02T00:28:53Z", "designation": "Pixope", "qte": 424, "prix": 6270.73, "montant": 69875.02, "tauxRemise": 0.31, "operateur": 2, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 86562.21}, {"id": 91, "date": "2022-02-20T20:38:16Z", "designation": "Youfeed", "qte": 88, "prix": 85869.87, "montant": 18935.53, "tauxRemise": 0.74, "operateur": 5, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 96439.01}, {"id": 92, "date": "2022-02-09T21:44:00Z", "designation": "Roombo", "qte": 265, "prix": 28441.86, "montant": 79147.15, "tauxRemise": 0.81, "operateur": 8, "beneficiaire": 4, "clientId": 1, "infoVente": 1, "cumul": 77486.35}, {"id": 93, "date": "2021-11-16T03:33:47Z", "designation": "<PERSON><PERSON><PERSON>", "qte": 454, "prix": 525.05, "montant": 67273.16, "tauxRemise": 0.07, "operateur": 7, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 83652.43}, {"id": 94, "date": "2022-02-16T08:46:18Z", "designation": "Devpoint", "qte": 12, "prix": 86899.47, "montant": 59610.48, "tauxRemise": 0.55, "operateur": 10, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 91081.89}, {"id": 95, "date": "2022-05-08T22:19:35Z", "designation": "Twitterworks", "qte": 65, "prix": 61792.43, "montant": 969.09, "tauxRemise": 0.4, "operateur": 9, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 27237.24}, {"id": 96, "date": "2021-06-11T14:55:48Z", "designation": "You<PERSON><PERSON>", "qte": 30, "prix": 4121.5, "montant": 3253.39, "tauxRemise": 0.39, "operateur": 2, "beneficiaire": 5, "clientId": 1, "infoVente": 1, "cumul": 30209.49}, {"id": 97, "date": "2021-07-21T23:08:04Z", "designation": "Ya<PERSON><PERSON>", "qte": 263, "prix": 47598.7, "montant": 13648.73, "tauxRemise": 0.24, "operateur": 7, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 34720.14}, {"id": 98, "date": "2021-10-01T19:41:36Z", "designation": "Thoughtsphere", "qte": 441, "prix": 21707.28, "montant": 81257.7, "tauxRemise": 0.48, "operateur": 10, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 90574.76}, {"id": 99, "date": "2022-03-14T18:24:24Z", "designation": "<PERSON><PERSON>", "qte": 429, "prix": 21472.34, "montant": 88655.78, "tauxRemise": 0.57, "operateur": 10, "beneficiaire": 2, "clientId": 1, "infoVente": 1, "cumul": 31622.61}, {"id": 100, "date": "2022-01-22T06:56:14Z", "designation": "Rhynoodle", "qte": 440, "prix": 90692.71, "montant": 68832.62, "tauxRemise": 0.57, "operateur": 1, "beneficiaire": 3, "clientId": 1, "infoVente": 1, "cumul": 72981.7}], "chiffreAffairesParFTs": [{"id": 1, "familleTarif": 4, "montantBrut": 8002, "montantRemise": 72385, "clientId": 1, "montantNet": 31943, "date": "2022-06-05T09:40:10Z"}, {"id": 2, "familleTarif": 3, "montantBrut": 23077, "montantRemise": 67382, "clientId": 2, "montantNet": 31245, "date": "2021-10-16T23:25:50Z"}, {"id": 3, "familleTarif": 2, "montantBrut": 27017, "montantRemise": 96888, "clientId": 3, "montantNet": 21702, "date": "2021-06-15T11:49:20Z"}, {"id": 4, "familleTarif": 3, "montantBrut": 16542, "montantRemise": 8445, "clientId": 4, "montantNet": 11600, "date": "2022-03-08T18:48:11Z"}, {"id": 5, "familleTarif": 4, "montantBrut": 66429, "montantRemise": 14388, "clientId": 5, "montantNet": 3574, "date": "2021-12-08T11:34:33Z"}, {"id": 6, "familleTarif": 4, "montantBrut": 54853, "montantRemise": 3702, "clientId": 6, "montantNet": 21784, "date": "2022-04-20T19:14:13Z"}, {"id": 7, "familleTarif": 3, "montantBrut": 4794, "montantRemise": 30207, "clientId": 1, "montantNet": 31437, "date": "2022-05-18T21:44:56Z"}, {"id": 8, "familleTarif": 3, "montantBrut": 46075, "montantRemise": 40402, "clientId": 5, "montantNet": 15460, "date": "2021-06-30T12:48:42Z"}, {"id": 9, "familleTarif": 4, "montantBrut": 28280, "montantRemise": 6992, "clientId": 2, "montantNet": 44193, "date": "2021-07-09T12:44:08Z"}, {"id": 10, "familleTarif": 4, "montantBrut": 61143, "montantRemise": 79356, "clientId": 10, "montantNet": 34584, "date": "2021-11-30T17:13:57Z"}, {"id": 11, "familleTarif": 2, "montantBrut": 89798, "montantRemise": 93674, "clientId": 1, "montantNet": 8322, "date": "2021-09-21T06:10:31Z"}, {"id": 12, "familleTarif": 4, "montantBrut": 37777, "montantRemise": 52832, "clientId": 1, "montantNet": 53493, "date": "2021-09-07T01:18:50Z"}, {"id": 13, "familleTarif": 3, "montantBrut": 95610, "montantRemise": 77082, "clientId": 1, "montantNet": 5070, "date": "2022-06-05T07:47:07Z"}, {"id": 14, "familleTarif": 2, "montantBrut": 63586, "montantRemise": 73510, "clientId": 1, "montantNet": 15744, "date": "2022-06-01T00:29:15Z"}, {"id": 15, "familleTarif": 2, "montantBrut": 17202, "montantRemise": 70641, "clientId": 1, "montantNet": 3656, "date": "2022-04-10T15:30:49Z"}, {"id": 16, "familleTarif": 2, "montantBrut": 63095, "montantRemise": 91249, "clientId": 1, "montantNet": 14014, "date": "2021-11-25T07:37:58Z"}, {"id": 17, "familleTarif": 5, "montantBrut": 59785, "montantRemise": 40865, "clientId": 1, "montantNet": 63786, "date": "2022-05-08T06:09:49Z"}, {"id": 18, "familleTarif": 2, "montantBrut": 45640, "montantRemise": 77465, "clientId": 1, "montantNet": 73945, "date": "2021-08-20T12:37:08Z"}, {"id": 19, "familleTarif": 1, "montantBrut": 31291, "montantRemise": 59681, "clientId": 1, "montantNet": 8894, "date": "2021-10-24T06:47:31Z"}, {"id": 20, "familleTarif": 4, "montantBrut": 33397, "montantRemise": 5616, "clientId": 1, "montantNet": 25192, "date": "2021-07-18T17:47:19Z"}, {"id": 21, "familleTarif": 5, "montantBrut": 49410, "montantRemise": 4496, "clientId": 1, "montantNet": 82470, "date": "2022-02-16T07:53:37Z"}, {"id": 22, "familleTarif": 5, "montantBrut": 15047, "montantRemise": 30154, "clientId": 1, "montantNet": 78530, "date": "2022-02-05T20:06:28Z"}, {"id": 23, "familleTarif": 4, "montantBrut": 41523, "montantRemise": 40549, "clientId": 1, "montantNet": 46588, "date": "2022-02-06T17:43:55Z"}, {"id": 24, "familleTarif": 4, "montantBrut": 25496, "montantRemise": 86950, "clientId": 1, "montantNet": 72982, "date": "2022-03-21T15:30:15Z"}, {"id": 25, "familleTarif": 3, "montantBrut": 65156, "montantRemise": 5939, "clientId": 1, "montantNet": 3244, "date": "2022-02-04T23:56:06Z"}, {"id": 26, "familleTarif": 2, "montantBrut": 85961, "montantRemise": 8227, "clientId": 1, "montantNet": 35044, "date": "2021-09-14T08:02:06Z"}, {"id": 27, "familleTarif": 5, "montantBrut": 51137, "montantRemise": 74475, "clientId": 1, "montantNet": 12880, "date": "2021-10-20T16:34:28Z"}, {"id": 28, "familleTarif": 4, "montantBrut": 2955, "montantRemise": 30488, "clientId": 1, "montantNet": 32924, "date": "2021-10-10T21:41:05Z"}, {"id": 29, "familleTarif": 1, "montantBrut": 36299, "montantRemise": 97290, "clientId": 1, "montantNet": 32042, "date": "2021-09-02T11:17:00Z"}, {"id": 30, "familleTarif": 4, "montantBrut": 46325, "montantRemise": 39180, "clientId": 1, "montantNet": 21686, "date": "2022-05-08T01:24:39Z"}, {"id": 31, "familleTarif": 5, "montantBrut": 12239, "montantRemise": 86346, "clientId": 1, "montantNet": 49466, "date": "2021-10-06T16:26:53Z"}, {"id": 32, "familleTarif": 5, "montantBrut": 29996, "montantRemise": 6825, "clientId": 1, "montantNet": 83397, "date": "2021-06-20T22:09:29Z"}, {"id": 33, "familleTarif": 4, "montantBrut": 69396, "montantRemise": 33004, "clientId": 1, "montantNet": 16173, "date": "2021-06-25T17:47:32Z"}, {"id": 34, "familleTarif": 1, "montantBrut": 32161, "montantRemise": 38899, "clientId": 1, "montantNet": 97541, "date": "2022-03-10T10:10:25Z"}, {"id": 35, "familleTarif": 3, "montantBrut": 21812, "montantRemise": 53076, "clientId": 1, "montantNet": 34498, "date": "2021-12-27T20:41:32Z"}, {"id": 36, "familleTarif": 2, "montantBrut": 25632, "montantRemise": 59712, "clientId": 1, "montantNet": 91788, "date": "2021-08-04T13:37:35Z"}, {"id": 37, "familleTarif": 1, "montantBrut": 24312, "montantRemise": 69454, "clientId": 1, "montantNet": 28374, "date": "2022-01-05T19:24:51Z"}, {"id": 38, "familleTarif": 5, "montantBrut": 74456, "montantRemise": 72037, "clientId": 1, "montantNet": 63831, "date": "2022-06-05T07:58:59Z"}, {"id": 39, "familleTarif": 5, "montantBrut": 47338, "montantRemise": 23749, "clientId": 1, "montantNet": 90534, "date": "2022-03-02T14:05:24Z"}, {"id": 40, "familleTarif": 4, "montantBrut": 60522, "montantRemise": 39367, "clientId": 1, "montantNet": 12051, "date": "2021-09-21T01:59:56Z"}, {"id": 41, "familleTarif": 3, "montantBrut": 1189, "montantRemise": 51931, "clientId": 1, "montantNet": 18807, "date": "2021-12-15T10:59:53Z"}, {"id": 42, "familleTarif": 4, "montantBrut": 19265, "montantRemise": 50047, "clientId": 1, "montantNet": 92682, "date": "2022-03-31T20:27:46Z"}, {"id": 43, "familleTarif": 3, "montantBrut": 54870, "montantRemise": 20670, "clientId": 1, "montantNet": 24735, "date": "2022-01-23T04:19:35Z"}, {"id": 44, "familleTarif": 4, "montantBrut": 21546, "montantRemise": 58002, "clientId": 1, "montantNet": 53499, "date": "2022-04-11T20:48:03Z"}, {"id": 45, "familleTarif": 1, "montantBrut": 68944, "montantRemise": 2877, "clientId": 1, "montantNet": 14205, "date": "2021-08-01T05:56:39Z"}, {"id": 46, "familleTarif": 2, "montantBrut": 12676, "montantRemise": 91563, "clientId": 1, "montantNet": 96635, "date": "2021-11-24T21:49:32Z"}, {"id": 47, "familleTarif": 5, "montantBrut": 81383, "montantRemise": 59990, "clientId": 1, "montantNet": 38346, "date": "2022-01-01T08:58:51Z"}, {"id": 48, "familleTarif": 4, "montantBrut": 29982, "montantRemise": 27863, "clientId": 1, "montantNet": 98282, "date": "2021-08-04T21:46:16Z"}, {"id": 49, "familleTarif": 2, "montantBrut": 31336, "montantRemise": 64508, "clientId": 1, "montantNet": 56131, "date": "2021-08-16T09:59:16Z"}, {"id": 50, "familleTarif": 1, "montantBrut": 29139, "montantRemise": 48628, "clientId": 1, "montantNet": 1493, "date": "2022-02-04T03:16:09Z"}, {"id": 51, "familleTarif": 3, "montantBrut": 92260, "montantRemise": 89007, "clientId": 1, "montantNet": 30925, "date": "2022-01-02T09:50:13Z"}, {"id": 52, "familleTarif": 1, "montantBrut": 25429, "montantRemise": 59760, "clientId": 1, "montantNet": 46850, "date": "2022-06-08T03:12:24Z"}, {"id": 53, "familleTarif": 4, "montantBrut": 2874, "montantRemise": 95655, "clientId": 1, "montantNet": 22860, "date": "2021-08-17T08:04:56Z"}, {"id": 54, "familleTarif": 1, "montantBrut": 61058, "montantRemise": 54266, "clientId": 1, "montantNet": 75453, "date": "2021-08-14T22:28:29Z"}, {"id": 55, "familleTarif": 4, "montantBrut": 61463, "montantRemise": 72544, "clientId": 1, "montantNet": 97826, "date": "2021-11-01T16:41:58Z"}, {"id": 56, "familleTarif": 4, "montantBrut": 37545, "montantRemise": 59595, "clientId": 1, "montantNet": 39285, "date": "2022-04-09T00:37:49Z"}, {"id": 57, "familleTarif": 2, "montantBrut": 93893, "montantRemise": 86774, "clientId": 1, "montantNet": 5492, "date": "2022-05-09T02:28:04Z"}, {"id": 58, "familleTarif": 4, "montantBrut": 79012, "montantRemise": 95795, "clientId": 1, "montantNet": 8777, "date": "2021-08-15T12:14:34Z"}, {"id": 59, "familleTarif": 2, "montantBrut": 27636, "montantRemise": 98200, "clientId": 1, "montantNet": 8340, "date": "2021-08-12T07:45:55Z"}, {"id": 60, "familleTarif": 4, "montantBrut": 42340, "montantRemise": 54049, "clientId": 1, "montantNet": 76642, "date": "2021-09-13T14:00:26Z"}, {"id": 61, "familleTarif": 3, "montantBrut": 56870, "montantRemise": 4861, "clientId": 1, "montantNet": 57609, "date": "2021-10-09T09:17:30Z"}, {"id": 62, "familleTarif": 2, "montantBrut": 83885, "montantRemise": 73963, "clientId": 1, "montantNet": 76314, "date": "2021-12-13T00:42:06Z"}, {"id": 63, "familleTarif": 1, "montantBrut": 49137, "montantRemise": 61686, "clientId": 1, "montantNet": 6417, "date": "2021-07-03T19:34:39Z"}, {"id": 64, "familleTarif": 5, "montantBrut": 1690, "montantRemise": 1123, "clientId": 1, "montantNet": 79484, "date": "2021-09-15T13:08:19Z"}, {"id": 65, "familleTarif": 1, "montantBrut": 82701, "montantRemise": 54058, "clientId": 1, "montantNet": 37310, "date": "2021-08-06T10:51:03Z"}, {"id": 66, "familleTarif": 2, "montantBrut": 25483, "montantRemise": 43228, "clientId": 1, "montantNet": 55528, "date": "2021-07-07T23:35:12Z"}, {"id": 67, "familleTarif": 2, "montantBrut": 19850, "montantRemise": 95793, "clientId": 1, "montantNet": 18863, "date": "2022-02-14T21:31:10Z"}, {"id": 68, "familleTarif": 3, "montantBrut": 25244, "montantRemise": 92738, "clientId": 1, "montantNet": 90320, "date": "2022-04-05T20:29:06Z"}, {"id": 69, "familleTarif": 1, "montantBrut": 16936, "montantRemise": 72462, "clientId": 1, "montantNet": 55003, "date": "2021-08-06T23:49:43Z"}, {"id": 70, "familleTarif": 1, "montantBrut": 52366, "montantRemise": 74865, "clientId": 1, "montantNet": 66115, "date": "2022-03-03T17:19:45Z"}, {"id": 71, "familleTarif": 3, "montantBrut": 53197, "montantRemise": 63389, "clientId": 1, "montantNet": 87540, "date": "2022-03-20T19:20:56Z"}, {"id": 72, "familleTarif": 3, "montantBrut": 22088, "montantRemise": 67784, "clientId": 1, "montantNet": 86254, "date": "2022-02-27T12:55:07Z"}, {"id": 73, "familleTarif": 3, "montantBrut": 97087, "montantRemise": 16049, "clientId": 1, "montantNet": 61624, "date": "2021-09-09T03:15:25Z"}, {"id": 74, "familleTarif": 3, "montantBrut": 36333, "montantRemise": 30921, "clientId": 1, "montantNet": 1481, "date": "2021-10-11T21:32:37Z"}, {"id": 75, "familleTarif": 2, "montantBrut": 63496, "montantRemise": 3619, "clientId": 1, "montantNet": 13392, "date": "2022-05-26T13:22:21Z"}, {"id": 76, "familleTarif": 4, "montantBrut": 75568, "montantRemise": 34614, "clientId": 1, "montantNet": 33110, "date": "2022-03-21T18:44:06Z"}, {"id": 77, "familleTarif": 4, "montantBrut": 16109, "montantRemise": 67987, "clientId": 1, "montantNet": 23709, "date": "2022-04-30T05:26:41Z"}, {"id": 78, "familleTarif": 1, "montantBrut": 86514, "montantRemise": 14184, "clientId": 1, "montantNet": 1437, "date": "2022-05-05T00:49:36Z"}, {"id": 79, "familleTarif": 1, "montantBrut": 26560, "montantRemise": 41741, "clientId": 1, "montantNet": 30303, "date": "2021-07-07T01:47:14Z"}, {"id": 80, "familleTarif": 3, "montantBrut": 14643, "montantRemise": 69142, "clientId": 1, "montantNet": 20436, "date": "2022-03-08T19:40:48Z"}, {"id": 81, "familleTarif": 3, "montantBrut": 75399, "montantRemise": 13127, "clientId": 1, "montantNet": 42340, "date": "2022-05-20T18:39:50Z"}, {"id": 82, "familleTarif": 4, "montantBrut": 9292, "montantRemise": 92448, "clientId": 1, "montantNet": 12090, "date": "2021-10-26T06:40:32Z"}, {"id": 83, "familleTarif": 1, "montantBrut": 36817, "montantRemise": 30580, "clientId": 1, "montantNet": 61402, "date": "2021-07-13T01:34:55Z"}, {"id": 84, "familleTarif": 1, "montantBrut": 27990, "montantRemise": 7152, "clientId": 1, "montantNet": 21712, "date": "2021-10-02T10:02:12Z"}, {"id": 85, "familleTarif": 3, "montantBrut": 6713, "montantRemise": 79206, "clientId": 1, "montantNet": 38733, "date": "2022-03-31T20:56:26Z"}, {"id": 86, "familleTarif": 3, "montantBrut": 78854, "montantRemise": 21999, "clientId": 1, "montantNet": 46102, "date": "2022-04-11T19:18:03Z"}, {"id": 87, "familleTarif": 5, "montantBrut": 61195, "montantRemise": 42605, "clientId": 1, "montantNet": 19664, "date": "2021-07-04T01:17:45Z"}, {"id": 88, "familleTarif": 1, "montantBrut": 53227, "montantRemise": 15821, "clientId": 1, "montantNet": 2205, "date": "2022-05-10T07:13:09Z"}, {"id": 89, "familleTarif": 3, "montantBrut": 21851, "montantRemise": 57134, "clientId": 1, "montantNet": 87820, "date": "2022-04-09T13:58:32Z"}, {"id": 90, "familleTarif": 4, "montantBrut": 49687, "montantRemise": 56564, "clientId": 1, "montantNet": 86038, "date": "2022-04-13T20:15:58Z"}, {"id": 91, "familleTarif": 5, "montantBrut": 15371, "montantRemise": 3689, "clientId": 1, "montantNet": 95086, "date": "2022-06-03T13:52:49Z"}, {"id": 92, "familleTarif": 1, "montantBrut": 49644, "montantRemise": 73800, "clientId": 1, "montantNet": 99733, "date": "2022-04-18T17:42:04Z"}, {"id": 93, "familleTarif": 3, "montantBrut": 17518, "montantRemise": 59175, "clientId": 1, "montantNet": 15253, "date": "2021-09-22T08:43:27Z"}, {"id": 94, "familleTarif": 5, "montantBrut": 21117, "montantRemise": 98023, "clientId": 1, "montantNet": 57519, "date": "2021-06-26T09:14:23Z"}, {"id": 95, "familleTarif": 5, "montantBrut": 61683, "montantRemise": 2424, "clientId": 1, "montantNet": 7430, "date": "2021-11-11T08:46:31Z"}, {"id": 96, "familleTarif": 1, "montantBrut": 55926, "montantRemise": 52999, "clientId": 1, "montantNet": 47606, "date": "2021-12-31T12:22:08Z"}, {"id": 97, "familleTarif": 1, "montantBrut": 82093, "montantRemise": 96440, "clientId": 1, "montantNet": 6528, "date": "2022-04-28T07:05:40Z"}, {"id": 98, "familleTarif": 1, "montantBrut": 52299, "montantRemise": 34056, "clientId": 1, "montantNet": 40031, "date": "2021-10-20T11:31:25Z"}, {"id": 99, "familleTarif": 5, "montantBrut": 27823, "montantRemise": 5602, "clientId": 1, "montantNet": 42595, "date": "2022-03-23T09:41:22Z"}, {"id": 100, "familleTarif": 3, "montantBrut": 39462, "montantRemise": 36422, "clientId": 1, "montantNet": 83779, "date": "2021-08-18T19:30:23Z"}], "conventions": [{"id": 1, "nom": "<PERSON><PERSON>", "organisme": 21, "tauxRemboursement": 0.49, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 2, "nom": "<PERSON><PERSON><PERSON>", "organisme": 2, "tauxRemboursement": 0.89, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 3, "nom": "<PERSON><PERSON><PERSON>", "organisme": 11, "tauxRemboursement": 0.52, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 4, "nom": "<PERSON><PERSON>", "organisme": 31, "tauxRemboursement": 0.72, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 5, "nom": "<PERSON><PERSON>", "organisme": 51, "tauxRemboursement": 0.09, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 6, "nom": "<PERSON><PERSON>", "organisme": 10, "tauxRemboursement": 0.66, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 7, "nom": "<PERSON>", "organisme": 48, "tauxRemboursement": 0.07, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 8, "nom": "<PERSON><PERSON><PERSON>", "organisme": 61, "tauxRemboursement": 0.88, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 9, "nom": "Jsandye", "organisme": 24, "tauxRemboursement": 0.93, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 10, "nom": "Sal", "organisme": 25, "tauxRemboursement": 0.54, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 11, "nom": "<PERSON><PERSON><PERSON>", "organisme": 66, "tauxRemboursement": 0.22, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 12, "nom": "Sancho", "organisme": 20, "tauxRemboursement": 0.35, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 13, "nom": "<PERSON>", "organisme": 12, "tauxRemboursement": 0.44, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 14, "nom": "<PERSON><PERSON><PERSON>", "organisme": 11, "tauxRemboursement": 0.16, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 15, "nom": "Worthington", "organisme": 95, "tauxRemboursement": 0.38, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 16, "nom": "<PERSON><PERSON><PERSON>", "organisme": 4, "tauxRemboursement": 0.55, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 17, "nom": "<PERSON><PERSON>", "organisme": 83, "tauxRemboursement": 0.28, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 18, "nom": "<PERSON><PERSON><PERSON>", "organisme": 37, "tauxRemboursement": 0.14, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 19, "nom": "<PERSON><PERSON>", "organisme": 57, "tauxRemboursement": 0.45, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 20, "nom": "<PERSON>", "organisme": 34, "tauxRemboursement": 0.76, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 21, "nom": "<PERSON>", "organisme": 13, "tauxRemboursement": 0.46, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 22, "nom": "<PERSON><PERSON>", "organisme": 11, "tauxRemboursement": 0.35, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 23, "nom": "Prince", "organisme": 59, "tauxRemboursement": 0.46, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 24, "nom": "<PERSON>", "organisme": 27, "tauxRemboursement": 0.9, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 25, "nom": "Raleigh", "organisme": 74, "tauxRemboursement": 0.3, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 26, "nom": "<PERSON><PERSON><PERSON>", "organisme": 51, "tauxRemboursement": 0.89, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 27, "nom": "<PERSON><PERSON>", "organisme": 40, "tauxRemboursement": 0.88, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 28, "nom": "<PERSON><PERSON><PERSON><PERSON>", "organisme": 37, "tauxRemboursement": 0.81, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 29, "nom": "<PERSON>ine", "organisme": 37, "tauxRemboursement": 0.8, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 30, "nom": "<PERSON><PERSON><PERSON>", "organisme": 80, "tauxRemboursement": 0.05, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 31, "nom": "<PERSON><PERSON><PERSON>", "organisme": 30, "tauxRemboursement": 0.98, "typeRembPrix": "T", "typeRembTaux": "P"}, {"id": 32, "nom": "Bamby", "organisme": 50, "tauxRemboursement": 0.02, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 33, "nom": "<PERSON><PERSON>", "organisme": 63, "tauxRemboursement": 0.79, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 34, "nom": "<PERSON><PERSON>", "organisme": 14, "tauxRemboursement": 0.3, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 35, "nom": "<PERSON><PERSON>", "organisme": 40, "tauxRemboursement": 0.1, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 36, "nom": "Libbey", "organisme": 42, "tauxRemboursement": 0.38, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 37, "nom": "<PERSON>", "organisme": 96, "tauxRemboursement": 0.82, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 38, "nom": "Farrah", "organisme": 6, "tauxRemboursement": 0.96, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 39, "nom": "<PERSON><PERSON>", "organisme": 3, "tauxRemboursement": 0.16, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 40, "nom": "<PERSON>", "organisme": 2, "tauxRemboursement": 0.22, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 41, "nom": "Yankee", "organisme": 77, "tauxRemboursement": 0.86, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 42, "nom": "<PERSON>", "organisme": 22, "tauxRemboursement": 0.47, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 43, "nom": "<PERSON><PERSON>", "organisme": 81, "tauxRemboursement": 0.9, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 44, "nom": "<PERSON>", "organisme": 97, "tauxRemboursement": 0.7, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 45, "nom": "<PERSON><PERSON>", "organisme": 27, "tauxRemboursement": 0.57, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 46, "nom": "<PERSON>", "organisme": 85, "tauxRemboursement": 0.57, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 47, "nom": "Yorgo", "organisme": 18, "tauxRemboursement": 0.41, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 48, "nom": "<PERSON><PERSON>", "organisme": 72, "tauxRemboursement": 0.53, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 49, "nom": "<PERSON><PERSON>", "organisme": 6, "tauxRemboursement": 0.13, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 50, "nom": "<PERSON>", "organisme": 78, "tauxRemboursement": 0.3, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 51, "nom": "<PERSON>", "organisme": 41, "tauxRemboursement": 0.32, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 52, "nom": "<PERSON><PERSON><PERSON>", "organisme": 85, "tauxRemboursement": 0.98, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 53, "nom": "<PERSON>", "organisme": 15, "tauxRemboursement": 0.39, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 54, "nom": "Dix", "organisme": 20, "tauxRemboursement": 0.34, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 55, "nom": "<PERSON><PERSON>", "organisme": 64, "tauxRemboursement": 0.39, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 56, "nom": "<PERSON><PERSON>", "organisme": 16, "tauxRemboursement": 0.38, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 57, "nom": "<PERSON>", "organisme": 40, "tauxRemboursement": 0.24, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 58, "nom": "<PERSON><PERSON>", "organisme": 63, "tauxRemboursement": 0.43, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 59, "nom": "<PERSON><PERSON>", "organisme": 63, "tauxRemboursement": 0.71, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 60, "nom": "Yank", "organisme": 3, "tauxRemboursement": 0.28, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 61, "nom": "<PERSON><PERSON><PERSON>", "organisme": 53, "tauxRemboursement": 1.0, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 62, "nom": "<PERSON><PERSON><PERSON>", "organisme": 47, "tauxRemboursement": 0.68, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 63, "nom": "<PERSON><PERSON>", "organisme": 70, "tauxRemboursement": 0.05, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 64, "nom": "<PERSON><PERSON><PERSON>", "organisme": 28, "tauxRemboursement": 0.0, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 65, "nom": "<PERSON><PERSON><PERSON>", "organisme": 78, "tauxRemboursement": 0.4, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 66, "nom": "Una", "organisme": 90, "tauxRemboursement": 0.3, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 67, "nom": "Stacia", "organisme": 3, "tauxRemboursement": 0.44, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 68, "nom": "<PERSON><PERSON>", "organisme": 30, "tauxRemboursement": 0.53, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 69, "nom": "<PERSON><PERSON>", "organisme": 95, "tauxRemboursement": 0.06, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 70, "nom": "Noe", "organisme": 31, "tauxRemboursement": 0.38, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 71, "nom": "<PERSON><PERSON>", "organisme": 99, "tauxRemboursement": 0.92, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 72, "nom": "Rai<PERSON><PERSON>", "organisme": 56, "tauxRemboursement": 0.15, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 73, "nom": "Read", "organisme": 91, "tauxRemboursement": 0.67, "typeRembPrix": "T", "typeRembTaux": "P"}, {"id": 74, "nom": "<PERSON>", "organisme": 68, "tauxRemboursement": 0.03, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 75, "nom": "Rabbi", "organisme": 39, "tauxRemboursement": 0.92, "typeRembPrix": "T", "typeRembTaux": "P"}, {"id": 76, "nom": "<PERSON><PERSON>", "organisme": 25, "tauxRemboursement": 0.67, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 77, "nom": "Ally", "organisme": 90, "tauxRemboursement": 0.45, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 78, "nom": "<PERSON><PERSON>", "organisme": 61, "tauxRemboursement": 0.44, "typeRembPrix": "B", "typeRembTaux": "T"}, {"id": 79, "nom": "<PERSON>", "organisme": 89, "tauxRemboursement": 0.67, "typeRembPrix": "T", "typeRembTaux": "P"}, {"id": 80, "nom": "<PERSON><PERSON><PERSON>", "organisme": 68, "tauxRemboursement": 0.55, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 81, "nom": "Zonda", "organisme": 22, "tauxRemboursement": 0.63, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 82, "nom": "<PERSON><PERSON><PERSON>", "organisme": 14, "tauxRemboursement": 0.72, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 83, "nom": "<PERSON><PERSON><PERSON>", "organisme": 9, "tauxRemboursement": 0.58, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 84, "nom": "Nadine", "organisme": 21, "tauxRemboursement": 0.45, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 85, "nom": "Asia", "organisme": 78, "tauxRemboursement": 0.61, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 86, "nom": "Myca", "organisme": 37, "tauxRemboursement": 0.7, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 87, "nom": "<PERSON><PERSON>", "organisme": 10, "tauxRemboursement": 0.42, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 88, "nom": "<PERSON><PERSON>", "organisme": 40, "tauxRemboursement": 0.8, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 89, "nom": "<PERSON><PERSON>", "organisme": 72, "tauxRemboursement": 0.02, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 90, "nom": "<PERSON><PERSON><PERSON>", "organisme": 4, "tauxRemboursement": 0.01, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 91, "nom": "Fancy", "organisme": 45, "tauxRemboursement": 0.91, "typeRembPrix": "T", "typeRembTaux": "T"}, {"id": 92, "nom": "<PERSON><PERSON>", "organisme": 76, "tauxRemboursement": 0.75, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 93, "nom": "<PERSON>", "organisme": 31, "tauxRemboursement": 0.24, "typeRembPrix": "B", "typeRembTaux": "G"}, {"id": 94, "nom": "<PERSON>", "organisme": 68, "tauxRemboursement": 0.56, "typeRembPrix": "T", "typeRembTaux": "P"}, {"id": 95, "nom": "<PERSON><PERSON>", "organisme": 82, "tauxRemboursement": 0.15, "typeRembPrix": "P", "typeRembTaux": "T"}, {"id": 96, "nom": "<PERSON><PERSON>", "organisme": 74, "tauxRemboursement": 0.84, "typeRembPrix": "P", "typeRembTaux": "G"}, {"id": 97, "nom": "<PERSON><PERSON>", "organisme": 96, "tauxRemboursement": 0.18, "typeRembPrix": "B", "typeRembTaux": "P"}, {"id": 98, "nom": "<PERSON><PERSON><PERSON>", "organisme": 75, "tauxRemboursement": 0.29, "typeRembPrix": "T", "typeRembTaux": "G"}, {"id": 99, "nom": "<PERSON><PERSON><PERSON><PERSON>", "organisme": 57, "tauxRemboursement": 0.77, "typeRembPrix": "P", "typeRembTaux": "P"}, {"id": 100, "nom": "<PERSON><PERSON>", "organisme": 46, "tauxRemboursement": 0.57, "typeRembPrix": "T", "typeRembTaux": "P"}], "identifiantsConventions": [{"id": 1, "convention": 100, "numeroImmatriculation": 6454, "numeroAffiliation": 9289}, {"id": 2, "convention": 57, "numeroImmatriculation": 5616, "numeroAffiliation": 1842}, {"id": 3, "convention": 22, "numeroImmatriculation": 6957, "numeroAffiliation": 8581}, {"id": 4, "convention": 1, "numeroImmatriculation": 9167, "numeroAffiliation": 3919}, {"id": 5, "convention": 57, "numeroImmatriculation": 2983, "numeroAffiliation": 4171}, {"id": 6, "convention": 40, "numeroImmatriculation": 4564, "numeroAffiliation": 3115}, {"id": 7, "convention": 38, "numeroImmatriculation": 2333, "numeroAffiliation": 9858}, {"id": 8, "convention": 81, "numeroImmatriculation": 4473, "numeroAffiliation": 3024}, {"id": 9, "convention": 30, "numeroImmatriculation": 5463, "numeroAffiliation": 2201}, {"id": 10, "convention": 49, "numeroImmatriculation": 7907, "numeroAffiliation": 7763}, {"id": 11, "convention": 11, "numeroImmatriculation": 1096, "numeroAffiliation": 1943}, {"id": 12, "convention": 99, "numeroImmatriculation": 1094, "numeroAffiliation": 8421}, {"id": 13, "convention": 43, "numeroImmatriculation": 8078, "numeroAffiliation": 8676}, {"id": 14, "convention": 37, "numeroImmatriculation": 1439, "numeroAffiliation": 8895}, {"id": 15, "convention": 82, "numeroImmatriculation": 2307, "numeroAffiliation": 6107}, {"id": 16, "convention": 4, "numeroImmatriculation": 7512, "numeroAffiliation": 9912}, {"id": 17, "convention": 52, "numeroImmatriculation": 4557, "numeroAffiliation": 8512}, {"id": 18, "convention": 89, "numeroImmatriculation": 8432, "numeroAffiliation": 6835}, {"id": 19, "convention": 100, "numeroImmatriculation": 5233, "numeroAffiliation": 7817}, {"id": 20, "convention": 96, "numeroImmatriculation": 4800, "numeroAffiliation": 1970}, {"id": 21, "convention": 24, "numeroImmatriculation": 1324, "numeroAffiliation": 2863}, {"id": 22, "convention": 81, "numeroImmatriculation": 6657, "numeroAffiliation": 3598}, {"id": 23, "convention": 85, "numeroImmatriculation": 4359, "numeroAffiliation": 5833}, {"id": 24, "convention": 22, "numeroImmatriculation": 5168, "numeroAffiliation": 4000}, {"id": 25, "convention": 4, "numeroImmatriculation": 6875, "numeroAffiliation": 7032}, {"id": 26, "convention": 28, "numeroImmatriculation": 1063, "numeroAffiliation": 3173}, {"id": 27, "convention": 81, "numeroImmatriculation": 7173, "numeroAffiliation": 6852}, {"id": 28, "convention": 67, "numeroImmatriculation": 8808, "numeroAffiliation": 2751}, {"id": 29, "convention": 7, "numeroImmatriculation": 8751, "numeroAffiliation": 1109}, {"id": 30, "convention": 21, "numeroImmatriculation": 6872, "numeroAffiliation": 9659}, {"id": 31, "convention": 53, "numeroImmatriculation": 6388, "numeroAffiliation": 8467}, {"id": 32, "convention": 48, "numeroImmatriculation": 1300, "numeroAffiliation": 9879}, {"id": 33, "convention": 29, "numeroImmatriculation": 2680, "numeroAffiliation": 6373}, {"id": 34, "convention": 96, "numeroImmatriculation": 2194, "numeroAffiliation": 7147}, {"id": 35, "convention": 74, "numeroImmatriculation": 9187, "numeroAffiliation": 1096}, {"id": 36, "convention": 91, "numeroImmatriculation": 7004, "numeroAffiliation": 7160}, {"id": 37, "convention": 87, "numeroImmatriculation": 3939, "numeroAffiliation": 7047}, {"id": 38, "convention": 1, "numeroImmatriculation": 6719, "numeroAffiliation": 6200}, {"id": 39, "convention": 11, "numeroImmatriculation": 6199, "numeroAffiliation": 2398}, {"id": 40, "convention": 16, "numeroImmatriculation": 6740, "numeroAffiliation": 9302}, {"id": 41, "convention": 56, "numeroImmatriculation": 5919, "numeroAffiliation": 8891}, {"id": 42, "convention": 47, "numeroImmatriculation": 9416, "numeroAffiliation": 9278}, {"id": 43, "convention": 24, "numeroImmatriculation": 6868, "numeroAffiliation": 5429}, {"id": 44, "convention": 47, "numeroImmatriculation": 1533, "numeroAffiliation": 6816}, {"id": 45, "convention": 65, "numeroImmatriculation": 7011, "numeroAffiliation": 2066}, {"id": 46, "convention": 4, "numeroImmatriculation": 7282, "numeroAffiliation": 1442}, {"id": 47, "convention": 60, "numeroImmatriculation": 6824, "numeroAffiliation": 5786}, {"id": 48, "convention": 83, "numeroImmatriculation": 3243, "numeroAffiliation": 1126}, {"id": 49, "convention": 24, "numeroImmatriculation": 7042, "numeroAffiliation": 5947}, {"id": 50, "convention": 71, "numeroImmatriculation": 4856, "numeroAffiliation": 3229}, {"id": 51, "convention": 25, "numeroImmatriculation": 3701, "numeroAffiliation": 5246}, {"id": 52, "convention": 45, "numeroImmatriculation": 2884, "numeroAffiliation": 8980}, {"id": 53, "convention": 37, "numeroImmatriculation": 7979, "numeroAffiliation": 8493}, {"id": 54, "convention": 58, "numeroImmatriculation": 9970, "numeroAffiliation": 5788}, {"id": 55, "convention": 61, "numeroImmatriculation": 8202, "numeroAffiliation": 5517}, {"id": 56, "convention": 65, "numeroImmatriculation": 9977, "numeroAffiliation": 3682}, {"id": 57, "convention": 53, "numeroImmatriculation": 6769, "numeroAffiliation": 7908}, {"id": 58, "convention": 63, "numeroImmatriculation": 9347, "numeroAffiliation": 3910}, {"id": 59, "convention": 6, "numeroImmatriculation": 7562, "numeroAffiliation": 3947}, {"id": 60, "convention": 80, "numeroImmatriculation": 4337, "numeroAffiliation": 6100}, {"id": 61, "convention": 17, "numeroImmatriculation": 1104, "numeroAffiliation": 2713}, {"id": 62, "convention": 31, "numeroImmatriculation": 3600, "numeroAffiliation": 2074}, {"id": 63, "convention": 76, "numeroImmatriculation": 6474, "numeroAffiliation": 8148}, {"id": 64, "convention": 90, "numeroImmatriculation": 8902, "numeroAffiliation": 6743}, {"id": 65, "convention": 49, "numeroImmatriculation": 3586, "numeroAffiliation": 3077}, {"id": 66, "convention": 96, "numeroImmatriculation": 1549, "numeroAffiliation": 9664}, {"id": 67, "convention": 28, "numeroImmatriculation": 3782, "numeroAffiliation": 2996}, {"id": 68, "convention": 42, "numeroImmatriculation": 3428, "numeroAffiliation": 5541}, {"id": 69, "convention": 85, "numeroImmatriculation": 7524, "numeroAffiliation": 8128}, {"id": 70, "convention": 76, "numeroImmatriculation": 9390, "numeroAffiliation": 8285}, {"id": 71, "convention": 92, "numeroImmatriculation": 7605, "numeroAffiliation": 3617}, {"id": 72, "convention": 71, "numeroImmatriculation": 7156, "numeroAffiliation": 8654}, {"id": 73, "convention": 28, "numeroImmatriculation": 2886, "numeroAffiliation": 3338}, {"id": 74, "convention": 94, "numeroImmatriculation": 2709, "numeroAffiliation": 8999}, {"id": 75, "convention": 98, "numeroImmatriculation": 7043, "numeroAffiliation": 6143}, {"id": 76, "convention": 90, "numeroImmatriculation": 8657, "numeroAffiliation": 9772}, {"id": 77, "convention": 55, "numeroImmatriculation": 2991, "numeroAffiliation": 1894}, {"id": 78, "convention": 60, "numeroImmatriculation": 8612, "numeroAffiliation": 9551}, {"id": 79, "convention": 4, "numeroImmatriculation": 1294, "numeroAffiliation": 8413}, {"id": 80, "convention": 19, "numeroImmatriculation": 2348, "numeroAffiliation": 1854}, {"id": 81, "convention": 40, "numeroImmatriculation": 7808, "numeroAffiliation": 9702}, {"id": 82, "convention": 4, "numeroImmatriculation": 9651, "numeroAffiliation": 4032}, {"id": 83, "convention": 34, "numeroImmatriculation": 3319, "numeroAffiliation": 8447}, {"id": 84, "convention": 41, "numeroImmatriculation": 3588, "numeroAffiliation": 7386}, {"id": 85, "convention": 13, "numeroImmatriculation": 8223, "numeroAffiliation": 8526}, {"id": 86, "convention": 71, "numeroImmatriculation": 3394, "numeroAffiliation": 4718}, {"id": 87, "convention": 85, "numeroImmatriculation": 1893, "numeroAffiliation": 4656}, {"id": 88, "convention": 28, "numeroImmatriculation": 6176, "numeroAffiliation": 1888}, {"id": 89, "convention": 89, "numeroImmatriculation": 3115, "numeroAffiliation": 2796}, {"id": 90, "convention": 46, "numeroImmatriculation": 4693, "numeroAffiliation": 9593}, {"id": 91, "convention": 29, "numeroImmatriculation": 2984, "numeroAffiliation": 3804}, {"id": 92, "convention": 15, "numeroImmatriculation": 6692, "numeroAffiliation": 4817}, {"id": 93, "convention": 26, "numeroImmatriculation": 3849, "numeroAffiliation": 5487}, {"id": 94, "convention": 54, "numeroImmatriculation": 1522, "numeroAffiliation": 2215}, {"id": 95, "convention": 27, "numeroImmatriculation": 5748, "numeroAffiliation": 6870}, {"id": 96, "convention": 84, "numeroImmatriculation": 7794, "numeroAffiliation": 1400}, {"id": 97, "convention": 18, "numeroImmatriculation": 4304, "numeroAffiliation": 8942}, {"id": 98, "convention": 18, "numeroImmatriculation": 1101, "numeroAffiliation": 8327}, {"id": 99, "convention": 34, "numeroImmatriculation": 3192, "numeroAffiliation": 3512}, {"id": 100, "convention": 79, "numeroImmatriculation": 3667, "numeroAffiliation": 8551}], "infoVentes": [{"id": 1, "numVente": 8108, "numFacture": 4031, "dateVente": "2021-12-28T19:17:57Z", "dateFacture": "2021-12-04T04:00:44Z", "infoProduits": [4, 6]}, {"id": 2, "numVente": 4058, "numFacture": 1734, "dateVente": "2021-06-20T06:33:14Z", "dateFacture": "2021-12-09T08:49:04Z", "infoProduits": [3, 3]}, {"id": 3, "numVente": 5406, "numFacture": 6594, "dateVente": "2022-01-29T10:22:27Z", "dateFacture": "2022-02-14T01:39:59Z", "infoProduits": [6, 2]}, {"id": 4, "numVente": 9458, "numFacture": 2068, "dateVente": "2021-09-15T21:21:32Z", "dateFacture": "2022-04-02T02:54:16Z", "infoProduits": [7, 5]}, {"id": 5, "numVente": 6564, "numFacture": 1702, "dateVente": "2021-08-30T03:00:33Z", "dateFacture": "2021-12-08T00:37:25Z", "infoProduits": [4, 6]}, {"id": 6, "numVente": 4277, "numFacture": 4226, "dateVente": "2021-09-13T08:45:07Z", "dateFacture": "2022-05-08T18:44:01Z", "infoProduits": [2, 8]}, {"id": 7, "numVente": 3366, "numFacture": 5829, "dateVente": "2022-03-18T13:09:18Z", "dateFacture": "2021-09-10T08:44:55Z", "infoProduits": [1, 4]}, {"id": 8, "numVente": 7875, "numFacture": 4746, "dateVente": "2021-08-05T23:04:09Z", "dateFacture": "2022-01-14T23:18:53Z", "infoProduits": [3, 2]}, {"id": 9, "numVente": 9495, "numFacture": 1795, "dateVente": "2021-07-15T03:46:32Z", "dateFacture": "2022-05-07T03:44:27Z", "infoProduits": [1, 5]}, {"id": 10, "numVente": 8492, "numFacture": 1379, "dateVente": "2022-04-03T05:14:36Z", "dateFacture": "2022-02-12T08:07:40Z", "infoProduits": [3, 7]}, {"id": 11, "numVente": 4988, "numFacture": 8302, "dateVente": "2021-12-16T21:38:13Z", "dateFacture": "2021-06-30T01:17:30Z", "infoProduits": [3, 6]}, {"id": 12, "numVente": 8672, "numFacture": 8384, "dateVente": "2022-01-03T09:48:48Z", "dateFacture": "2022-02-16T08:51:30Z", "infoProduits": [7, 7]}, {"id": 13, "numVente": 7796, "numFacture": 8330, "dateVente": "2021-06-26T22:36:20Z", "dateFacture": "2021-11-10T00:45:56Z", "infoProduits": [8, 7]}, {"id": 14, "numVente": 5197, "numFacture": 5790, "dateVente": "2022-05-19T17:55:23Z", "dateFacture": "2021-10-05T17:50:52Z", "infoProduits": [8, 8]}, {"id": 15, "numVente": 2529, "numFacture": 2030, "dateVente": "2021-10-25T05:59:27Z", "dateFacture": "2022-05-24T04:21:15Z", "infoProduits": [5, 8]}, {"id": 16, "numVente": 4876, "numFacture": 7091, "dateVente": "2022-05-13T05:30:29Z", "dateFacture": "2021-09-28T09:14:20Z", "infoProduits": [4, 6]}, {"id": 17, "numVente": 6567, "numFacture": 1490, "dateVente": "2022-04-25T18:05:29Z", "dateFacture": "2021-07-09T14:57:23Z", "infoProduits": [1, 4]}, {"id": 18, "numVente": 6686, "numFacture": 9005, "dateVente": "2021-12-04T23:22:57Z", "dateFacture": "2021-11-15T23:36:29Z", "infoProduits": [8, 2]}, {"id": 19, "numVente": 4672, "numFacture": 5320, "dateVente": "2022-05-18T19:34:10Z", "dateFacture": "2022-02-22T06:36:29Z", "infoProduits": [3, 5]}, {"id": 20, "numVente": 1460, "numFacture": 6037, "dateVente": "2021-09-13T11:07:38Z", "dateFacture": "2021-08-31T17:14:08Z", "infoProduits": [5, 3]}, {"id": 21, "numVente": 4467, "numFacture": 7338, "dateVente": "2021-09-26T07:28:08Z", "dateFacture": "2022-05-09T00:09:01Z", "infoProduits": [1, 8]}, {"id": 22, "numVente": 7662, "numFacture": 1421, "dateVente": "2021-09-27T07:02:55Z", "dateFacture": "2021-10-12T17:28:08Z", "infoProduits": [7, 6]}, {"id": 23, "numVente": 2494, "numFacture": 9136, "dateVente": "2021-10-20T14:15:25Z", "dateFacture": "2021-10-03T10:37:05Z", "infoProduits": [1, 5]}, {"id": 24, "numVente": 3532, "numFacture": 5657, "dateVente": "2021-12-31T06:36:30Z", "dateFacture": "2022-05-12T17:53:48Z", "infoProduits": [1, 9]}, {"id": 25, "numVente": 5572, "numFacture": 9496, "dateVente": "2022-03-03T05:24:30Z", "dateFacture": "2021-10-11T10:29:42Z", "infoProduits": [6, 7]}, {"id": 26, "numVente": 9260, "numFacture": 3580, "dateVente": "2021-12-27T07:05:39Z", "dateFacture": "2021-12-11T19:30:38Z", "infoProduits": [9, 1]}, {"id": 27, "numVente": 4229, "numFacture": 6279, "dateVente": "2022-03-21T17:29:46Z", "dateFacture": "2021-11-22T11:11:37Z", "infoProduits": [6, 4]}, {"id": 28, "numVente": 6159, "numFacture": 9202, "dateVente": "2021-07-05T06:39:12Z", "dateFacture": "2021-10-26T18:44:08Z", "infoProduits": [6, 2]}, {"id": 29, "numVente": 6422, "numFacture": 2514, "dateVente": "2022-04-07T16:32:26Z", "dateFacture": "2021-08-13T07:34:28Z", "infoProduits": [2, 3]}, {"id": 30, "numVente": 9500, "numFacture": 6597, "dateVente": "2021-07-10T02:25:49Z", "dateFacture": "2022-05-31T01:44:49Z", "infoProduits": [1, 6]}, {"id": 31, "numVente": 4062, "numFacture": 4429, "dateVente": "2021-07-20T01:39:51Z", "dateFacture": "2021-12-13T18:48:20Z", "infoProduits": [6, 6]}, {"id": 32, "numVente": 6059, "numFacture": 3688, "dateVente": "2021-11-09T08:40:11Z", "dateFacture": "2021-10-17T19:45:32Z", "infoProduits": [5, 5]}, {"id": 33, "numVente": 5411, "numFacture": 7202, "dateVente": "2021-06-27T07:26:59Z", "dateFacture": "2021-11-29T23:07:15Z", "infoProduits": [4, 3]}, {"id": 34, "numVente": 2461, "numFacture": 9166, "dateVente": "2021-11-17T19:58:32Z", "dateFacture": "2021-07-13T11:47:58Z", "infoProduits": [1, 8]}, {"id": 35, "numVente": 2955, "numFacture": 9806, "dateVente": "2021-08-15T14:38:32Z", "dateFacture": "2021-06-15T12:47:04Z", "infoProduits": [1, 2]}, {"id": 36, "numVente": 8196, "numFacture": 1390, "dateVente": "2021-10-16T01:07:27Z", "dateFacture": "2021-10-11T06:42:02Z", "infoProduits": [3, 7]}, {"id": 37, "numVente": 6316, "numFacture": 2462, "dateVente": "2022-05-11T13:15:11Z", "dateFacture": "2021-10-15T18:40:50Z", "infoProduits": [4, 1]}, {"id": 38, "numVente": 4440, "numFacture": 7369, "dateVente": "2021-11-04T23:41:44Z", "dateFacture": "2022-05-19T04:32:47Z", "infoProduits": [1, 3]}, {"id": 39, "numVente": 4141, "numFacture": 1150, "dateVente": "2021-11-24T17:38:53Z", "dateFacture": "2021-12-21T21:40:54Z", "infoProduits": [1, 1]}, {"id": 40, "numVente": 9494, "numFacture": 5418, "dateVente": "2022-01-19T00:09:05Z", "dateFacture": "2021-07-12T16:39:44Z", "infoProduits": [6, 5]}, {"id": 41, "numVente": 5209, "numFacture": 8312, "dateVente": "2021-10-28T07:00:33Z", "dateFacture": "2022-01-04T23:17:57Z", "infoProduits": [6, 5]}, {"id": 42, "numVente": 4947, "numFacture": 6374, "dateVente": "2021-09-02T19:54:39Z", "dateFacture": "2021-08-07T15:14:27Z", "infoProduits": [3, 8]}, {"id": 43, "numVente": 5969, "numFacture": 6662, "dateVente": "2021-07-02T17:02:41Z", "dateFacture": "2021-10-07T09:10:57Z", "infoProduits": [9, 4]}, {"id": 44, "numVente": 8006, "numFacture": 5565, "dateVente": "2021-09-16T16:09:43Z", "dateFacture": "2021-06-19T17:20:33Z", "infoProduits": [4, 1]}, {"id": 45, "numVente": 6844, "numFacture": 5088, "dateVente": "2022-03-10T03:09:00Z", "dateFacture": "2022-05-16T11:52:18Z", "infoProduits": [4, 1]}, {"id": 46, "numVente": 1103, "numFacture": 3480, "dateVente": "2021-07-11T05:27:14Z", "dateFacture": "2022-01-29T20:16:11Z", "infoProduits": [1, 8]}, {"id": 47, "numVente": 7073, "numFacture": 9023, "dateVente": "2021-07-01T05:24:53Z", "dateFacture": "2021-12-08T03:28:34Z", "infoProduits": [1, 9]}, {"id": 48, "numVente": 1371, "numFacture": 9290, "dateVente": "2021-11-19T15:17:16Z", "dateFacture": "2021-10-01T21:19:05Z", "infoProduits": [9, 7]}, {"id": 49, "numVente": 3750, "numFacture": 4924, "dateVente": "2021-09-01T16:59:51Z", "dateFacture": "2021-07-24T08:29:13Z", "infoProduits": [5, 7]}, {"id": 50, "numVente": 8909, "numFacture": 7017, "dateVente": "2021-09-08T16:21:38Z", "dateFacture": "2022-04-24T03:03:40Z", "infoProduits": [9, 2]}, {"id": 51, "numVente": 2759, "numFacture": 2680, "dateVente": "2021-08-07T13:05:54Z", "dateFacture": "2022-06-01T22:42:53Z", "infoProduits": [3, 4]}, {"id": 52, "numVente": 7351, "numFacture": 4742, "dateVente": "2022-01-27T04:39:27Z", "dateFacture": "2021-12-12T21:20:51Z", "infoProduits": [8, 1]}, {"id": 53, "numVente": 3579, "numFacture": 3759, "dateVente": "2021-09-29T04:33:40Z", "dateFacture": "2021-08-11T11:42:16Z", "infoProduits": [2, 1]}, {"id": 54, "numVente": 3352, "numFacture": 7600, "dateVente": "2022-05-16T13:30:17Z", "dateFacture": "2022-04-24T03:19:00Z", "infoProduits": [3, 4]}, {"id": 55, "numVente": 8741, "numFacture": 4998, "dateVente": "2021-12-04T15:14:49Z", "dateFacture": "2022-02-21T04:38:42Z", "infoProduits": [2, 2]}, {"id": 56, "numVente": 9697, "numFacture": 4253, "dateVente": "2021-08-28T15:31:25Z", "dateFacture": "2021-11-30T02:31:54Z", "infoProduits": [6, 5]}, {"id": 57, "numVente": 7073, "numFacture": 2316, "dateVente": "2022-03-13T03:18:57Z", "dateFacture": "2021-11-22T04:28:27Z", "infoProduits": [3, 2]}, {"id": 58, "numVente": 6468, "numFacture": 5672, "dateVente": "2021-09-12T12:27:20Z", "dateFacture": "2021-12-02T15:19:38Z", "infoProduits": [4, 6]}, {"id": 59, "numVente": 7855, "numFacture": 1148, "dateVente": "2022-05-05T19:08:29Z", "dateFacture": "2022-06-10T19:30:22Z", "infoProduits": [6, 5]}, {"id": 60, "numVente": 1928, "numFacture": 3987, "dateVente": "2021-11-23T14:56:27Z", "dateFacture": "2022-05-14T02:36:42Z", "infoProduits": [9, 5]}, {"id": 61, "numVente": 3910, "numFacture": 7073, "dateVente": "2022-03-26T09:36:14Z", "dateFacture": "2021-12-10T21:24:33Z", "infoProduits": [9, 1]}, {"id": 62, "numVente": 1723, "numFacture": 7126, "dateVente": "2022-06-11T10:04:07Z", "dateFacture": "2022-03-01T20:21:09Z", "infoProduits": [1, 3]}, {"id": 63, "numVente": 7997, "numFacture": 1766, "dateVente": "2022-03-08T08:29:53Z", "dateFacture": "2021-12-11T13:01:16Z", "infoProduits": [6, 1]}, {"id": 64, "numVente": 2931, "numFacture": 1565, "dateVente": "2022-04-13T23:33:53Z", "dateFacture": "2021-09-30T12:01:42Z", "infoProduits": [6, 9]}, {"id": 65, "numVente": 4268, "numFacture": 1336, "dateVente": "2022-03-27T23:11:17Z", "dateFacture": "2021-08-02T23:05:59Z", "infoProduits": [1, 1]}, {"id": 66, "numVente": 1461, "numFacture": 5750, "dateVente": "2021-09-13T10:26:21Z", "dateFacture": "2021-06-29T11:35:39Z", "infoProduits": [9, 8]}, {"id": 67, "numVente": 3644, "numFacture": 7455, "dateVente": "2022-01-20T11:58:21Z", "dateFacture": "2021-08-24T17:16:17Z", "infoProduits": [1, 5]}, {"id": 68, "numVente": 8089, "numFacture": 8007, "dateVente": "2022-02-02T02:39:28Z", "dateFacture": "2021-09-06T00:24:17Z", "infoProduits": [1, 5]}, {"id": 69, "numVente": 2289, "numFacture": 7250, "dateVente": "2022-04-04T15:07:39Z", "dateFacture": "2021-12-15T00:28:20Z", "infoProduits": [3, 1]}, {"id": 70, "numVente": 1438, "numFacture": 7357, "dateVente": "2021-07-20T07:45:48Z", "dateFacture": "2022-01-14T08:08:57Z", "infoProduits": [8, 8]}, {"id": 71, "numVente": 6370, "numFacture": 7219, "dateVente": "2021-12-14T05:49:05Z", "dateFacture": "2021-11-10T23:30:11Z", "infoProduits": [7, 5]}, {"id": 72, "numVente": 8977, "numFacture": 1933, "dateVente": "2021-06-26T22:12:12Z", "dateFacture": "2022-05-10T16:05:32Z", "infoProduits": [8, 3]}, {"id": 73, "numVente": 8836, "numFacture": 7850, "dateVente": "2022-05-30T23:08:30Z", "dateFacture": "2021-12-28T20:51:38Z", "infoProduits": [8, 6]}, {"id": 74, "numVente": 6303, "numFacture": 8574, "dateVente": "2022-02-25T06:35:35Z", "dateFacture": "2021-09-19T10:08:00Z", "infoProduits": [7, 5]}, {"id": 75, "numVente": 7440, "numFacture": 2478, "dateVente": "2022-02-23T23:52:28Z", "dateFacture": "2021-09-16T05:34:52Z", "infoProduits": [3, 8]}, {"id": 76, "numVente": 7723, "numFacture": 2998, "dateVente": "2022-03-25T13:54:12Z", "dateFacture": "2022-02-02T17:28:14Z", "infoProduits": [4, 6]}, {"id": 77, "numVente": 9255, "numFacture": 5896, "dateVente": "2022-01-24T21:39:05Z", "dateFacture": "2021-12-26T01:53:35Z", "infoProduits": [1, 4]}, {"id": 78, "numVente": 3097, "numFacture": 6500, "dateVente": "2021-10-24T21:40:36Z", "dateFacture": "2021-08-23T01:40:29Z", "infoProduits": [8, 5]}, {"id": 79, "numVente": 7542, "numFacture": 2439, "dateVente": "2021-08-19T23:12:15Z", "dateFacture": "2022-02-28T09:57:03Z", "infoProduits": [2, 8]}, {"id": 80, "numVente": 9793, "numFacture": 6635, "dateVente": "2022-05-27T22:35:57Z", "dateFacture": "2022-04-15T16:52:36Z", "infoProduits": [1, 1]}, {"id": 81, "numVente": 1273, "numFacture": 8894, "dateVente": "2021-12-06T00:42:18Z", "dateFacture": "2022-02-14T07:32:46Z", "infoProduits": [9, 5]}, {"id": 82, "numVente": 2643, "numFacture": 1639, "dateVente": "2021-07-31T01:29:49Z", "dateFacture": "2021-10-11T18:19:10Z", "infoProduits": [8, 6]}, {"id": 83, "numVente": 9382, "numFacture": 1766, "dateVente": "2022-05-21T15:11:24Z", "dateFacture": "2022-03-25T21:23:42Z", "infoProduits": [3, 1]}, {"id": 84, "numVente": 6877, "numFacture": 1379, "dateVente": "2021-12-26T14:00:49Z", "dateFacture": "2021-12-26T23:02:51Z", "infoProduits": [4, 5]}, {"id": 85, "numVente": 9220, "numFacture": 2456, "dateVente": "2022-06-03T19:57:52Z", "dateFacture": "2021-10-19T23:41:25Z", "infoProduits": [4, 5]}, {"id": 86, "numVente": 7131, "numFacture": 1510, "dateVente": "2021-09-14T08:52:04Z", "dateFacture": "2021-12-06T20:59:46Z", "infoProduits": [9, 4]}, {"id": 87, "numVente": 4623, "numFacture": 7373, "dateVente": "2022-03-26T08:06:47Z", "dateFacture": "2022-02-22T13:22:16Z", "infoProduits": [1, 5]}, {"id": 88, "numVente": 7071, "numFacture": 1054, "dateVente": "2021-11-30T20:52:48Z", "dateFacture": "2021-07-18T21:17:13Z", "infoProduits": [2, 2]}, {"id": 89, "numVente": 2629, "numFacture": 7761, "dateVente": "2021-11-25T19:38:40Z", "dateFacture": "2021-12-04T05:47:31Z", "infoProduits": [2, 2]}, {"id": 90, "numVente": 2781, "numFacture": 9175, "dateVente": "2021-10-06T20:12:12Z", "dateFacture": "2022-05-28T02:22:30Z", "infoProduits": [7, 6]}, {"id": 91, "numVente": 6081, "numFacture": 9108, "dateVente": "2021-06-16T23:15:28Z", "dateFacture": "2022-03-05T11:08:24Z", "infoProduits": [2, 8]}, {"id": 92, "numVente": 5203, "numFacture": 8525, "dateVente": "2021-08-28T06:24:30Z", "dateFacture": "2021-09-09T13:29:36Z", "infoProduits": [6, 6]}, {"id": 93, "numVente": 8519, "numFacture": 5096, "dateVente": "2022-04-29T20:08:35Z", "dateFacture": "2021-07-06T04:17:01Z", "infoProduits": [4, 1]}, {"id": 94, "numVente": 2704, "numFacture": 3811, "dateVente": "2022-02-09T01:28:25Z", "dateFacture": "2022-04-10T23:11:53Z", "infoProduits": [9, 1]}, {"id": 95, "numVente": 9199, "numFacture": 5682, "dateVente": "2021-09-20T05:00:17Z", "dateFacture": "2022-02-16T12:30:44Z", "infoProduits": [7, 5]}, {"id": 96, "numVente": 6529, "numFacture": 7321, "dateVente": "2021-06-21T08:36:23Z", "dateFacture": "2022-01-28T00:39:04Z", "infoProduits": [7, 5]}, {"id": 97, "numVente": 4930, "numFacture": 4322, "dateVente": "2021-10-16T21:10:38Z", "dateFacture": "2021-09-21T03:00:50Z", "infoProduits": [1, 3]}, {"id": 98, "numVente": 8358, "numFacture": 3622, "dateVente": "2021-07-10T17:55:17Z", "dateFacture": "2022-01-05T01:08:44Z", "infoProduits": [8, 5]}, {"id": 99, "numVente": 5516, "numFacture": 8558, "dateVente": "2021-09-22T19:20:14Z", "dateFacture": "2021-12-02T16:56:10Z", "infoProduits": [9, 6]}, {"id": 100, "numVente": 3395, "numFacture": 9171, "dateVente": "2022-01-24T01:13:14Z", "dateFacture": "2021-11-19T11:01:45Z", "infoProduits": [6, 5]}], "infoProduits": [{"id": 1, "produit": 37, "qte": 98}, {"id": 2, "produit": 21, "qte": 1}, {"id": 3, "produit": 13, "qte": 5}, {"id": 4, "produit": 42, "qte": 922}, {"id": 5, "produit": 29, "qte": 231}, {"id": 6, "produit": 16, "qte": 694}, {"id": 7, "produit": 48, "qte": 127}, {"id": 8, "produit": 11, "qte": 74}, {"id": 9, "produit": 41, "qte": 253}, {"id": 10, "produit": 6, "qte": 509}, {"id": 11, "produit": 20, "qte": 978}, {"id": 12, "produit": 3, "qte": 928}, {"id": 13, "produit": 21, "qte": 655}, {"id": 14, "produit": 38, "qte": 125}, {"id": 15, "produit": 45, "qte": 101}, {"id": 16, "produit": 5, "qte": 300}, {"id": 17, "produit": 26, "qte": 132}, {"id": 18, "produit": 6, "qte": 52}, {"id": 19, "produit": 31, "qte": 151}, {"id": 20, "produit": 50, "qte": 560}, {"id": 21, "produit": 33, "qte": 291}, {"id": 22, "produit": 1, "qte": 975}, {"id": 23, "produit": 11, "qte": 296}, {"id": 24, "produit": 1, "qte": 94}, {"id": 25, "produit": 41, "qte": 369}, {"id": 26, "produit": 39, "qte": 635}, {"id": 27, "produit": 34, "qte": 16}, {"id": 28, "produit": 47, "qte": 449}, {"id": 29, "produit": 7, "qte": 207}, {"id": 30, "produit": 47, "qte": 167}, {"id": 31, "produit": 7, "qte": 622}, {"id": 32, "produit": 37, "qte": 322}, {"id": 33, "produit": 19, "qte": 222}, {"id": 34, "produit": 37, "qte": 550}, {"id": 35, "produit": 24, "qte": 905}, {"id": 36, "produit": 50, "qte": 169}, {"id": 37, "produit": 18, "qte": 535}, {"id": 38, "produit": 14, "qte": 551}, {"id": 39, "produit": 50, "qte": 524}, {"id": 40, "produit": 50, "qte": 641}, {"id": 41, "produit": 17, "qte": 830}, {"id": 42, "produit": 39, "qte": 915}, {"id": 43, "produit": 1, "qte": 14}, {"id": 44, "produit": 10, "qte": 498}, {"id": 45, "produit": 30, "qte": 956}, {"id": 46, "produit": 18, "qte": 953}, {"id": 47, "produit": 6, "qte": 697}, {"id": 48, "produit": 21, "qte": 80}, {"id": 49, "produit": 32, "qte": 705}, {"id": 50, "produit": 19, "qte": 519}, {"id": 51, "produit": 38, "qte": 150}, {"id": 52, "produit": 5, "qte": 909}, {"id": 53, "produit": 41, "qte": 428}, {"id": 54, "produit": 41, "qte": 745}, {"id": 55, "produit": 3, "qte": 22}, {"id": 56, "produit": 37, "qte": 342}, {"id": 57, "produit": 36, "qte": 65}, {"id": 58, "produit": 30, "qte": 554}, {"id": 59, "produit": 3, "qte": 88}, {"id": 60, "produit": 45, "qte": 29}, {"id": 61, "produit": 40, "qte": 138}, {"id": 62, "produit": 35, "qte": 402}, {"id": 63, "produit": 27, "qte": 309}, {"id": 64, "produit": 14, "qte": 220}, {"id": 65, "produit": 19, "qte": 277}, {"id": 66, "produit": 9, "qte": 369}, {"id": 67, "produit": 13, "qte": 958}, {"id": 68, "produit": 25, "qte": 609}, {"id": 69, "produit": 42, "qte": 999}, {"id": 70, "produit": 20, "qte": 984}, {"id": 71, "produit": 24, "qte": 153}, {"id": 72, "produit": 23, "qte": 792}, {"id": 73, "produit": 40, "qte": 262}, {"id": 74, "produit": 34, "qte": 744}, {"id": 75, "produit": 21, "qte": 845}, {"id": 76, "produit": 31, "qte": 942}, {"id": 77, "produit": 13, "qte": 572}, {"id": 78, "produit": 37, "qte": 975}, {"id": 79, "produit": 37, "qte": 959}, {"id": 80, "produit": 46, "qte": 464}, {"id": 81, "produit": 16, "qte": 344}, {"id": 82, "produit": 35, "qte": 139}, {"id": 83, "produit": 39, "qte": 766}, {"id": 84, "produit": 5, "qte": 372}, {"id": 85, "produit": 43, "qte": 290}, {"id": 86, "produit": 15, "qte": 524}, {"id": 87, "produit": 7, "qte": 136}, {"id": 88, "produit": 30, "qte": 738}, {"id": 89, "produit": 12, "qte": 825}, {"id": 90, "produit": 19, "qte": 726}, {"id": 91, "produit": 1, "qte": 546}, {"id": 92, "produit": 42, "qte": 475}, {"id": 93, "produit": 49, "qte": 363}, {"id": 94, "produit": 10, "qte": 803}, {"id": 95, "produit": 24, "qte": 548}, {"id": 96, "produit": 2, "qte": 685}, {"id": 97, "produit": 10, "qte": 601}, {"id": 98, "produit": 2, "qte": 22}, {"id": 99, "produit": 3, "qte": 424}, {"id": 100, "produit": 47, "qte": 70}], "ventilations": [{"id": 1, "dateVente": "2022-06-05T02:52:57Z", "numVente": 7114, "operateur": 4, "mntPayee": 6951, "type": "P", "typeVente": "E", "mntNet": 6957, "restAPayee": 9147, "statut": "I"}, {"id": 2, "dateVente": "2022-01-30T07:34:04Z", "numVente": 71889, "operateur": 3, "mntPayee": 7986, "type": "N", "typeVente": "V", "mntNet": 1741, "restAPayee": 5968, "statut": "I"}, {"id": 3, "dateVente": "2021-08-01T20:36:58Z", "numVente": 51948, "operateur": 3, "mntPayee": 9309, "type": "N", "typeVente": "V", "mntNet": 9912, "restAPayee": 4636, "statut": "TP"}, {"id": 4, "dateVente": "2022-05-20T00:53:14Z", "numVente": 11651, "operateur": 3, "mntPayee": 1994, "type": "N", "typeVente": "T", "mntNet": 2641, "restAPayee": 3270, "statut": "I"}, {"id": 5, "dateVente": "2021-09-19T17:15:28Z", "numVente": 93685, "operateur": 5, "mntPayee": 2431, "type": "N", "typeVente": "V", "mntNet": 5511, "restAPayee": 2027, "statut": "PP"}, {"id": 6, "dateVente": "2022-02-01T00:46:50Z", "numVente": 41067, "operateur": 1, "mntPayee": 6987, "type": "P", "typeVente": "E", "mntNet": 2233, "restAPayee": 3008, "statut": "I"}, {"id": 7, "dateVente": "2021-10-19T06:40:19Z", "numVente": 22452, "operateur": 4, "mntPayee": 4937, "type": "N", "typeVente": "T", "mntNet": 8294, "restAPayee": 4211, "statut": "I"}, {"id": 8, "dateVente": "2021-12-11T23:15:29Z", "numVente": 31914, "operateur": 3, "mntPayee": 7658, "type": "N", "typeVente": "E", "mntNet": 5719, "restAPayee": 1230, "statut": "I"}, {"id": 9, "dateVente": "2021-12-16T13:14:22Z", "numVente": 49317, "operateur": 3, "mntPayee": 500, "type": "P", "typeVente": "V", "mntNet": 4331, "restAPayee": 1865, "statut": "I"}, {"id": 10, "dateVente": "2022-02-20T18:40:15Z", "numVente": 63598, "operateur": 4, "mntPayee": 6976, "type": "P", "typeVente": "E", "mntNet": 5722, "restAPayee": 6695, "statut": "PP"}]}