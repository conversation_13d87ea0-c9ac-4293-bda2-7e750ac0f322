import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';
import { TypeBla } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeBla.enum';
import { Fournisseur } from '../../tiers/fournisseur/fournisseur.model';

// import { Moment } from 'moment';



export class BlAchatCriteria { 
    axeAnalytique?: string;
    dateCreation?: any;
    dateDebut?: any;
    dateFin?: any;
    etatBla?: TypeBla;
    fournisseurId?: number;
    isFacture?: boolean;
    numeroBl?: number;
    numeroCmd?: number;
    statut?: Statut;
    fournisseur?:Fournisseur
}

