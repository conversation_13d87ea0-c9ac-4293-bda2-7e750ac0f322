import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment as env } from "src/environments/environment";
import { Province } from "../models/province";
@Injectable({
  providedIn: "root",
})
export class WinClientProvinces {
    constructor(private http:HttpClient) {}


    getProvinces() {
        return this.http.get<Province[]>(`${env.winclient_base_url}/api/winclient/provinces`);
    }
}