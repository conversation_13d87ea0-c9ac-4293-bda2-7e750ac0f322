<!-- Backdrop -->
<div 
  *ngIf="showBackdrop && isOpen"
  class="drawer-backdrop"
  [class.drawer-backdrop--visible]="isOpen"
  [attr.aria-hidden]="!isOpen"
  (click)="onBackdropClick($event)"
  [style.pointer-events]="closeOnBackdropClick ? 'auto' : 'none'">
</div>

<!-- Drawer Container -->
<div 
  #drawerContainer
  [class]="drawerClasses"
  [style]="drawerStyles"
  [attr.id]="drawerId"
  [attr.role]="'dialog'"
  [attr.aria-modal]="isOpen"
  [attr.aria-label]="ariaLabel || null"
  [attr.aria-labelledby]="ariaLabelledBy || (title ? drawerLabelId : null)"
  [attr.aria-hidden]="!isOpen"
  [attr.tabindex]="-1">
  
  <!-- Drawer Content -->
  <div 
    #drawerContent
    class="drawer__content"
    (click)="$event.stopPropagation()">
    
    <!-- Header -->
    <div class="drawer__header" *ngIf="title || showCloseButton">
      <div class="drawer__header-content">
        <!-- Title -->
        <h2 
          *ngIf="title"
          [attr.id]="drawerLabelId"
          class="drawer__title">
          {{ title }}
        </h2>
        
        <!-- Custom header content -->
        <ng-content select="[slot=header]"></ng-content>
      </div>
      
      <!-- Close Button -->
      <button 
        *ngIf="showCloseButton"
        type="button"
        class="drawer__close-button"
        [attr.aria-label]="'Fermer ' + (title || 'drawer')"
        (click)="onCloseButtonClick()">
        <i class="mdi mdi-close" aria-hidden="true"></i>
      </button>
    </div>
    
    <!-- Body -->
    <div class="drawer__body">
      <ng-content></ng-content>
    </div>
    
    <!-- Footer -->
    <div class="drawer__footer" *ngIf="hasFooterContent">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
  </div>
</div>

<!-- Screen Reader Announcements -->
<div 
  class="sr-only" 
  [attr.aria-live]="'polite'"
  [attr.aria-atomic]="true">
  <span *ngIf="isOpen">{{ title || 'Drawer' }} ouvert</span>
  <span *ngIf="!isOpen && title">{{ title }} fermé</span>
</div>
