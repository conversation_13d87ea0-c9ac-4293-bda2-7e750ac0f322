import { TypeAssistantAchat } from "src/app/references-app/references-produits/enums/achat-avoir/TypeAssistantAchat.enum";
import { ConfigurationResolutionConflitAssistantAchat } from "src/app/references-app/references-produits/enums/parametre/ConfigurationResolutionConflitAssistantAchat.enum";



export class ParametresAchat { 
    audited?: boolean;
    listeAssistantsCmdOrdonnes?: TypeAssistantAchat[];
    regleResolutionConflitAssistantAchat?: ConfigurationResolutionConflitAssistantAchat;
    userModifiable?: boolean;
}

