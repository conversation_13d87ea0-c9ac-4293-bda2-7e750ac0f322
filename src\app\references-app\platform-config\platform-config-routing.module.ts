import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExecutionBatchComponent } from './pages/execution-batch/execution-batch.component';
import { LayoutContainerComponent } from 'src/app/layouts/layout-container.component';
import { GestionUtilisateurComponent } from './pages/gestion-utilisateur/gestion-utilisateur.component';

const routes: Routes = [
{
  path: '',
  component: LayoutContainerComponent,
  children:[
    {
      path: 'execution-batch',
      component: ExecutionBatchComponent,
    },
    {
      path: 'gestion-utilisateur',
      component: GestionUtilisateurComponent,
    }
  ],
}
 
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PlatformConfigRoutingModule { }
