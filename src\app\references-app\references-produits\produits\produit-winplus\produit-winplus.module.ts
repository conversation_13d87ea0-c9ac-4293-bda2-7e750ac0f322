import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {ListProduitWinplusComponent } from './list-produit-winplus/list-produit-winplus.component';
import { ProduitWinplusRoutingModule } from './produit-winplus-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GridModule } from '@progress/kendo-angular-grid';
import { SharedModule } from "../../../../shared/shared.module";
import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  imports: [
    CommonModule,
    ProduitWinplusRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    GridModule,
    SharedModule,
    NgbTypeaheadModule
],
  declarations: [ListProduitWinplusComponent]
})
export class ProduitFournisseurModule { }
