import { TypeVente } from 'src/app/references-app/references-produits/enums/vente/TypeVente.enum';
import { TypeEncaissementVente } from 'src/app/references-app/references-produits/enums/vente/TypeEncaissementVente.enum';
import { ModePaiement } from 'src/app/references-app/references-produits/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { Client } from 'src/app/references-app/references-produits/models/tiers/client/client.model';
import { ConventionAssurance } from 'src/app/references-app/references-produits/models/assurance/conventionAssurance.model';
import { Depot } from 'src/app/references-app/references-produits/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Medecin } from 'src/app/references-app/references-produits/models/common/medecin.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { Produit } from 'src/app/references-app/references-produits/models/produit/base/produit.model';
import { Rayon } from 'src/app/references-app/references-produits/models/produit/base/rayon.model';
import { TypeClient } from 'src/app/references-app/references-produits/enums/tiers/TypeClient.enum';


export class StatistiquesVentesCriteria { 
    categorieProduit?: CategorieProduit;
    client?: Client;
    convention?: ConventionAssurance;
    dateDebut?: any;
    dateFin?: any;
    familleTarifaire?: FamilleTarifaire;
    forme?: FormeProduit;
    laboratoire?: Fournisseur;
    listeTypesVentes?: TypeVente[];
    medecin?: Medecin;
    modePaiement?: ModePaiement;
    operateur?: Operateur;
    produit?: Produit;
    rayon?: Rayon;
    typeClient?: TypeClient;
    typeEncaissement?: TypeEncaissementVente;
    zone?: Depot;
}

