{"info": {"_postman_id": "925311f0-19ea-4e92-a9af-c69bfcbdfa6f", "name": "Impression APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "32602638"}, "item": [{"name": "AutreImpression", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default", "disabled": true}, {"key": "Accept-Encoding", "value": "gzip, deflate", "type": "default"}, {"key": "Content-Type", "value": "application/json", "type": "default", "disabled": true}, {"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\"dateDebut\":\"2023-09-01 00:00:00\",\"dateFin\":\"2023-11-28 00:00:00\"}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:5005/api/winpharm/autresimpression/47", "protocol": "http", "host": ["192", "168", "101", "38"], "port": "5005", "path": ["api", "winpharm", "autresimpression", "47"]}}, "response": []}, {"name": "ImpressionSingle", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "default"}, {"key": "Accept", "value": "application/json, text/plain, */*", "type": "default", "disabled": true}, {"key": "Accept-Encoding", "value": "gzip, deflate", "type": "default"}, {"key": "Content-Type", "value": "application/json", "type": "default", "disabled": true}], "url": {"raw": "http://**************:5005/api/winpharm/impression?actionName=CMDACHAT&idFlux=220", "protocol": "http", "host": ["192", "168", "101", "38"], "port": "5005", "path": ["api", "winpharm", "impression"], "query": [{"key": "actionName", "value": "CMDACHAT"}, {"key": "idFlux", "value": "220"}]}}, "response": []}, {"name": "ImpressionMultiple", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\"listActionsFlux\" :[\r\n    {\r\n        \"idFlux\": 233,\r\n        \"actionName\": \"FTPAONE\"\r\n    },\r\n    {\r\n        \"idFlux\": 233,\r\n        \"actionName\": \"RECAPONE\"\r\n    },\r\n       {\r\n        \"idFlux\": 233,\r\n        \"actionName\": \"FTPADET\"\r\n    }\r\n]}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**************:5005/api/winpharm/impression/multiple", "protocol": "http", "host": ["192", "168", "101", "38"], "port": "5005", "path": ["api", "winpharm", "impression", "multiple"]}}, "response": []}, {"name": "CMDACHAT", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tokenUser}}", "type": "text"}, {"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=CMDACHAT&idFlux=23", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "CMDACHAT"}, {"key": "idFlux", "value": "23"}]}}, "response": []}, {"name": "BLACHAT2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=BLACHAT2&idFlux=511", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "BLACHAT2"}, {"key": "idFlux", "value": "511"}]}}, "response": []}, {"name": "DMDAVOIR", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=DMDAVOIR&idFlux=135", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "DMDAVOIR"}, {"key": "idFlux", "value": "135"}]}}, "response": []}, {"name": "BLAVOIRFRN", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=BLAVOIRFRN&idFlux=491", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "BLAVOIRFRN"}, {"key": "idFlux", "value": "491"}]}}, "response": []}, {"name": "SOLDEECHG", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=SOLDEECHG&idFlux=56", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "SOLDEECHG"}, {"key": "idFlux", "value": "56"}]}}, "response": []}, {"name": "HistEncClient(5)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-12-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-19 00:00:00\",\r\n    \"client\": {\"id\": 1},\r\n    \"listTypesOperations\": [\r\n        \"V\",\r\n        \"A\",\r\n        \"S\",\r\n        \"E\"\r\n    ],\r\n    \"isArchive\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/5", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "5"]}}, "response": []}, {"name": "HistPrdEchConf(15a)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateEchangeMin\": \"2023-11-01 00:00:00\",\r\n    \"dateDebut\": \"2023-11-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-19 00:00:00\",\r\n    \"dateEchangeMax\": \"2023-12-19 00:00:00\",\r\n    \"confrere\": {\"id\": 2},\r\n    \"confrereId\": 2\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/15a", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "15a"]}}, "response": []}, {"name": "ECHANGE", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=ECHANGE&idFlux=192", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "ECHANGE"}, {"key": "idFlux", "value": "192"}]}}, "response": []}, {"name": "ECHANGE", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=ECHANGE&idFlux=191", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "ECHANGE"}, {"key": "idFlux", "value": "191"}]}}, "response": []}, {"name": "VENTE", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=VENTE&idFlux=1268", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "VENTE"}, {"key": "idFlux", "value": "1268"}]}}, "response": []}, {"name": "FTPACLT", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=FTPACLT&idFlux=1269", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "FTPACLT"}, {"key": "idFlux", "value": "1269"}]}}, "response": []}, {"name": "BLVC", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=FTPACLT&idFlux=1250", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "FTPACLT"}, {"key": "idFlux", "value": "1250"}]}}, "response": []}, {"name": "DEVIS", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=DEVIS&idFlux=1212", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "DEVIS"}, {"key": "idFlux", "value": "1212"}]}}, "response": []}, {"name": "FTPAPEC", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=FTPAPEC&idFlux=1212", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "FTPAPEC"}, {"key": "idFlux", "value": "1212"}]}}, "response": []}, {"name": "BLRVC", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=BLRVC&idFlux=1122", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "BLRVC"}, {"key": "idFlux", "value": "1122"}]}}, "response": []}, {"name": "FACTCLT1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=FACTCLT1&idFlux=63", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "FACTCLT1"}, {"key": "idFlux", "value": "63"}]}}, "response": []}, {"name": "FTPADETVT", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/{{OneImpression}}?actionName=FTPADETVT&idFlux=233", "host": ["{{baseUrl}}"], "path": ["{{OneImpression}}"], "query": [{"key": "actionName", "value": "FTPADETVT"}, {"key": "idFlux", "value": "233"}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(39)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-12-19 14:40:43\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/39", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "39"]}}, "response": []}, {"name": "print61a", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    // \"dateDebut\": \"2022-01-01 00:00:00\",\r\n    // \"dateFin\": \"2023-12-31 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/61a", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "61a"]}}, "response": []}, {"name": "print61b", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2022-01-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-31 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/61b", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "61b"]}}, "response": []}, {"name": "print61c", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2022-01-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-31 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/61c", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "61c"]}}, "response": []}, {"name": "print62", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-12-01 14:40:43\",\r\n    \"dateFin\": \"2023-12-31 14:40:43\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/62", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "62"]}}, "response": []}, {"name": "print63", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-01-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-19 15:00:45\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/63", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "63"]}}, "response": []}, {"name": "print63_a", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2022-01-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-31 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/63_a", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "63_a"]}}, "response": []}, {"name": "VenteFT(87)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-11-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-20 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/87", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "87"]}}, "response": []}, {"name": "FactureTpa_Organisme(47)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-10-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-19 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/47", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "47"]}}, "response": []}, {"name": "RecapFactureTPA(46)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"dateDebut\": \"2023-10-01 00:00:00\",\r\n    \"dateFin\": \"2023-12-19 00:00:00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/46", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "46"]}}, "response": []}, {"name": "ImpressionAMO", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "body": {"mode": "raw", "raw": "{   \r\n\"listActionsFlux\":[\r\n        {\r\n            \"idFlux\": 233,\r\n            \"actionName\": \"FTPAONE\"\r\n        },\r\n        {\r\n            \"idFlux\": 233,\r\n            \"actionName\": \"RECAPONE\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{MultipleImpression}}", "host": ["{{baseUrl}}"], "path": ["{{MultipleImpression}}"]}}, "response": []}, {"name": "ImpressionONE", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"listActionsFlux\": [\r\n        {\r\n            \"idFlux\": 233,\r\n            \"actionName\": \"FTPAONE\"\r\n        },\r\n        {\r\n            \"idFlux\": 233,\r\n            \"actionName\": \"RECAPONE\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{MultipleImpression}}", "host": ["{{baseUrl}}"], "path": ["{{MultipleImpression}}"]}}, "response": []}, {"name": "ImpressionBRD", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"listActionsFlux\": [\r\n        {\r\n            \"idFlux\": 233,\r\n            \"actionName\": \"BRD\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{MultipleImpression}}", "host": ["{{baseUrl}}"], "path": ["{{MultipleImpression}}"]}}, "response": []}, {"name": "printDetailVentesFactureTpa(48)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Check if request type is PDF\", function () {\r", "    // Check if the Content-Type header contains 'application/pdf'\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/pdf\");\r", "});\r", "\r", "pm.test(\"Check if status code is 200\", function () {\r", "    // Check if the status code is 200\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/{{AutreImpression}}/48", "host": ["{{baseUrl}}"], "path": ["{{AutreImpression}}", "48"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{tokenUser}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.request.headers.add({", "    key: \"Authorizationtenant\",", "    value: pm.variables.get(\"tokenTenant\")", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}