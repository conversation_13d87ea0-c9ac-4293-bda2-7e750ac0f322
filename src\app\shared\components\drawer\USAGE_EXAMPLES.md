# Drawer Component Usage Examples

## Quick Start

Add this to any component template to test the drawer:

```html
<!-- Simple trigger button -->
<button type="button" class="btn btn-primary" (click)="testDrawerOpen = true">
  Open Test Drawer
</button>

<!-- Basic drawer -->
<app-drawer
  [(isOpen)]="testDrawerOpen"
  title="Test Drawer"
  position="right"
  size="medium">
  
  <div class="p-3">
    <h4>Hello World!</h4>
    <p>This is a test drawer with all accessibility features enabled.</p>
    
    <div class="mt-3">
      <button type="button" class="btn btn-primary me-2">Test Button</button>
      <button type="button" class="btn btn-secondary" (click)="testDrawerOpen = false">
        Close
      </button>
    </div>
  </div>
</app-drawer>
```

Add this to your component TypeScript file:

```typescript
export class YourComponent {
  testDrawerOpen = false;
}
```

## Form Example

```html
<button type="button" class="btn btn-success" (click)="openFormDrawer()">
  Open Form Drawer
</button>

<app-drawer
  [(isOpen)]="formDrawerOpen"
  title="Contact Form"
  position="right"
  size="large"
  [closeOnBackdropClick]="false">
  
  <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="p-3">
    <div class="mb-3">
      <label for="name" class="form-label">Name *</label>
      <input 
        type="text" 
        class="form-control" 
        id="name" 
        formControlName="name"
        appAutoFocus>
    </div>
    
    <div class="mb-3">
      <label for="email" class="form-label">Email *</label>
      <input 
        type="email" 
        class="form-control" 
        id="email" 
        formControlName="email">
    </div>
    
    <div class="mb-3">
      <label for="message" class="form-label">Message</label>
      <textarea 
        class="form-control" 
        id="message" 
        rows="4" 
        formControlName="message"></textarea>
    </div>
  </form>
  
  <div slot="footer" class="d-flex justify-content-end gap-2">
    <button type="button" class="btn btn-secondary" (click)="formDrawerOpen = false">
      Cancel
    </button>
    <button 
      type="button" 
      class="btn btn-primary" 
      (click)="onSubmit()"
      [disabled]="contactForm.invalid">
      Send Message
    </button>
  </div>
</app-drawer>
```

```typescript
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

export class YourComponent {
  formDrawerOpen = false;
  contactForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.contactForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      message: ['']
    });
  }

  openFormDrawer() {
    this.contactForm.reset();
    this.formDrawerOpen = true;
  }

  onSubmit() {
    if (this.contactForm.valid) {
      console.log('Form data:', this.contactForm.value);
      this.formDrawerOpen = false;
    }
  }
}
```

## Navigation Drawer

```html
<button type="button" class="btn btn-outline-primary" (click)="navDrawerOpen = true">
  <i class="mdi mdi-menu"></i> Menu
</button>

<app-drawer
  [(isOpen)]="navDrawerOpen"
  title="Navigation"
  position="left"
  size="small">
  
  <nav class="p-3">
    <ul class="list-unstyled">
      <li class="mb-2">
        <a href="#" class="d-flex align-items-center text-decoration-none p-2 rounded">
          <i class="mdi mdi-home me-2"></i>
          Dashboard
        </a>
      </li>
      <li class="mb-2">
        <a href="#" class="d-flex align-items-center text-decoration-none p-2 rounded">
          <i class="mdi mdi-account me-2"></i>
          Users
        </a>
      </li>
      <li class="mb-2">
        <a href="#" class="d-flex align-items-center text-decoration-none p-2 rounded">
          <i class="mdi mdi-cog me-2"></i>
          Settings
        </a>
      </li>
      <li class="mb-2">
        <a href="#" class="d-flex align-items-center text-decoration-none p-2 rounded">
          <i class="mdi mdi-chart-line me-2"></i>
          Reports
        </a>
      </li>
    </ul>
  </nav>
</app-drawer>
```

## Filter Drawer

```html
<button type="button" class="btn btn-info" (click)="filterDrawerOpen = true">
  <i class="mdi mdi-filter"></i> Filters
</button>

<app-drawer
  [(isOpen)]="filterDrawerOpen"
  title="Filter Options"
  position="right"
  size="medium">
  
  <form [formGroup]="filterForm" class="p-3">
    <div class="mb-3">
      <label class="form-label">Date Range</label>
      <div class="row">
        <div class="col-6">
          <input type="date" class="form-control" formControlName="startDate">
        </div>
        <div class="col-6">
          <input type="date" class="form-control" formControlName="endDate">
        </div>
      </div>
    </div>
    
    <div class="mb-3">
      <label class="form-label">Category</label>
      <select class="form-select" formControlName="category">
        <option value="">All Categories</option>
        <option value="electronics">Electronics</option>
        <option value="clothing">Clothing</option>
        <option value="books">Books</option>
      </select>
    </div>
    
    <div class="mb-3">
      <label class="form-label">Price Range</label>
      <div class="row">
        <div class="col-6">
          <input type="number" class="form-control" placeholder="Min" formControlName="minPrice">
        </div>
        <div class="col-6">
          <input type="number" class="form-control" placeholder="Max" formControlName="maxPrice">
        </div>
      </div>
    </div>
    
    <div class="form-check">
      <input type="checkbox" class="form-check-input" id="inStock" formControlName="inStock">
      <label class="form-check-label" for="inStock">
        In Stock Only
      </label>
    </div>
  </form>
  
  <div slot="footer" class="d-flex justify-content-between">
    <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">
      Clear All
    </button>
    <div>
      <button type="button" class="btn btn-secondary me-2" (click)="filterDrawerOpen = false">
        Cancel
      </button>
      <button type="button" class="btn btn-primary" (click)="applyFilters()">
        Apply Filters
      </button>
    </div>
  </div>
</app-drawer>
```

## Notification Panel

```html
<button type="button" class="btn btn-warning position-relative" (click)="notificationDrawerOpen = true">
  <i class="mdi mdi-bell"></i>
  <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
    3
  </span>
</button>

<app-drawer
  [(isOpen)]="notificationDrawerOpen"
  title="Notifications"
  position="top"
  size="medium">
  
  <div class="p-3">
    <div class="list-group list-group-flush">
      <div class="list-group-item d-flex justify-content-between align-items-start">
        <div class="ms-2 me-auto">
          <div class="fw-bold">New Order</div>
          Order #12345 has been placed
        </div>
        <small>3m ago</small>
      </div>
      <div class="list-group-item d-flex justify-content-between align-items-start">
        <div class="ms-2 me-auto">
          <div class="fw-bold">System Update</div>
          Maintenance scheduled for tonight
        </div>
        <small>1h ago</small>
      </div>
      <div class="list-group-item d-flex justify-content-between align-items-start">
        <div class="ms-2 me-auto">
          <div class="fw-bold">Payment Received</div>
          Payment for invoice #INV-001 received
        </div>
        <small>2h ago</small>
      </div>
    </div>
  </div>
  
  <div slot="footer" class="text-center">
    <button type="button" class="btn btn-link">View All Notifications</button>
  </div>
</app-drawer>
```

## Using with Service

```typescript
import { DrawerService } from './shared/services/drawer.service';

export class YourComponent implements OnInit {
  
  constructor(private drawerService: DrawerService) {}
  
  ngOnInit() {
    // Register drawers
    this.drawerService.register({
      id: 'user-details',
      isOpen: false,
      title: 'User Details',
      position: 'right',
      size: 'large'
    });
  }
  
  openUserDetails(userId: number) {
    this.drawerService.open('user-details', { userId });
  }
  
  closeAllDrawers() {
    this.drawerService.closeAll();
  }
}
```

## Accessibility Testing

To test accessibility features:

1. **Keyboard Navigation**: Use Tab, Shift+Tab, and Escape keys
2. **Screen Reader**: Test with NVDA, JAWS, or VoiceOver
3. **Focus Management**: Verify focus moves correctly when opening/closing
4. **ARIA Attributes**: Check with browser dev tools

```typescript
// Example accessibility test
it('should trap focus within drawer', () => {
  component.testDrawerOpen = true;
  fixture.detectChanges();
  
  // Tab through all focusable elements
  const focusableElements = fixture.debugElement.queryAll(
    By.css('button:not([disabled]), input:not([disabled]), select:not([disabled])')
  );
  
  expect(focusableElements.length).toBeGreaterThan(0);
  
  // Test that focus cycles within drawer
  const firstElement = focusableElements[0].nativeElement;
  const lastElement = focusableElements[focusableElements.length - 1].nativeElement;
  
  firstElement.focus();
  expect(document.activeElement).toBe(firstElement);
});
```

## Performance Tips

1. Use `OnPush` change detection for better performance
2. Lazy load drawer content when possible
3. Avoid complex animations on mobile devices
4. Use virtual scrolling for long lists in drawers

```typescript
@Component({
  selector: 'app-my-drawer',
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <app-drawer [(isOpen)]="isOpen">
      <ng-container *ngIf="isOpen">
        <!-- Lazy loaded content -->
        <app-heavy-component></app-heavy-component>
      </ng-container>
    </app-drawer>
  `
})
export class MyDrawerComponent {
  isOpen = false;
}
