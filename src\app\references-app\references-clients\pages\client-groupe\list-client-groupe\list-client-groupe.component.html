

<div  style="    position: fixed;
top: 0;
right: 0;
width: 100%;
height: 100%;
transition: all .3s;
z-index: 1002;
background: rgba(0, 0, 0, 0.4);" #drawerContainer [style]="isFilterDrawerOpen ? 'visibility: visible; pointer-events: auto;opacity: 1 ' : 'visibility: hidden; pointer-events: none;opacity: 0'" (click)="toggleFilterDrawer(false)">
<div class="bg-white" [style]="isFilterDrawerOpen ? 'transform: translateX(0)' : 'transform: translateX(100%)'" style="width: 600px;height: 100%; right: 0; position: fixed;transition: all .3s;"  (click)="$event.stopPropagation()"  #drawerContent>
  <div class="modal-header">
    <h5 class="modal-title" style="line-height: 1;">Filtrer Client Groupe</h5>
    <button type="button" class="cross-button" data-bs-dismiss="modal" aria-label="Close" (click)="toggleFilterDrawer(false)">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body d-flex flex-column" style="height: calc(100% - 46px);">
    <form style="display: contents;" [formGroup]="clientGroupeFilterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
      <div class="flex-grow-1">
      <div class="row">
        <div class="col-6 mb-2">
          <label for="codeClientGroupe" class="form-label">Code Groupe</label>
          <input id="codeClientGroupe" formControlName="codeClientGroupe" class="form-control" id="codeGroupe"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="nomPharmacien" class="form-label">Nom Pharmacien</label>
          <input id="nomPharmacien" formControlName="nomPharmacien" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="raisonSociale" class="form-label">Raison Social</label>
          <input id="raisonSociale" formControlName="raisonSociale" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="ville" class="form-label">Ville</label>
          <input id="ville" formControlName="ville" class="form-control" placeholder="taper ville"   type="search"
          [ngbTypeahead]="searchVilleTypeahead" [inputFormatter]="formatterVille" [resultFormatter]="formatterVille" [editable]="false">
        </div>
        <div class="col-6 mb-2">
          <label for="telephone" class="form-label">Telephone</label>
          <input id="telephone" formControlName="telephone" class="form-control"   type="search">
        </div>
        <!-- <div class="col-6 mb-2">
          <label for="telephone2" class="form-label">Telephone 2</label>
          <input id="telephone2" formControlName="telephone2" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="gsm" class="form-label">Gsm</label>
          <input id="gsm" formControlName="gsm" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="whatsapp" class="form-label">Whatsapp</label>
          <input id="whatsapp" formControlName="whatsapp" class="form-control"   type="search">
        </div> -->
        <div class="col-6 mb-2">
          <label for="classification" class="form-label">Classification</label>
          <input id="classification" formControlName="classification" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="adress" class="form-label">Adress</label>
          <input id="adress" formControlName="adress" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="tag" class="form-label">Tag</label>
          <input id="tag" formControlName="tag" class="form-control"   type="search">
        </div>
        <div class="col-12">
          <label for="segment" class="form-label mb-0">Type de Client</label>
          <app-switch [elements]='segmentFilterOptions' name="segment" formControlName="segment" [switchClass]="'info'"></app-switch>
        </div>
      </div>
      
    </div>
      <div class="row mt-3">
        <div class="col-12 d-flex flex-wrap gap-2 justify-content-start">
          <button class="btn btn-primary" tabindex="-1" type="submit">Recherche</button>
          <button class="btn btn-dark" tabindex="-1" type="button" (click)="resetFilters()">Vider</button>
        </div>
      </div>
    </form>
  </div>
</div>
</div>


<div  style="    position: fixed;
top: 0;
right: 0;
width: 100%;
height: 100%;
transition: all .3s;
z-index: 1002;
background: rgba(0, 0, 0, 0.4);"
(click)="modalMode === 'VIEW' ? toggleCreateDrawer(false) : null"
 [style]="isCreateDrawerOpen ? 'visibility: visible; pointer-events: auto;opacity: 1 ' : 'visibility: hidden; pointer-events: none;opacity: 0'" >
<div class="bg-white d-flex flex-column" [style]="isCreateDrawerOpen ? 'transform: translateX(0)' : 'transform: translateX(100%)'" style="width: 70%;height: 100%; right: 0; position: fixed;transition: all .3s;"  (click)="$event.stopPropagation()"  #drawerContent>
  <div class="modal-header">
    <h5 class="modal-title" style="line-height: 1;">{{modalMode === 'CREATE' ? 'Créer' : 'Modifier'}} Client Groupe</h5>
    <button type="button" class="cross-button" data-bs-dismiss="modal" aria-label="Close" (click)="toggleCreateDrawer(false)">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
   <form [formGroup]="clientGroupeForm" class="container mt-2 flex-grow-1 d-flex flex-column w-100" (ngSubmit)="onSubmit()" appFocusTrap >

  <div class="modal-body" style=" scrollbar-width: auto;">
     <div class="row">
      <div class="col-8">
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="raisonSociale" class="form-label">Raison Sociale<span class="text-danger">*</span></label>
            <input id="raisonSociale"  formControlName="raisonSociale" class="form-control raison-social-create" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('raisonSociale')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('raisonSociale')?.invalid && formSubmited" class="text-danger">
              Raison Sociale est obligatoire.
            </div>
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="nomDuPharmacien" class="form-label">Nom du Pharmacien<span class="text-danger">*</span></label>
            <input id="nomDuPharmacien" formControlName="nomDuPharmacien" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('nomDuPharmacien')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('nomDuPharmacien')?.invalid && formSubmited" class="text-danger">
              Nom du Pharmacien est obligatoire.
            </div>
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="adresse" class="form-label">Adresse<span class="text-danger">*</span></label>
            <input id="adresse" formControlName="adresse" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('adresse')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('adresse')?.invalid && formSubmited" class="text-danger">
              Adresse est obligatoire.
            </div>
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="adresseComplement" class="form-label">Adresse (Complément)</label>
            <input id="adresseComplement" formControlName="adresseComplement" class="form-control">
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="localite" class="form-label">Localité</label>
            <input [ngbTypeahead]="searchLocaliteTypeahead" class="form-control" id="localite"
            formControlName="localite" [inputFormatter]="formatterLocalite"  type="search"
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('localite')?.invalid}"
            placeholder="taper localité"  [editable]="false"
            [resultFormatter]="formatterLocalite">
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="ville" class="form-label">Ville<span class="text-danger">*</span></label>
            <input [ngbTypeahead]="searchVilleTypeahead" class="form-control" id="localite"
            formControlName="ville" [inputFormatter]="formatterVille" [editable]="false"  type="search"
               [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('ville')?.invalid}"
            placeholder="taper ville"
            [resultFormatter]="formatterVille">
            <div *ngIf="clientGroupeForm.get('ville')?.invalid && formSubmited" class="text-danger">
              Ville est obligatoire.
            </div>
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="telephone" class="form-label">Téléphone<span class="text-danger">*</span></label>
            <input id="telephone" formControlName="telephone" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('telephone')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('telephone')?.invalid && formSubmited" class="text-danger">
              Téléphone est obligatoire.
            </div>
          </div>
          <div class="col-md-6 mb-1">
            <label for="email" class="form-label">Email</label>
            <input id="email" formControlName="email" class="form-control"  placeholder="ex: <EMAIL>"
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('email')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('email')?.invalid && formSubmited" class="text-danger">
              Email est non valide.
            </div>
          </div>
          <div class="col-md-6 mb-1">
            <label for="telephone2" class="form-label">Téléphone 2</label>
            <input id="telephone2" formControlName="telephone2" class="form-control"  placeholder="ex: 06 00 00 00 00"
            >
          </div>
          <div class="col-md-6 mb-1">
            <label for="gsm" class="form-label">Gsm</label>
            <input id="gsm" formControlName="gsm" class="form-control" placeholder="ex: 06 00 00 00 00">
          </div>
        </div>
        <!--  -->
        <div class="row">
       
          <div class="col-md-6 mb-1">
            <label for="whatsapp" class="form-label">Whatsapp</label>
            <input id="whatsapp" formControlName="whatsapp" class="form-control"  placeholder="ex: 06 00 00 00 00">
          </div>
          <div class="col-md-6 mb-1">
            <label for="ice" class="form-label">ICE</label>
            <input id="ice" formControlName="ice" class="form-control"  placeholder="ex: 06 00 00 00 00">
          </div>
        </div>
        <!--  -->
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="inpe" class="form-label">inpe</label>
            <input id="inpe" formControlName="inpe" class="form-control"  minlength="9" maxlength="9"
            >
          </div>
          <div class="col-md-6 mb-1">
            <label for="patente" class="form-label">Patente</label>
            <input id="patente" formControlName="patente" class="form-control" 
            >
          </div>
          <div class="col-12">
              <label for="longitude" class="form-label mb-0">Type de Client</label>
              <app-switch [elements]='segmentOptions' name="segment" formControlName="segment" [switchClass]="'info'"></app-switch>
          </div>
        </div>
      </div>
      <div class="col-4">
           <div class="row">
            <div class="col-6">
              <label for="latitude" class="form-label">Latitude</label>
              <input id="latitude" formControlName="latitude" class="form-control">
            </div>
            <div class="col-6">
              <label for="longitude" class="form-label">Longitude</label>
              <input id="longitude" formControlName="longitude" class="form-control">
            </div>
          </div>
         <div class="row mt-2">
          <div class="map-container" *ngIf="isCreateDrawerOpen">
            <app-map (coords)="onMarkerSelected($event)" [selectedLocation]="{
              latitude: clientGroupeForm.get('latitude').value,
              longitude: clientGroupeForm.get('longitude').value
              }"></app-map>
          </div>
        </div>
      </div>
     </div>

      
  </div>
  <div class="modal-footer mt-auto" *ngIf="modalMode != 'VIEW'">
    <button type="button" class="btn btn-secondary"  tabindex="-1" (click)="toggleCreateDrawer(false)" >Annuler</button>
    <button type="submit" class="btn btn-primary" tabindex="-1">
      {{modalMode == "CREATE" ? "Enregistrer" : "Modifier "}}
    </button>
  </div>
  </form>


</div>
  </div>




<div  style="    position: fixed;
top: 0;
right: 0;
width: 100%;
height: 100%;
transition: all .3s;
z-index: 1002;
background: rgba(0, 0, 0, 0.4);"
(click)="toggleAssociationDrawer(false)"
 [style]="isAssocitionDrawerOpen ? 'visibility: visible; pointer-events: auto;opacity: 1 ' : 'visibility: hidden; pointer-events: none;opacity: 0'" >
<div class="bg-white d-flex flex-column" [style]="isAssocitionDrawerOpen ? 'transform: translateX(0)' : 'transform: translateX(100%)'" style="width: 70%;height: 100%; right: 0; position: fixed;transition: all .3s;"  (click)="$event.stopPropagation()"  #drawerContent>
  <div class="modal-header">
    <h5 class="modal-title" style="line-height: 1;">Client Site Associés</h5>
    <button type="button" class="cross-button" data-bs-dismiss="modal" aria-label="Close" (click)="toggleAssociationDrawer(false)">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <div class="modal-body"  style="display: flex;
    flex-direction: column;
    height: calc(100% - 46px); overflow-y: auto; scrollbar-width: thin;" >
          <kendo-grid [kendoGridBinding]="[clickedItem]"
      class=" content-wrap flex-shrink-0 ref-grid" style="height:auto !important;">
        <ng-template kendoGridToolbarTemplate>
          <div style="height: 44px;" class="d-flex justify-content-between align-items-center px-2 client-have-no-association-bg">
            <span class="text-white fs-4 k-font-weight-bold">Client groupe</span>
          </div>
        </ng-template>
        <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="90"></kendo-grid-column>
        <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.raisonSociale"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.nomPharmacien"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
        <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="telephone" title="Téléphone" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>

      <!-- list Client Sites -->
    <kendo-grid [data]="linkedClientSites"  style="height: calc(100vh - 235px);"
      [pageable]="true"
      [pageSize]="linkedClientSitesNavigation.pageSize"
      [skip]="linkedClientSitesNavigation.skip"
      class=" content-wrap flex-shrink-0 client-have-association-grid mt-3"  
      *ngIf="linkedClientSites.data.length > 0 && !isLoadingLinkedClientSites"
      >
        <ng-template kendoGridToolbarTemplate>
          <div   style="height: 44px;" class="d-flex justify-content-between align-items-center px-2 client-have-association-bg">
            <span class="text-white fs-4 k-font-weight-bold">Client Sites Liés</span>
          </div>
        </ng-template>
        <kendo-grid-column field="cliCode" title="Code Client Site" [width]="90"></kendo-grid-column>
        <kendo-grid-column field="cliRaiSoc" title="Raison Sociale" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.cliRaiSoc"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="cliNomPhar" title="Nom Pharmacien" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.cliNomPhar"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="cliVille" title="Ville" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="cliAdresse" title="Adresse" [width]="200"></kendo-grid-column>
        <kendo-grid-column field="cliLocalite" title="Localité" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="telephone" title="Téléphone" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
            <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
        let-total="total">
        <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
          [navigation]="navigation" style="width: 100%;"
          (pageChange)="linkedClientSitesPageChange($event)"></wph-grid-custom-pager>
      </ng-template>
      </kendo-grid>

      <!-- loading state -->
        <div class="card flex-grow-0 card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isLoadingLinkedClientSites">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement des Client Sites Liés...</p>
       </div>
       <!-- no data found -->
      <div class="card card-body flex-grow-0 no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="!isLoadingLinkedClientSites && linkedClientSites.data.length === 0">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Ce client n’a pas des client sites associés.</p>
       </div>
    </div>
    </div>
    </div>













<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Client Groupe</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <button class="btn btn-dark" (click)="toggleFilterDrawer(true)">
          <i class="mdi mdi-filter"></i>
          Filtrer
        </button>
        <button type="button" class="btn btn-primary"
          (click)="toggleCreateDrawer(true)">
         <i class="mdi mdi-plus"></i>
          Nouveau
        </button>
      </div>
    </div>
  </div>
</div>

<kendo-grid [data]="clientsGroupe"  
style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid ref-grid content-wrap"
[pageable]="true"
[pageSize]="30"
[pageSize]="navigation.pageSize" [skip]="navigation.skip"
>
  <kendo-grid-column field="codeClientGroupe" title="Code Groupe" [width]="100"></kendo-grid-column>
  <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200"></kendo-grid-column>
  <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200"></kendo-grid-column>
  <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
  <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
  <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
  <kendo-grid-column field="telephone" title="Telephone" [width]="100"></kendo-grid-column>
  <kendo-grid-column field="patente" title="Patente" [width]="100"></kendo-grid-column>
  <kendo-grid-column field="classification" title="Classification" [width]="100"></kendo-grid-column>
  <kendo-grid-column title="Actions" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="'magnify'" [extendClass]="'circle-lg'" (click)="openAssociationDrawer(dataItem)"></app-action-icon>
      <app-action-icon [icon]="'eye'" [backgroundColor]="'light'" [extendClass]="'circle-lg'" (click)="onView(dataItem)"></app-action-icon>
      <app-action-icon [icon]="'pencil'" [backgroundColor]="'success'" [extendClass]="'circle-lg'" (click)="onEdit(dataItem)"></app-action-icon>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="true"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>