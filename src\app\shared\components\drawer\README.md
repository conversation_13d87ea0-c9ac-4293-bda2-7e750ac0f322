# Accessible Drawer Component

A fully accessible, customizable drawer (slide-in panel) component for Angular applications. This component follows WCAG 2.1 guidelines and implements all the accessibility features from your comprehensive checklist.

## ✅ Accessibility Features Implemented

### Keyboard Accessibility
- ✅ **Escape key closes drawer** - Press ESC to close any open drawer
- ✅ **Focus trap inside drawer** - Tab navigation is contained within the drawer
- ✅ **Focus first interactive element on open** - Automatically focuses the first focusable element
- ✅ **Restore focus to triggering element on close** - Returns focus to the button that opened the drawer

### ARIA Attributes
- ✅ **role="dialog"** - Properly identifies the drawer as a dialog
- ✅ **aria-modal="true"** - Prevents screen readers from accessing background content
- ✅ **aria-label or aria-labelledby** - Provides accessible names for the drawer
- ✅ **aria-hidden="true" on backdrop** - Hides backdrop from screen readers when closed

### Focus Management
- ✅ **Trap tab/shift+tab inside drawer** - Focus cycling within drawer boundaries
- ✅ **Focus lands inside drawer on open** - Immediate focus management
- ✅ **Return focus to last active element on close** - Maintains user context

### Visual Accessibility
- ✅ **High contrast support** - Works with high contrast mode
- ✅ **Clear focus outlines** - Visible focus indicators for all interactive elements
- ✅ **Responsive drawer width** - Adapts to different screen sizes
- ✅ **Animations respect reduced motion** - Honors `prefers-reduced-motion` setting

### Screen Reader Compatibility
- ✅ **Semantic HTML tags** - Uses proper form, label, input elements
- ✅ **Live region announcements** - Announces drawer state changes
- ✅ **Logical tab order** - Natural reading flow for assistive technologies

## Basic Usage

```typescript
import { Component } from '@angular/core';

@Component({
  selector: 'app-example',
  template: `
    <button (click)="drawerOpen = true">Open Drawer</button>
    
    <app-drawer
      [(isOpen)]="drawerOpen"
      title="My Drawer"
      position="right"
      size="medium">
      
      <p>Drawer content goes here</p>
      
      <div slot="footer">
        <button (click)="drawerOpen = false">Close</button>
      </div>
    </app-drawer>
  `
})
export class ExampleComponent {
  drawerOpen = false;
}
```

## Component API

### Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `isOpen` | `boolean` | `false` | Controls drawer visibility |
| `title` | `string` | `''` | Drawer title (used for accessibility) |
| `position` | `'left' \| 'right' \| 'top' \| 'bottom'` | `'right'` | Drawer slide direction |
| `size` | `'small' \| 'medium' \| 'large' \| 'full'` | `'medium'` | Predefined size options |
| `width` | `string` | `''` | Custom width (overrides size for left/right) |
| `height` | `string` | `''` | Custom height (overrides size for top/bottom) |
| `showCloseButton` | `boolean` | `true` | Show/hide close button |
| `closeOnBackdropClick` | `boolean` | `true` | Close when clicking backdrop |
| `closeOnEscape` | `boolean` | `true` | Close when pressing Escape |
| `preventBodyScroll` | `boolean` | `true` | Prevent body scrolling when open |
| `showBackdrop` | `boolean` | `true` | Show/hide backdrop overlay |
| `customClass` | `string` | `''` | Additional CSS classes |
| `ariaLabel` | `string` | `''` | Custom aria-label |
| `ariaLabelledBy` | `string` | `''` | ID of element that labels the drawer |
| `enableAnimations` | `boolean` | `true` | Enable/disable animations |
| `focusFirstElement` | `boolean` | `true` | Auto-focus first element on open |
| `restoreFocus` | `boolean` | `true` | Restore focus on close |

### Outputs

| Event | Type | Description |
|-------|------|-------------|
| `openChange` | `EventEmitter<boolean>` | Emitted when open state changes |
| `opened` | `EventEmitter<void>` | Emitted when drawer finishes opening |
| `closed` | `EventEmitter<void>` | Emitted when drawer finishes closing |
| `backdropClick` | `EventEmitter<void>` | Emitted when backdrop is clicked |

### Methods

| Method | Description |
|--------|-------------|
| `open()` | Programmatically open the drawer |
| `close()` | Programmatically close the drawer |
| `toggle()` | Toggle drawer open/closed state |

## Content Projection

The drawer supports multiple content areas using Angular's content projection:

```html
<app-drawer [(isOpen)]="isOpen" title="My Drawer">
  <!-- Main content (default slot) -->
  <p>This goes in the main body area</p>
  
  <!-- Header content -->
  <div slot="header">
    <button>Custom header button</button>
  </div>
  
  <!-- Footer content -->
  <div slot="footer">
    <button>Cancel</button>
    <button>Save</button>
  </div>
</app-drawer>
```

## Size Options

### Predefined Sizes

- **Small**: 320px (left/right) or 240px (top/bottom)
- **Medium**: 480px (left/right) or 360px (top/bottom)
- **Large**: 640px (left/right) or 480px (top/bottom)
- **Full**: 100% viewport width/height

### Custom Sizes

```html
<app-drawer
  [(isOpen)]="isOpen"
  position="right"
  width="600px">
  <!-- Content -->
</app-drawer>

<app-drawer
  [(isOpen)]="isOpen"
  position="top"
  height="300px">
  <!-- Content -->
</app-drawer>
```

## Drawer Service

For complex applications, use the `DrawerService` to manage multiple drawers:

```typescript
import { DrawerService } from './shared/services/drawer.service';

@Component({...})
export class MyComponent {
  constructor(private drawerService: DrawerService) {
    // Register a drawer
    this.drawerService.register({
      id: 'user-form',
      isOpen: false,
      title: 'User Form',
      position: 'right'
    });
  }

  openUserForm() {
    this.drawerService.open('user-form', { userId: 123 });
  }

  closeAllDrawers() {
    this.drawerService.closeAll();
  }
}
```

## Styling

The component uses CSS custom properties for theming:

```scss
:root {
  --drawer-backdrop-color: rgba(0, 0, 0, 0.5);
  --drawer-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --drawer-border-radius: 8px;
  --drawer-animation-duration: 0.3s;
}
```

### Custom Styling

```scss
.my-custom-drawer {
  .drawer__header {
    background: linear-gradient(45deg, #007bff, #6610f2);
    color: white;
  }
  
  .drawer__body {
    padding: 2rem;
  }
}
```

```html
<app-drawer customClass="my-custom-drawer">
  <!-- Content -->
</app-drawer>
```

## Form Integration

The drawer works seamlessly with Angular forms and includes proper validation display:

```html
<app-drawer [(isOpen)]="formOpen" title="User Form">
  <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
    <div class="mb-3">
      <label for="name" class="form-label">Name *</label>
      <input 
        type="text" 
        class="form-control" 
        id="name" 
        formControlName="name"
        [class.is-invalid]="userForm.get('name')?.invalid && userForm.get('name')?.touched"
        appAutoFocus>
      <div class="invalid-feedback">Name is required</div>
    </div>
  </form>
  
  <div slot="footer">
    <button type="button" (click)="formOpen = false">Cancel</button>
    <button type="submit" [disabled]="userForm.invalid">Save</button>
  </div>
</app-drawer>
```

## Mobile Considerations

- Drawers automatically become full-screen on mobile devices
- Touch gestures are supported for closing (swipe or tap backdrop)
- Responsive padding and font sizes
- Proper viewport handling

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11 (with polyfills)

## Dependencies

- Angular 12+
- Bootstrap 5 (for styling classes)
- MDI Icons (for close button icon)

## Testing

The component includes comprehensive accessibility testing:

```typescript
// Example test
it('should trap focus within drawer', async () => {
  component.isOpen = true;
  fixture.detectChanges();
  
  const focusableElements = fixture.debugElement.queryAll(
    By.css('button, input, select, textarea, [tabindex]:not([tabindex="-1"])')
  );
  
  expect(focusableElements.length).toBeGreaterThan(0);
  // Test tab cycling...
});
```

## Contributing

When contributing to this component, ensure all accessibility features remain intact and test with:

- Screen readers (NVDA, JAWS, VoiceOver)
- Keyboard-only navigation
- High contrast mode
- Reduced motion settings
- Mobile devices
