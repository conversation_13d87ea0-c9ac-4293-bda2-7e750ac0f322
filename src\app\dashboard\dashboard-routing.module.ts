import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from '../core/guards/auth.guard';
import { dashboardComponent } from './dashboard.component';
import { AuthGuardService } from '../shared/services/auth-guard.service';


const routes: Routes = [
  {
    path: '',
    component: dashboardComponent,
    data: { breadcrumb: '' },
    canActivate: [AuthGuardService],

  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class dashboardRoutingModule { }
