import { TypeBla } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeBla.enum';
import { TypeOrigine } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeOrigine.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { DetailDemandeAvoir } from 'src/app/references-app/references-produits/models/achat-avoir/avoir/dmdavoir/detailDemandeAvoir.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Produit } from 'src/app/references-app/references-produits/models/produit/base/produit.model';
import { Taxe } from 'src/app/references-app/references-produits/models/common/taxe.model';


export class DetailOcrBlAchat { 
    localIndex?: number;
    audited?: boolean;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codePrd?: string;
    ctgr?: CategorieProduit;
    dateBla?: any;
    datePeremption?: string;
    depotId?: number;
    detailDemandeAvoir?: DetailDemandeAvoir;
    dsgnPrd?: string;
    etat?: TypeBla;
    frm?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    labo?: Fournisseur;
    mntLigneAchatStd?: number;
    mntLigneBrutHt?: number;
    mntLigneBrutTtc?: number;
    mntLigneNetHt?: number;
    mntLigneNetTtc?: number;

    mntLigneBrutEffectifTtc?: number;       // autocalculé backend
    mntLigneNetEffectifTtc?: number;       // autocalculé backend

    mntLigneRemiseHt?: number;
    mntLigneRemiseTtc?: number;
    mntLigneTva?: number;
    mntLigneVenteStd?: number;
    mntUnitRemiseHt?: number;
    mntUnitRemiseTtc?: number;
    mntUnitTva?: number;
    numBla?: number;
    numLigne?: number;
    numLot?: string;
    origine?: TypeOrigine;
    pbrH?: number;
    pbrP?: number;
    prixAchatStd?: number;
    prixAchatTtc: number
    prixBrutHt?: number;
    prixBrutTtc?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixNetHt?: number;
    prixNetTtc?: number;
    prixVenteStd?: number;
    prixVenteTtc?: number;
    produit?: Produit;
    qtAvoirRefuse?: number;
    qtCmd?: number;
    qtLivre?: number;
    qtUg?: number;
    stockId?: string;
    tauxMarge?: number;
    tauxRemise?: number;
    tauxTva?: number;
    tva?: Taxe;
    userModifiable?: boolean;
    bleTrangere?: BLEtrangere;
    typeBla: TypeBla;         // transient

    // New field with default value
    valider?: boolean = false;
    dsgnPrdDisabled?: boolean = false;
    associate_suggection?: boolean = false;
}

export class BLEtrangere {
    id?: number;
    quantity?: string;
    designation?: string;
    forme_galenique?: string;
    pph?: string;
    ppv?: string;
    total_ttc?: string;
    tva?: string;
    date_per?: string;
    num_lot?: string;
    code_produit?: string;
    gf?: string;
    pu_client?: string;
}

export class IndexedDetailBlAchat extends DetailOcrBlAchat {
    idx: number;
}