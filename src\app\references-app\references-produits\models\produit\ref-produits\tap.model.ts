interface ITAPCriteria {
  ID: string;
  barcode: string;
  barcode_2: string;
  categorie: string;
  designation: string;
  forme_galenique: string;
  prix_vente: string;
}

export class TAPCriteria implements ITAPCriteria {
    ID: string = '';
    barcode: string = '';
    barcode_2: string = '';
    categorie: string = '';
    designation: string = '';
    forme_galenique: string = '';
    prix_vente: string = '';
    
    constructor(data: Partial<ITAPCriteria>) {
        Object.assign(this, data);
        this.ID = this.generateHash(this.designation);
    }

    private generateHash(input: string): string {
    let hash = 0;
  
    for (let i = 0; i < input.length; i++) {
      hash = Math.imul(31, hash) + input.charCodeAt(i);
      hash |= 0; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  }
}


interface IAssociation {
  categorie_base_etrangere: string;
  categorie_base_winplus: string;
  codePrdExtern: string;
  code_barre_win: string;
  code_groupe: string;
  code_prd_winplus: string;
  id_base_etrangere: string;
  nom_base_etrangere: string;
  nom_base_winplus: string;
  prix_vente_base_etrangere: number;
  prix_vente_base_winplus: number;
  type_association: string;
  pph_winplus: number;
  ppv_winplus: number;
}

export type AssociationsResponse = {
  associations: IAssociation[];
}