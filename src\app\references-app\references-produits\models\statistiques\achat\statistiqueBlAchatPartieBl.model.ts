import { TypeBla } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeBla.enum';

// import { Moment } from 'moment';

import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';


export class StatistiqueBlAchatPartieBl {
    dateBl?: any;
    fournisseur?: Fournisseur;
    id?: number;
    numeroBl?: number;
    numeroCmd?: number;
    operateur?: Operateur;
    typeBla?: TypeBla;
    statut?: Statut
}

