<div class="row">

  <div class="col-lg-6 left-homelanding px-0">


    <div class="left-homelandingcont  h-100 d-flex flex-column align-content-between">

      <div class="left-homelandingcontup bg-white position-relative px-3 px-md-4 py-4 p-md-5 p-lg-4  ">
        <div class="blurred-img d-none d-lg-block"></div>
        <h3 class="display-4 position-relative" style="z-index:1; max-width: 520px;">Concrétisez vos plus belles idées,
          sans effort inutile.</h3>


        <div class="left-homelandingcontdown bg-white d-block d-lg-none pt-4  pt-md-5 pt-lg-4">

          <div class="left-homelandingcontdown-body lead mx-auto" style="max-width: 520px;">
            <p class="position-relative ">WinPlus-Ref aide les équipes à organiser leur travail de manière à savoir quoi
              faire, pourquoi et comment.</p>



          </div>

        </div>

      </div>





      <div class="left-homelandingcontdown bg-white d-none d-lg-grid p-md-5 p-lg-4"
        style="min-height: 50%;display: grid; place-items: center;">

        <div class="left-homelandingcontdown-body mx-auto lead pt-" style="max-width: 520px;">
          <p class="mb-4 position-relative ">WinPlus-Ref aide les équipes à organiser leur travail de manière à savoir quoi
            faire, pourquoi et comment.</p>


          <a href="#" class="btn btn-warning text-white py-2 px-3 mb-3">S'inscrire</a>
        </div>

      </div>




    </div>


  </div>





  <div class="col-lg-6 d-flex flex-column justify-content-center" style="background-color: #F3F3F3;">


    <div class="loginform d-block overflow-hidden">

      <div class="formcard position-relative my-4 my-lg-0 mx-2">
        <div class="loginformbg d-none d-md-block"></div>
        <div class="card position-relative my-4 my-lg-0" style="z-index: 1;">

          <!-- Logo -->
          <!-- <div class="card-header py-3 text-center bg-primary">
              <a routerLink="/">
                <span><img src="../../../../../assets/images/logo-dark.svg" alt="" height="28"></span>
              </a>
            </div> -->

          <div class="card-body-cus p-4">

            <h4 class="text-dark-50 text-center mt-0 fw-bold mb-2">Réinitialiser le mot de passe</h4>
            <div class="text-center w-75 m-auto mt-3">
              <p class="text-muted mb-4" *ngIf="successMessage===''">Entrez votre adresse e-mail et nous vous enverrons
                un e-mail avec des instructions pour réinitialiser votre mot de passe.</p>
            </div>

            <ngb-alert type="success" *ngIf="successMessage!==''" [dismissible]="false" class="mt-3">{{
              successMessage }}
            </ngb-alert>

            <form name="reset-password-form" [formGroup]="resetPassswordForm" (ngSubmit)="onSubmit()"
              *ngIf="successMessage===''">

              <div class="mb-3">
                <label for="emailaddress" class="form-label">Adresse e-mail</label>
                <input class="form-control" type="email" id="emailaddress" placeholder="Entrer votre Email"
                  formControlName="email" [ngClass]="{'is-invalid': formSubmitted && formValues.email.invalid}"
                  required>

                <div *ngIf="formSubmitted && formValues.email.invalid" class="invalid-feedback">

                  <div *ngIf="formValues.email.errors?.required">
                    L'e-mail est requis.
                  </div>
                  <div *ngIf="formValues.email.errors?.email">
                    L'entrée doit être une adresse e-mail valide
                  </div>

                </div>

              </div>

              <div class="mb-0 text-center">
                <button class="btn btn-primary" type="submit">Réinitialiser le mot de passe</button>
              </div>
            </form>
          </div> <!-- end card-body-cus-->
        </div>
        <!-- end card -->

        <div class="row mt-3">
          <div class="col-12 text-center position-relative">
            <p class="text-muted"><a [routerLink]="['../login']" class="text-muted ms-1"><b>connexion</b></a></p>
          </div> <!-- end col -->
        </div>





      </div>





    </div>






    <footer class="footer footer-alt">
      2024 © WinPlus-Ref - ref.WinPlus.ma <app-version-badge></app-version-badge>
    </footer>