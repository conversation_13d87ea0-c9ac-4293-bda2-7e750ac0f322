import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  HostListener,
  ElementRef,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
  Renderer2,
  ContentChildren,
  QueryList,
  AfterContentInit,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { Subject } from 'rxjs';

export type DrawerPosition = 'left' | 'right' | 'top' | 'bottom';
export type DrawerSize = 'small' | 'medium' | 'large' | 'full';

@Component({
  selector: 'app-drawer',
  templateUrl: './drawer.component.html',
  styleUrls: ['./drawer.component.scss']
})
export class DrawerComponent implements OnInit, OnDestroy, AfterViewInit, AfterContentInit, OnChanges {
  
  // Configuration inputs
  @Input() isOpen: boolean = false;
  @Input() title: string = '';
  @Input() position: DrawerPosition = 'right';
  @Input() size: DrawerSize = 'medium';
  @Input() width: string = '';
  @Input() height: string = '';
  @Input() showCloseButton: boolean = true;
  @Input() closeOnBackdropClick: boolean = true;
  @Input() closeOnEscape: boolean = true;
  @Input() preventBodyScroll: boolean = true;
  @Input() showBackdrop: boolean = true;
  @Input() customClass: string = '';
  @Input() ariaLabel: string = '';
  @Input() ariaLabelledBy: string = '';
  @Input() enableAnimations: boolean = true;
  @Input() focusFirstElement: boolean = true;
  @Input() restoreFocus: boolean = true;

  // Events
  @Output() openChange = new EventEmitter<boolean>();
  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() backdropClick = new EventEmitter<void>();

  // ViewChild references
  @ViewChild('drawerContainer', { static: false }) drawerContainer!: ElementRef<HTMLElement>;
  @ViewChild('drawerContent', { static: false }) drawerContent!: ElementRef<HTMLElement>;

  // Content projection
  @ContentChildren('[slot=footer]') footerContent!: QueryList<ElementRef>;

  // Internal state
  private destroy$ = new Subject<void>();
  private lastActiveElement: HTMLElement | null = null;
  private focusableElements: HTMLElement[] = [];
  private isAnimating = false;
  
  // Unique ID for accessibility
  public drawerId: string;
  public drawerLabelId: string;

  constructor(
    private elementRef: ElementRef,
    private cdr: ChangeDetectorRef,
    private renderer: Renderer2
  ) {
    const uniqueId = Math.random().toString(36).substring(2, 11);
    this.drawerId = `drawer-${uniqueId}`;
    this.drawerLabelId = `drawer-label-${uniqueId}`;
  }

  ngOnInit(): void {
    // Store the currently focused element when drawer opens
    if (this.isOpen && this.restoreFocus) {
      this.lastActiveElement = document.activeElement as HTMLElement;
    }
  }

  ngAfterViewInit(): void {
    if (this.isOpen) {
      this.handleDrawerOpen();
    }
  }

  ngAfterContentInit(): void {
    // This will be called after content projection is initialized
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && !changes['isOpen'].firstChange) {
      this.onIsOpenChange();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    // Restore body scroll if it was disabled
    if (this.preventBodyScroll) {
      this.toggleBodyScroll(true);
    }
    
    // Restore focus if needed
    if (this.restoreFocus && this.lastActiveElement) {
      this.lastActiveElement.focus();
    }
  }

  // Handle drawer state changes
  onIsOpenChange(): void {
    if (this.isOpen) {
      this.handleDrawerOpen();
    } else {
      this.handleDrawerClose();
    }
  }

  private handleDrawerOpen(): void {
    if (this.isAnimating) return;
    
    this.isAnimating = true;
    
    // Store currently focused element
    if (this.restoreFocus) {
      this.lastActiveElement = document.activeElement as HTMLElement;
    }
    
    // Prevent body scroll
    if (this.preventBodyScroll) {
      this.toggleBodyScroll(false);
    }
    
    // Set up focus management after animation
    setTimeout(() => {
      this.setupFocusManagement();
      this.isAnimating = false;
      this.opened.emit();
    }, this.enableAnimations ? 300 : 0);
  }

  private handleDrawerClose(): void {
    if (this.isAnimating) return;
    
    this.isAnimating = true;
    
    // Restore body scroll
    if (this.preventBodyScroll) {
      this.toggleBodyScroll(true);
    }
    
    // Restore focus after animation
    setTimeout(() => {
      if (this.restoreFocus && this.lastActiveElement) {
        this.lastActiveElement.focus();
      }
      this.isAnimating = false;
      this.closed.emit();
    }, this.enableAnimations ? 300 : 0);
  }

  private setupFocusManagement(): void {
    if (!this.drawerContent) return;
    
    // Find all focusable elements
    this.focusableElements = this.getFocusableElements();
    
    // Focus first element if enabled and available
    if (this.focusFirstElement && this.focusableElements.length > 0) {
      this.focusableElements[0].focus();
    }
  }

  private getFocusableElements(): HTMLElement[] {
    if (!this.drawerContent) return [];
    
    const focusableSelectors = [
      'button:not([disabled]):not([tabindex="-1"])',
      '[href]:not([disabled]):not([tabindex="-1"])',
      'input:not([disabled]):not([readonly]):not([tabindex="-1"])',
      'select:not([disabled]):not([tabindex="-1"])',
      'textarea:not([disabled]):not([readonly]):not([tabindex="-1"])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable]:not([contenteditable="false"])'
    ].join(', ');
    
    return Array.from(this.drawerContent.nativeElement.querySelectorAll(focusableSelectors));
  }

  private toggleBodyScroll(enable: boolean): void {
    if (enable) {
      this.renderer.removeClass(document.body, 'drawer-open');
    } else {
      this.renderer.addClass(document.body, 'drawer-open');
    }
  }

  // Event handlers
  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.isOpen && this.closeOnEscape && !this.isAnimating) {
      event.preventDefault();
      this.close();
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isOpen || this.focusableElements.length === 0) return;
    
    if (event.key === 'Tab') {
      this.handleTabKey(event);
    }
  }

  private handleTabKey(event: KeyboardEvent): void {
    const firstElement = this.focusableElements[0];
    const lastElement = this.focusableElements[this.focusableElements.length - 1];
    const activeElement = document.activeElement as HTMLElement;
    
    if (event.shiftKey) {
      // Shift + Tab
      if (activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab
      if (activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  onBackdropClick(event: MouseEvent): void {
    if (this.closeOnBackdropClick && !this.isAnimating) {
      this.backdropClick.emit();
      this.close();
    }
  }

  onCloseButtonClick(): void {
    this.close();
  }

  // Public methods
  open(): void {
    if (!this.isOpen) {
      this.isOpen = true;
      this.openChange.emit(true);
      this.onIsOpenChange();
    }
  }

  close(): void {
    if (this.isOpen) {
      this.isOpen = false;
      this.openChange.emit(false);
      this.onIsOpenChange();
    }
  }

  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  // Computed properties for styling
  get drawerClasses(): string {
    const classes = [
      'drawer',
      `drawer--${this.position}`,
      `drawer--${this.size}`,
      this.customClass
    ];
    
    if (this.isOpen) {
      classes.push('drawer--open');
    }
    
    if (!this.enableAnimations) {
      classes.push('drawer--no-animation');
    }
    
    return classes.filter(Boolean).join(' ');
  }

  get drawerStyles(): any {
    const styles: any = {};
    
    if (this.width && (this.position === 'left' || this.position === 'right')) {
      styles.width = this.width;
    }
    
    if (this.height && (this.position === 'top' || this.position === 'bottom')) {
      styles.height = this.height;
    }
    
    return styles;
  }

  get ariaLabelValue(): string {
    return this.ariaLabel || this.title || 'Drawer';
  }

  get hasFooterContent(): boolean {
    return this.footerContent && this.footerContent.length > 0;
  }
}
