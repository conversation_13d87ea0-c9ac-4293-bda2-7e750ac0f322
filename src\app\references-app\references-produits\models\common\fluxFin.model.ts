import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';
import { StatutVentilation } from 'src/app/references-app/references-produits/enums/common/StatutVentilation.enum';
import { SousStatutFluxFin } from 'src/app/references-app/references-produits/enums/common/SousStatutFluxFin.enum';
import { SensFlux } from 'src/app/references-app/references-produits/enums/common/SensFlux.enum';
import { ModePaiement } from 'src/app/references-app/references-produits/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { Banque } from './banque.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { Tiers } from 'src/app/references-app/references-produits/models/tiers/tiers.model';


export class FluxFin {
  audited?: boolean;
  banque?: Banque;
  dateEcheance?: any;
  dateFlux?: any;
  designationFlux?: string;
  id?: number;
  mntBrutFlux?: number;
  mntEscompte?: number;
  mntNetFlux?: number;
  mntTaxeParafiscale?: number;
  modeFlux?: ModePaiement;
  nomTiers?: string;
  numFlux?: string;
  numeroDocumentCause?: number;
  operateur?: Operateur;
  resteAVentiler?: number;
  sensFlux?: SensFlux;
  sousStatut?: SousStatutFluxFin;
  statut?: Statut;
  statutVentilation?: StatutVentilation;
  tauxEscompte?: number;
  tiers?: Tiers;
  userModifiable?: boolean;
  tauxRemise?: number

  //
  toSold?: boolean = false


  constructor() {
    this.modeFlux = ModePaiement.ESPECE;
    this.mntBrutFlux = 0;
    this.mntNetFlux = 0;
  }
}

