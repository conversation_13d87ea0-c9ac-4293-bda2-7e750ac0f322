import { Component, OnInit } from '@angular/core';
import { BatchAdmin } from '../../../referential/models/admin/batch-admin.model';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AlertService } from 'src/app/shared/services/alert.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from 'src/app/shared/services/auth.service';
import { BatchesService } from '../../services/batches.service';

@Component({
  selector: 'app-execution-batch',
  templateUrl: './execution-batch.component.html',
  styleUrls: ['./execution-batch.component.scss']
})
export class ExecutionBatchComponent implements OnInit {

  



  closeResult: string = '';
  timelineItems = [
    {
      date: '23-12-2024 18:00',
      description: 'Description of the batch.',
      status: 'En cours',
      statusClass: 'status-en-cours'
    },
    {
      date: '22-12-2024 16:30',
      description: 'Another description.',
      status: 'Succès',
      statusClass: 'status-succes'
    },
    {
      date: '21-12-2024 14:00',
      description: 'Error occurred.',
      status: 'Erreur',
      statusClass: 'status-erreur'
    },
    {
      date: '20-12-2024 12:00',
      description: 'Alert description.',
      status: 'Alerte',
      statusClass: 'status-alerte'
    }
  ];

  hasAdminAbility: boolean = false

  // Sample data for batches
  // List of available batches
  // batches = [
  //   {
  //     title: 'Batch 1',
  //     description: 'This is a description for Batch 1. Add more details as necessary.',
  //     configuration: [
  //       {
  //         type: "text",
  //         name: 'inputTest',
  //         label: 'Input Text',
  //         options: []  // No options needed for text input
  //       },
  //       {
  //         type: "select",
  //         name: 'select_input',
  //         label: 'Select option',
  //         options: [
  //           { label: "Option 1", value: "val1" },
  //           { label: "Option 2", value: "val2" }
  //         ]
  //       }
  //     ]
  //   },
  //   {
  //     title: 'Batch 2',
  //     description: 'Description for Batch 2.',
  //     configuration: [
  //       {
  //         required: false,  // Mark as optional
  //         type: "text",
  //         name: 'inputText',
  //         label: 'Input Text',
  //         options: []  // No options needed for text input
  //       },
  //       {
  //         required: true,  // Mark as required
  //         type: "number",
  //         name: 'inputNumber',
  //         label: 'Input number',
  //         options: []  // No options needed for text input
  //       },

  //       {
  //         required: false,  // Mark as optional
  //         type: "select",
  //         name: 'select_input',
  //         label: 'Select option',
  //         options: [
  //           { label: "Option 1", value: "val1" },
  //           { label: "Option 2", value: "val2" }
  //         ]
  //       },
  //       {
  //         type: "checkbox",
  //         name: 'checkbox_input',
  //         label: 'Accept Terms and Conditions',
  //         options: []  // No options needed for checkbox
  //       },
  //       {
  //         required: true,  // Mark as required
  //         type: "date",
  //         name: 'date_debut',
  //         label: 'Date debut',
  //         options: []  // No options needed for checkbox
  //       }
  //     ]
  //   }
  // ];
  batches: BatchAdmin[] = []
  constructor(private adminBatchSrv: BatchesService, private alertSrv: AlertService,
    private authService: AuthService, private fb: FormBuilder, private modalService: NgbModal) { }

  ngOnInit() {
    this.hasAdminAbility = this.authService.hasAnyAuthority(["ROLE_SUPERADMIN"])
    this.getListBatch()
  }

  getListBatch() {
    this.adminBatchSrv.listAvailableBatches().subscribe(res => {
      console.log("res", res)
      this.batches = res;
      const localDataString = localStorage.getItem("batchesAdmin") ?? null
      if (localDataString) {

        const converted = JSON.parse(localDataString);
        console.log("localDataString", converted)
        this.batches = this.batches.map(batch => {
          const bat = converted.find(con => con.code == batch.code)
          batch["lastExec"] = bat ? bat?.lastExec : null
          batch["statut"] = bat ? bat.statut : null
          return batch;
        })
      }
    })
  }

  selectedBatch: any;
  batchForm: FormGroup;  // Reactive form group
  formData: any = {};

  open(content: any, size = 'lg') {

    this.modalService.open(content, { size: size }).result.then(
      (result) => {
        this.closeResult = `Closed with: ${result}`;
      },
      (reason) => {
        this.closeResult = `Dismissed: ${reason}`;
      }
    );
  }

  // Open the form modal and set selected batch
  openForm(batch: any, content) {
    this.selectedBatch = batch;
    if (batch.configuration.length > 0) {
      this.createForm(batch.configuration);
      this.open(content, "md")
    } else {
      this.execute()
    }
  }

  // Dynamically create a form group based on the batch configuration
  createForm(configuration: any) {
    const group = {};

    configuration.forEach(field => {
      const validators = [];
      if (field.required) {
        validators.push(Validators.required);
      }

      if (field.type === 'checkbox') {
        // Checkbox should have a boolean value
        group[field.name] = [false, validators];
      } else {
        group[field.name] = [null, validators];
      }
    });

    this.batchForm = this.fb.group(group);
  }

  // Submit the form data
  submitForm() {
    if (this.batchForm.valid) {
      console.log("Form submitted with data:", this.batchForm.value);
    } else {
      console.log("Form is invalid");
    }
  }

  execute() {
    console.log("Executing batch...");
    this.selectedBatch.statut = null;
    this.adminBatchSrv.executeBatch({ codeBatch: this.selectedBatch?.code }).subscribe(ress => {

      if (ress == 0) {
        this.alertSrv.success("Le batch est exécuté avec succès", "MODAL")
      } else {
        this.alertSrv.error("Un problème est survenu lors de l'exécution", "MODAL")
      }
      this.selectedBatch.statut = ress == 0 ? true : false;
      const localDataString = localStorage.getItem("batchesAdmin");
      let converted = localDataString ? JSON.parse(localDataString) : [];
      let isBatchFound = false;

      // Update existing batch or flag for new addition
      converted = converted.map(batch => {
        if (batch.code === this.selectedBatch.code) {
          batch.statut = ress === 0;
          isBatchFound = true;
        }
        batch['lastExec'] = new Date();
        this.selectedBatch['lastExec'] = new Date();
        return batch;
      });

      // Add new batch if it wasn't found
      if (!isBatchFound) {
        this.selectedBatch.statut = ress === 0;
        converted.push({ lastExec: new Date(), ...this.selectedBatch });
      }

      // Update local storage
      localStorage.setItem("batchesAdmin", JSON.stringify(converted));


    });
  }


  getObject(dataItem: BatchAdmin): { label: string, value: boolean } {
    return {
      label: dataItem.statut ? 'Exécuté avec succès' : "Problème dans l'exécution",
      value: dataItem.statut
    }
  }
}
