import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';

// import { Moment } from 'moment';

import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { Fournisseur } from '../../../tiers/fournisseur/fournisseur.model';


export class CmdAchatCriteria {
    dateDebut?: any;
    dateFin?: any;
    fournisseurId?: number;
    nonRecusUniquement?: boolean;
    numero?: number;
    operateur?: Operateur;
    statut?: Statut;
    fournisseur?: Fournisseur
}
