/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { ListAnomaliesClientGroupeComponent } from './list-anomalies-client-groupe.component';

describe('ListAnomaliesClientGroupeComponent', () => {
  let component: ListAnomaliesClientGroupeComponent;
  let fixture: ComponentFixture<ListAnomaliesClientGroupeComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ListAnomaliesClientGroupeComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ListAnomaliesClientGroupeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
