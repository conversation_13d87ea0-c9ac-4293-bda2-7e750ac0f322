import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { Page } from '../../referential/models/Page/page.model';
import { WinClientSite } from '../models/sites';
@Injectable({
  providedIn: 'root'
})
export class WinclientSitesService {

  constructor(private http: HttpClient) { }


  getAllSites() {
    return this.http.get<WinClientSite[]>(`${env.winclient_base_url}/api/winclient/sites`);
  }

  getSiteById(id: number) {
    return this.http.get<WinClientSite>(`${env.winclient_base_url}/api/winclient/sites/${id}`);
  }

  createSite(site: Omit<WinClientSite, "id">) {
    return this.http.post<WinClientSite>(`${env.winclient_base_url}/api/winclient/sites`, site);
  }

  
  updateSite(id:number,site: Omit<WinClientSite, "id">) {
    return this.http.put<WinClientSite>(`${env.winclient_base_url}/api/winclient/sites/${id}`, site);
  }


  deleteSite(id: number) {
    return this.http.delete<WinClientSite>(`${env.winclient_base_url}/api/winclient/sites/${id}`);
  }

}