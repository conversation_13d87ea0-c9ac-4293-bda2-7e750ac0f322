<div class="row">

    <div class="col">
        <app-period-setter (formExport)="formChange($event)" [tocompareOption]="true"></app-period-setter>
    </div>
    <div class="col-12" style="margin-top: 5px;">
        <div class="card ">

            <div class="card-body p-0">


                <!-- *ngIf="period?.toCompare"  *ngIf="period?.toCompare"  -->

                <div class="row pt-2 pb-1" *ngIf="!isRecette">
                    <div class="col-sm-6 col-lg-4">
                        <div class="widgetcontent  px-3 d-flex">
                            <div class="float-start widget-icon-svg me-2 me-lg-3">
                                <img src="assets/images/icons/brut.svg" alt="Brut">
                            </div>
                            <div class="widtext ">
                                <h4 class=" mt-0 mb-1">
                                    CA BRUT PPV
                                </h4>
                                <h3 class=" mt-0 mb-0">
                                    {{(dashboardSyntheseVente?.synthesePeriode1.totalMontantVenteBrutTtc ?? 0) | number:
                                    "1.2-2"}}
                                </h3>

                                <div *ngIf="period?.toCompare">
                                    <small class="mb-0 mt-0 text-muted">période comparée :
                                        {{(dashboardSyntheseVente?.synthesePeriode2?.totalMontantVenteBrutTtc ?? 0) |
                                        number:
                                        "1.2-2"}}</small>
                                    <p class="mb-0 mt-0 text-muted" style="padding-top: 2px;">
                                        <span class=" me-1"
                                            [ngClass]=" (dashboardSyntheseVente?. syntheseVariation?.totalMontantVenteBrutTtc ?? 0)>0  ?  'text-success' :   'text-danger'">
                                            <i [ngClass]=" (dashboardSyntheseVente?. syntheseVariation?.totalMontantVenteBrutTtc ?? 0)>0  ?  'text-success mdi mdi-arrow-up-bold' :   'text-danger mdi mdi-arrow-down-bold'"
                                                style="font-size: 16px;"></i>
                                            {{((dashboardSyntheseVente?. syntheseVariation?.totalMontantVenteBrutTtc ??
                                            0) |
                                            number: "1.1-2")+"%"}}</span>
                                        <small>depuis la période comparée</small>
                                    </p>
                                </div>

                            </div>
                        </div>
                    </div>


                    <div class="col-sm-6 col-lg-4">
                        <div class="widgetcontent  px-3 d-flex">
                            <div class="float-start widget-icon-svg me-2 me-lg-3">
                                <img src="assets/images/icons/rem.svg" alt="Brut">
                            </div>
                            <div class="widtext">
                                <h4 class=" mt-0 mb-1">
                                    Remises
                                </h4>
                                <h3 class=" mt-0 mb-0">
                                    {{(dashboardSyntheseVente?.synthesePeriode1.totalMontantRemise ?? 0) | number:
                                    "1.2-2"}}
                                </h3>
                                <div *ngIf="period?.toCompare">
                                    <small class="mb-0 mt-0 text-muted">période comparée
                                        :{{(dashboardSyntheseVente?.synthesePeriode2?.totalMontantRemise ?? 0) | number:
                                        "1.2-2"}} </small>
                                    <p class="mb-0 mt-0 text-muted" style="padding-top: 2px;">
                                        <span class=" me-1"
                                            [ngClass]=" (dashboardSyntheseVente?. syntheseVariation?.totalMontantRemise ?? 0)>0  ?  'text-success' :   'text-danger'">
                                            <i [ngClass]=" (dashboardSyntheseVente?. syntheseVariation?.totalMontantRemise ?? 0)>0  ?  'text-success mdi mdi-arrow-up-bold' :   'text-danger mdi mdi-arrow-down-bold'"
                                                style="font-size: 16px;"></i>
                                            {{((dashboardSyntheseVente?. syntheseVariation?.totalMontantRemise ?? 0) |
                                            number:
                                            "1.1-2")+"%"}}</span>
                                        <small>depuis la périod comparée</small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="col-sm-6 col-lg-4">
                        <div class="widgetcontent  px-3 d-flex">
                            <div class="float-start widget-icon-svg me-2 me-lg-3">
                                <img src="assets/images/icons/net.svg" alt="Brut">
                            </div>
                            <div class="widtext">
                                <h4 class="mt-0 mb-1">
                                    CA NET PPV
                                </h4>
                                <h3 class=" mt-0 mb-0">
                                    {{(dashboardSyntheseVente?.synthesePeriode1.totalMontantVenteNetTtc ?? 0) | number:
                                    "1.2-2"}}
                                </h3>
                                <div *ngIf="period?.toCompare">
                                    <small class="mb-0 mt-0 text-muted">période comparée :
                                        {{(dashboardSyntheseVente?.synthesePeriode2?.totalMontantVenteNetTtc ??
                                        0) | number: "1.2-2"}}</small>
                                    <p class="mb-0 mt-0 text-muted" style="padding-top: 2px;">
                                        <span class="me-1"
                                            [ngClass]=" (dashboardSyntheseVente?. syntheseVariation?.totalMontantVenteNetTtc ?? 0)>0  ?  'text-success' :   'text-danger'">


                                            <i [ngClass]=" (dashboardSyntheseVente?. syntheseVariation?.totalMontantVenteNetTtc ?? 0)>0  ?  'mdi mdi-arrow-up-bold' :   'mdi mdi-arrow-down-bold'"
                                                style="font-size: 16px;"></i>
                                            {{(( dashboardSyntheseVente?. syntheseVariation?.totalMontantVenteNetTtc
                                            ?? 0) |
                                            number:
                                            "1.2-2")+"%"}}
                                        </span>
                                        <small>depuis la périod comparée</small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
                <!-- <hr class="ligne border-bottom-1 mx-3" *ngIf="period?.toCompare || !isRecette" /> -->
                <div class="row">
                    <div class="col-lg-3 ">
                        <div class="mb-2"> <app-switch [elements]='switchElements' [(ngModel)]="isRecette" name="type"
                                [disabled]="false" [switchClass]="'info'" (ngModelChange)="typeDataChange($event)"
                                [ngModelOptions]="{standalone: true}"></app-switch></div>
                        <!-- <div class="widgetstats h-100 d-flex flex-column justify-content-between p-3">
                 


                        </div> -->

                    </div>

                    <!-- <div class="col-lg-9 mt-2">
                        <div class="pe-3">
                            <apx-chart class="apex-charts " [series]="graphObject1.graph.series"
                                [chart]="graphObject1.graph.chart" [xaxis]="graphObject1.graph.xaxis"
                                [tooltip]="graphObject1.graph.tooltip" [stroke]="graphObject1.graph.stroke"
                                [plotOptions]="graphObject1.graph.plotOptions" [fill]="graphObject1.graph.fillData"
                                [autoUpdateSeries]="false" [title]="graphObject1.graph.title"
                                [grid]="graphObject1.graph.grid" [colors]="graphObject1.graph.colors"
                                [responsive]="graphObject1.graph.responsive" [yaxis]="graphObject1.graph.yaxis"
                                [subtitle]="graphObject1.graph.subtitle" [legend]="graphObject1.graph.legend"
                                [labels]="graphObject1.graph.labels" *ngIf="graphObject1?.graph">
                            </apx-chart>




                        </div>

                    </div> -->
                    <!-- test -->
                    <div class="col-lg-9">
                        <div class="pe-3">
                            <apx-chart class="apex-charts " [series]="graphObject.graph.series"
                                [chart]="graphObject.graph.chart" [xaxis]="graphObject.graph.xaxis"
                                [tooltip]="graphObject.graph.tooltip" [stroke]="graphObject.graph.stroke"
                                [plotOptions]="graphObject.graph.plotOptions" [fill]="graphObject.graph.fillData"
                                [autoUpdateSeries]="false" [title]="graphObject.graph.title"
                                [grid]="graphObject.graph.grid" [colors]="graphObject.graph.colors"
                                [responsive]="graphObject.graph.responsive" [yaxis]="graphObject.graph.yaxis"
                                [subtitle]="graphObject.graph.subtitle" [legend]="graphObject.graph.legend"
                                [labels]="graphObject.graph.labels" *ngIf="graphObject?.graph">
                            </apx-chart>




                        </div>

                    </div>
                </div>


            </div>
        </div>

    </div>

</div>