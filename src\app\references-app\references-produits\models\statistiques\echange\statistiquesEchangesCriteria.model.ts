// import { Moment } from 'moment';

import { Statut } from "src/app/references-app/references-produits/enums/common/Statut.enum";
import { Produit } from "../../produit/base/produit.model";
import { Confrere } from "../../tiers/confrere/confrere.model";



export class StatistiquesEchangesCriteria {
    confrereId?: number;
    dateDebut?: any;
    dateFin?: any;
    produitId?: number;
    confrere?: Confrere
    ///
    produit?: Produit

    statut?: Statut
}
