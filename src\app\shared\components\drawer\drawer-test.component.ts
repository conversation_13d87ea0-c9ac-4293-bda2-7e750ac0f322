import { Component } from '@angular/core';

@Component({
  selector: 'app-drawer-test',
  template: `
    <div class="container p-4">
      <h2>Quick Drawer Test</h2>
      <p>Click the button below to test the drawer component:</p>
      
      <button type="button" class="btn btn-primary" (click)="testDrawerOpen = true">
        Open Test Drawer
      </button>
      
      <app-drawer
        [isOpen]="testDrawerOpen"
        title="Test Drawer"
        position="right"
        size="medium"
        [closeOnBackdropClick]="true"
        [closeOnEscape]="true">
        
        <div class="p-3">
          <h4>Hello World!</h4>
          <p>This is a test drawer with all accessibility features enabled.</p>
          
          <div class="alert alert-success">
            <strong>✅ Accessibility Features Active:</strong>
            <ul class="mb-0 mt-2">
              <li>Focus is trapped within this drawer</li>
              <li>Press ESC to close</li>
              <li>Click backdrop to close</li>
              <li>Focus will return to the trigger button</li>
              <li>Screen reader announcements work</li>
            </ul>
          </div>
          
          <div class="mt-3">
            <input type="text" class="form-control mb-2" placeholder="Test input field" appAutoFocus>
            <button type="button" class="btn btn-primary me-2">Test Button 1</button>
            <button type="button" class="btn btn-secondary me-2">Test Button 2</button>
            <button type="button" class="btn btn-success" (click)="testDrawerOpen = false">
              Close Drawer
            </button>
          </div>
        </div>
      </app-drawer>
    </div>
  `
})
export class DrawerTestComponent {
  testDrawerOpen = false;
}
