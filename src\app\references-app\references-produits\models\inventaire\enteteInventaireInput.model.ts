import { MethodeInventaire } from 'src/app/references-app/references-produits/enums/inventaire/MethodeInventaire.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { Depot } from 'src/app/references-app/references-produits/models/produit/stock/depot.model';
import { DetailInventaireListe } from './detailInventaireListe.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Rayon } from 'src/app/references-app/references-produits/models/produit/base/rayon.model';


export class EnteteInventaireInput {
    audited?: boolean;
    categories?: CategorieProduit[];
    codeabarre?: string;
    dateInventaire?: any;
    depots?: Depot[];
    detailInvList?: DetailInventaireListe[];
    formes?: FormeProduit[];
    fts?: FamilleTarifaire[];
    laboratoires?: Fournisseur[];
    methode?: MethodeInventaire;
    rayons?: Rayon[];
    userModifiable?: boolean;

    initQteFinal?: boolean

    onlyProduitsMouvementes?: boolean;

    signStock?: string
}

