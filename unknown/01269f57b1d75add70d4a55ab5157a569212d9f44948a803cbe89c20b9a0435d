

<div class="row mt-2">
    <!-- First Grid: Produits Winplus -->
    <div class="col-12" >
        <div class="page-title-box mb-1 row">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h4 class="page-title ps-2">Liste produits Groupe</h4>
                <div class="d-flex flex-wrap justify-content-end me-2">
                    <app-filtering-button (filtersChange)="handleFiltersChange($event)" class="me-2" *ngIf="filtersRef"
                        [reference]="filtersRef">
                    </app-filtering-button>
                <button class="btn btn-primary" [disabled]="selectedItems.length === 0"  (click)="sendSelected()">Envoyer</button>

                </div>
            </div>
        </div>
        <kendo-grid
            class="border-grey me-2  ref-grid"
            style="height: 400px; cursor: pointer;"
            [data]="gridData"
            [pageable]="true"
            [pageSize]="navigation.pageSize"
            [skip]="navigation.skip"
        >
        <kendo-grid-column [width]="20" class="text-center">
                <ng-template kendoGridHeaderTemplate>
                    <input class="checkbox-grid" type="checkbox" (change)="selectAll($event)" />
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                    <input class="checkbox-grid" type="checkbox" [checked]="isSelected(dataItem)" (change)="toggleSelection(dataItem)" />
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="codeGroupe" [width]="50" title="Code Groupe" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span style="color: #E09F3E">{{ dataItem.codeGroupe ? dataItem.codeGroupe : "" }}</span>
                </ng-template>
            </kendo-grid-column>
            <!-- <kendo-grid-column field="codeWinplus" [width]="50" title="Code Winplus" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span style="color: #E09F3E">{{ dataItem.codeWinplus ? dataItem.codeWinplus : "" }}</span>
                </ng-template>
            </kendo-grid-column> -->
            <kendo-grid-column field="designation" [width]="200" title="Désignation" class="text-start">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.designation}}
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="pfht" [width]="50" title="PFHT" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.pfht | number: "1.2-2": "Fr-fr"}}
                </ng-template>
            </kendo-grid-column>
            
            <kendo-grid-column field="prixAchatStd" [width]="50" title="PPH" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="prixVenteStd" [width]="50" title="PPV" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="laboratoireLabel" [width]="50" title="Laboratoire" class="text-center"></kendo-grid-column>
            <kendo-grid-column field="dateCreation" [width]="50" title="Date Création" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.dateCreation | date: "dd/MM/yyyy"}}
                </ng-template>
            </kendo-grid-column>
            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
            <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
                <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="false"
                    [navigation]="navigation" style="width: 100%;" (pageChange)="pageChange($event)"></wph-grid-custom-pager>
            </ng-template>
        </kendo-grid>
    </div>

    <!-- Second Grid: Produits à envoyer -->
    <div class="col-12">
        <div class="page-title-box me-2 mb-1 row">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h4 class="page-title ps-2">Produits à envoyer</h4>
                <div class="d-flex flex-wrap justify-content-end">
                </div>
            </div>
        </div>
        <ng-container #outlet [ngTemplateOutlet]="sendSelectedGrid"></ng-container>

    </div>
</div>
<ng-template #sendSelectedGrid>
    <kendo-grid     
    #secondGrid
        class="border-grey me-2  ref-grid"
        style="height: 300px; cursor: pointer;"
        [data]="selectedGridData"   [trackBy]="trackByCodeWinplus"
    >
        <kendo-grid-column [width]="20" class="text-center">
            <ng-template kendoGridHeaderTemplate>
                <input class="checkbox-grid" type="checkbox" (change)="selectAllSelected($event)" />
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
                <input class="checkbox-grid" type="checkbox" 
                       [checked]="isSelectedInSecondGrid( dataItem)" 
                       (change)="toggleSelectionInSecondGrid($event, dataItem)" />
              </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeGroupe" [width]="50" title="Code Groupe" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                <span style="color: #E09F3E">{{ dataItem.codeGroupe ? dataItem.codeGroupe : "" }}</span>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="designation" [width]="200" title="Désignation" class="text-start">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.designation}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="pfht" [width]="50" title="PFHT" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.pfht | number: "1.2-2": "Fr-fr"}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" [width]="50" title="PPH" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" [width]="50" title="PPV" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="laboratoireLabel" [width]="50" title="Laboratoire" class="text-center"></kendo-grid-column>
        <kendo-grid-column field="dateCreation" [width]="50" title="Date Création" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.dateCreation | date: "dd/MM/yyyy"}}
            </ng-template>
        </kendo-grid-column>
       
        <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun produit sélectionné.</span>
        </ng-template>
    </kendo-grid>
</ng-template>

<ng-template #configureEnvoiModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title">Configurez l'envoi</h5>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
    </div>
    <div class="modal-body">
      <form>
        <div class="row align-items-center mb-3">
          <label class="form-label">Type d'envoi</label>
          <app-switch
            [formControl]="envoiTypeControl"
            switchClass="primary"
            [elements]="[
              {label: 'Ajout', value: true}, 
              {label: 'Modification', value:false }
            ]"
            (change)="updateAction()"
          >
          </app-switch>
        </div>
        <div class="mb-3">
          <label class="form-label me-2">Sites</label>
          <!-- Add the Select All button here -->
          <div class="d-flex k-gap-1 align-items-center">
              <button
                type="button"
                class="btn btn-sm btn-outline-primary mb-2"
                (click)="selectAllSites()"
              >
                {{showDeselectAllButton ? 'Désélectionner' : 'Sélectionner'}} tous les sites
              </button>
              <button
                type="button"
                *ngIf="showClearButton"
                class="btn btn-sm btn-outline-primary mb-2"
                (click)="deselectAllSites()"
              >
                Vider
              </button>
          </div>
          <div *ngIf="availableSites.length > 0; else noSitesAvailable">
            <select2
              [data]="siteOptions"
              [value]="selectedSites"
              (update)="toggleSiteSelectionFromDropdown($event)"
              [multiple]="true"
              [hideSelectedItems]="true"
              class="form-control bg-white p-0 border-0"
              placeholder="Rechercher et sélectionner des sites"
            >
            </select2>
          </div>
          <ng-template #noSitesAvailable>
            <div class="text-muted">Aucun site disponible.</div>
          </ng-template>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Annuler</button>
      <button
        type="button"
        class="btn btn-primary"
        (click)="confirmEnvoi(modal)"
        [disabled]="!selectedAction || selectedSites.length === 0"
      >
        Envoyer
      </button>
    </div>
  </ng-template>