  import { Component, OnInit } from '@angular/core';
import { GridDataResult } from '@progress/kendo-angular-grid';
  import { Filter, Filters, FiltersModalBetaService } from 'src/app/shared/filters/filters-modal-service/filters-modal-beta.service';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
  import { AlertService } from 'src/app/shared/services/alert.service';
import { PageChangeEvent } from '@progress/kendo-angular-pager';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { WinClientVille } from '../../../models/ville';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { ClientGroupe, ClientGroupeCriteria, ClientGroupeSegment } from '../../../models/clientGroupe';
import { ClientGroupeService } from '../../../Services/winclient.clientGroupe.service';
import { WinclientVilleService } from '../../../Services/winclient.ville.service';
import { WinClientSite } from '../../../models/sites';
import { WinclientSitesService } from '../../../Services/winclient.sites.service';
import { ClientSite, ClientSiteFilter } from '../../../models/clientSite';
import { WinclientLocaliteService } from '../../../Services/winclient.localite.service';
import { WinClientLocalite } from '../../../models/localite';
import { Observable, of, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, switchMap, skip } from 'rxjs/operators';
import { ClientSiteService } from '../../../Services/winclient.clientSite.service';
import { ActivatedRoute } from '@angular/router';

type Option = {
  label: string;
  value: ClientGroupeSegment | null;
}
@Component({
  selector: 'app-list-client-groupe',
  templateUrl: './list-client-groupe.component.html',
  styleUrls: ['./list-client-groupe.component.scss']
})
export class ListClientGroupeComponent implements OnInit {

    villes: WinClientVille[];
    localities : WinClientLocalite[];
    clientGroupeForm: FormGroup;
    clientGroupeFilterForm : FormGroup;
    formSubmited: boolean = false;
    modalMode : 'CREATE' | 'EDIT' | 'VIEW' = 'CREATE';
    navigation : Pagination = {pageSize :21,skip:0}
    linkedClientSitesNavigation: Pagination = {pageSize :21,skip:0}
    clientsGroupe :GridDataResult = {data:[] as ClientGroupe[],total:0};
    linkedClientSites: GridDataResult = {data:[] as ClientSite[],total:0};
    clientGroupeFilter: Partial<ClientGroupeCriteria>;
    clickedItem: ClientGroupe;
    modalRef : NgbModalRef;
    isFilterDrawerOpen: boolean = false;
    isCreateDrawerOpen : boolean = false;
    isAssocitionDrawerOpen : boolean = false;
    isLoadingLinkedClientSites: boolean = false;
    codeGroupeQueryParam: string;
 

// Array of options for Segment with French labels
    segmentOptions: Option[] = [
      { label: "Pharmacie", value: ClientGroupeSegment.PHARMACIE },
      { label: "Parapharmacie", value: ClientGroupeSegment.PARAPHARMACIE },
      { label: "Centre Beauté", value: ClientGroupeSegment.CENTRE_BEAUTE }
    ];

    segmentFilterOptions : Option[] = [
      { label: "Tous", value: null },
      ...this.segmentOptions
    ];
    
    constructor(
      private clientGroupeService: ClientGroupeService,
      private modalService: NgbModal,
      private fb: FormBuilder,
      private villeService : WinclientVilleService,
      private alertService : AlertService,
      private userInputService:UserInputService,
      private locationService: WinclientLocaliteService,
      private clientSiteService : ClientSiteService,
      private activeRoute: ActivatedRoute,
    ) { 
      this.clientGroupeFilter = new ClientGroupeCriteria({
        page:0,
        size:this.navigation.pageSize,
      });
    }
  


  ngOnInit() {
    this.initFiltersForm();
    this.listenToQueryParam();
    this.initClientGroupeForm();
    !this.codeGroupeQueryParam && this.getListClientGroupe(0);
    this.getVilles();
    this.getLocatities();
  }


  initFiltersForm(){
    this.clientGroupeFilterForm = this.fb.group({
      codeClientGroupe: [null],
      tag: [null],
      nomPharmacien: [null],
      raisonSociale: [null],
      adress: [null],
      ville: [null],
      telephone: [null],
      // telephone2: [null],
      // gsm : [null],
      // whatsapp : [null],
      classification: [null],
      segment: [null],
    });
  }

  listenToQueryParam(){
    this.activeRoute.queryParams.subscribe(params => {
      const codeGroupe = params['codeGroupe'];
      if(codeGroupe){
        this.codeGroupeQueryParam = codeGroupe;
        this.clientGroupeFilterForm.get('codeClientGroupe').patchValue(codeGroupe);
        this.onFilterSubmit();
      }
    });
  }

  initClientGroupeForm(){
    this.clientGroupeForm = this.fb.group({
      id: [null],
      codeClientGroupe: [null],
      raisonSociale: ['', Validators.required],
      nomDuPharmacien: ['', Validators.required],
      adresse: ['', Validators.required],
      adresseComplement: [''],
      localite: [''],
      ville: [null, Validators.required],
      telephone: ['', [Validators.required]],
      telephone2: [null],
      gsm : [null],
      whatsapp : [null],
      ice: [null],
      email: [null, [Validators.email]],
      patente: [''],
      inpe: [null],
      longitude: [null],
      latitude: [null],
      segment: [this.segmentOptions[0].value],
    });
  }


  searchVilleTypeahead: OperatorFunction<string, readonly WinClientVille[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      filter((term) => term.length >= 2),
      switchMap((term) => {
       const targetVilles =  this.villes.filter((v) => v.libelle.toLowerCase().indexOf(term.toLowerCase()) > -1);
        return of(targetVilles);
      })
    )

  searchLocaliteTypeahead: OperatorFunction<string, readonly WinClientLocalite[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      filter((term) => term.length >= 2),
      switchMap((term) => {
       const targetLocalite =  this.localities.filter((v) => v.libLocalite.toLowerCase().indexOf(term.toLowerCase()) > -1);
        return of(targetLocalite);
      })
    )

  formatterLocalite = (localite: WinClientLocalite) =>  localite.libLocalite;
  formatterVille = (ville: WinClientVille) =>  ville.libelle;


  onMarkerSelected(event:number[]){
    this.clientGroupeForm.patchValue({
      longitude: event[0],
      latitude: event[1]
    })
  }

  OpenNewClientGroupeModal(modal: any) {
    this.formSubmited = false;
    this.modalMode = "CREATE";
    this.clientGroupeForm.reset();
    this.modalRef = this.modalService.open(modal, { size: 'xl', scrollable: true});
  }

  phoneValidator(control) {
    const phoneNumber = control.value;
    if (phoneNumber && !/^\d{10}$/.test(phoneNumber)) { 
      return { invalidPhone: true };
    }
    return null;
  }



  onEdit(clientGroupe:ClientGroupe){
    this.formSubmited = false;
    this.modalMode = "EDIT";
    this.patchFormValues(clientGroupe);
    this.toggleCreateDrawer(true);
  }
  onView(clientGroupe:ClientGroupe){
    this.formSubmited = false;
    this.modalMode = "VIEW";
    this.patchFormValues(clientGroupe);
    this.clientGroupeForm.disable();
    this.toggleCreateDrawer(true);
  }

  openAssociationDrawer(clientGroupe: ClientGroupe) {
    this.isAssocitionDrawerOpen = true;
    this.clickedItem = clientGroupe;
    this.getAllLinkedClientSites(clientGroupe);
  }


  getAllLinkedClientSites(clientGroupe: ClientGroupe) {
    const clientSiteFilter =  new ClientSiteFilter({
      codeGroupe: clientGroupe.codeClientGroupe,
      page : Math.floor(this.linkedClientSitesNavigation.skip / this.linkedClientSitesNavigation.pageSize),
      size: this.linkedClientSitesNavigation.pageSize,
    })
    this.isLoadingLinkedClientSites = true;
    this.clientSiteService.searchClientSite(clientSiteFilter).subscribe({
      next: (res) => {
        console.log("Sites associés:", res);
        this.linkedClientSites.data = res.content;
        this.linkedClientSites.total = res.totalElements;
      },
      error: (err) => {
        this.alertService.error("Erreur lors de la récupération des sites associés");
      },
      complete: () => {
        this.isLoadingLinkedClientSites = false;
      }
    });
  }

  linkedClientSitesPageChange(skip: number) {
    this.linkedClientSitesNavigation.skip = skip;
    this.getAllLinkedClientSites(this.clickedItem);
  }


  getVilles(){
    this.villeService.getAllVilles().subscribe(res => {
      this.villes = res.content;

      const transformedValues = res.content.map(ville => {
        return { label: ville.libelle, value: ville.libelle }
      });

    });
  }
  getLocatities(){
    this.locationService.getAllLocalites().subscribe(res => {
      this.localities = res;
    });
  }

  
  getListClientGroupe(page=0){
    this.navigation.skip = page * this.navigation.pageSize;
    this.clientGroupeFilter.page = page;
    this.clientGroupeFilter.size = this.navigation.pageSize;
    this.clientGroupeService.searchClientGroupe(this.clientGroupeFilter).subscribe(res => {
      this.clientsGroupe.data = res.content;
      this.clientsGroupe.total = res.totalElements;
      this.isFilterDrawerOpen = false;
    });
  
  }

  cleanFilterQueryParams(filters:  Partial<ClientGroupeCriteria>) {
    // remove undifined values and empty strings
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined || filters[key] === '' || filters[key] === null || (typeof filters[key] === 'string' && filters[key].trim() === '')) {
        delete filters[key];
      }
    });
    return filters;
  }

  onFilterSubmit(){
    const criteria = new ClientGroupeCriteria({
      nomPharmacien:this.clientGroupeFilterForm.get('nomPharmacien').value,
      raisonSociale:this.clientGroupeFilterForm.get('raisonSociale').value,
      size:this.navigation.pageSize,
      tag:this.clientGroupeFilterForm.get('tag').value,
      adress:this.clientGroupeFilterForm.get('adress').value,
      villeLibelle:this.clientGroupeFilterForm.get('ville').value?.libelle,
      codeClientGroupe:this.clientGroupeFilterForm.get('codeClientGroupe').value,
      telephone:this.clientGroupeFilterForm.get('telephone').value,
      // telephone2:this.clientGroupeFilterForm.get('telephone2').value,
      // gsm:this.clientGroupeFilterForm.get('gsm').value,
      // whatsapp:this.clientGroupeFilterForm.get('whatsapp').value,
      classification:this.clientGroupeFilterForm.get('classification').value,
      segment:this.clientGroupeFilterForm.get('segment').value,
    });
    const CleanedCriteria = this.cleanFilterQueryParams(criteria);
    this.clientGroupeFilter = CleanedCriteria;
    this.getListClientGroupe();
  }

  resetFilters(){
    this.clientGroupeFilterForm.reset();
    this.clientGroupeFilter = new ClientGroupeCriteria({
      page:0,
      size:this.navigation.pageSize,
    });
    this.getListClientGroupe();
  }

          

  private patchFormValues(clientGroupe: ClientGroupe) {
    const localite = this.localities.find(l => l.libLocalite === clientGroupe.localite);
    this.clientGroupeForm.patchValue({
      id: clientGroupe.id,
      raisonSociale: clientGroupe.raisonSociale,
      nomDuPharmacien: clientGroupe.nomPharmacien,
      adresse: clientGroupe.adresse1,
      adresseComplement: clientGroupe.adresse2,
      localite,
      ville: clientGroupe.ville,
      telephone: clientGroupe.telephone,
      patente: clientGroupe.patente,
      inpe: clientGroupe.inpe,
      longitude: clientGroupe.longitude,
      latitude: clientGroupe.latitude,
      gsm: clientGroupe.gsm,
      telephone2: clientGroupe.telephone2,
      whatsapp: clientGroupe.whatsapp,
      email: clientGroupe.email,
      ice: clientGroupe.ice,
      segment:clientGroupe.segment,
    });
  }

  onDelete(clientGroupe:ClientGroupe){
    this.userInputService.confirm("Confirmer la suppression du groupe ?","Voulez-vous vraiment supprimer ce groupe ?","Supprimer","Annuler").then(result => {
      if(result){
        this.deleteClientGroupe(clientGroupe.id);
      }
    }).catch(() => { });
  }

  toggleFilterDrawer(forcedValue: boolean=null) {
    this.isFilterDrawerOpen = forcedValue ?? !this.isFilterDrawerOpen;

    if(this.isFilterDrawerOpen){
      setTimeout(() => {
        (document.querySelector('#codeGroupe') as HTMLInputElement)?.focus();
      }, 200);
  }
  }

  toggleCreateDrawer(forcedValue: boolean=null) {
    this.isCreateDrawerOpen = forcedValue ?? !this.isCreateDrawerOpen;

    if(!this.isCreateDrawerOpen) {
      this.clientGroupeForm.reset();
      this.formSubmited = false;
    }   
    
    
    if(this.isCreateDrawerOpen){
      this.modalMode == "CREATE" && this.clientGroupeForm.enable();
      this.modalMode == "EDIT" && this.clientGroupeForm.enable();
      setTimeout(() => {
        (document.querySelector('.raison-social-create') as HTMLInputElement)?.focus();
      }, 200);
  } 
  }

  toggleAssociationDrawer(forcedValue: boolean=null) {
    this.isAssocitionDrawerOpen = forcedValue ?? !this.isAssocitionDrawerOpen;
  }


  onSubmit(){ 
    this.formSubmited = true
    if(!this.clientGroupeForm.valid){
      this.alertService.error("Formulaire non valide");
      return;
    }
    
    const clientGroupe = this.formToClientGroupe();

    const {id, ...clientGroupePayload} = clientGroupe;

    if(this.modalMode == "EDIT"){
      this.updateClientGroupe(id,clientGroupePayload);
    }else{
      this.createClientGroupe(clientGroupePayload);
    }
  }

  formToClientGroupe(){
    const clientGroupe = new ClientGroupe({
      raisonSociale: this.clientGroupeForm.value.raisonSociale,
      nomPharmacien: this.clientGroupeForm.value.nomDuPharmacien,
      adresse1: this.clientGroupeForm.value.adresse,
      adresse2: this.clientGroupeForm.value.adresseComplement,
      localite: this.clientGroupeForm.value.localite?.libLocalite,
      ville: this.clientGroupeForm.value.ville,
      telephone: this.clientGroupeForm.value.telephone,
      gsm: this.clientGroupeForm.value.gsm,
      telephone2: this.clientGroupeForm.value.telephone2,
      whatsapp: this.clientGroupeForm.value.whatsapp,
      email: this.clientGroupeForm.value.email,
      ice: this.clientGroupeForm.value.ice,
      patente: this.clientGroupeForm.value.patente,
      inpe: this.clientGroupeForm.value.inpe,
      longitude: this.clientGroupeForm.value.longitude,
      latitude: this.clientGroupeForm.value.latitude,
      segment: this.clientGroupeForm.value.segment,
    });
    this.modalMode === "EDIT" && (clientGroupe.id = this.clientGroupeForm.value.id);
    return clientGroupe;
  }


  compareVille(ville1:WinClientVille , ville2:WinClientVille){
    return ville1 && ville2 && ville1.id === ville2.id;
  }
  compareLocalite(localite1:string , localite2:string){
    return localite1 && localite2 && localite1 === localite2;
  }
  

  pageChange(skip: number){
    this.navigation.skip =skip;
    const page = Math.ceil(skip / this.navigation.pageSize);
    this.getListClientGroupe(page);
  }


  private createClientGroupe(clientGroupe:Omit<ClientGroupe, "id">){
    this.clientGroupeService.createClientGroupe(clientGroupe).subscribe(res => {
      this.toggleCreateDrawer(false);
      this.alertService.success(`Le Client Groupe ${clientGroupe.raisonSociale} (${clientGroupe.nomPharmacien}) a été créé avec succès`);
      this.clientGroupeForm.reset();
      this.getListClientGroupe();
    });
  }

  private updateClientGroupe(id:number,clientGroupe:Omit<ClientGroupe, "id">){
    this.clientGroupeService.updateClientGroupe(id,clientGroupe).subscribe(res => {
      this.getListClientGroupe();
      this.toggleCreateDrawer(false);
      this.clientGroupeForm.reset();
      this.alertService.success(`Le Client Groupe ${clientGroupe.raisonSociale} (${clientGroupe.nomPharmacien}) a été créé avec succès`);
    });
  }

  private deleteClientGroupe(id:number){
    this.clientGroupeService.deleteClientGroupe(id).subscribe(res => {
      this.alertService.success("Le groupe a été supprimé avec succès");
      this.getListClientGroupe();
    });
  }
 

}
