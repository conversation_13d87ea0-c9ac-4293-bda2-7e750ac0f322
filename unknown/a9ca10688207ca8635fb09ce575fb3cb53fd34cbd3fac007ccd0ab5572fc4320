import { ListClientGroupeComponent } from './pages/client-groupe/list-client-groupe/list-client-groupe.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReferencesClientsRoutingModule } from './referencesClients-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbAccordionModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { TypeFournisseurPipe } from 'src/app/references-app/referential/pipes/type-fournisseur.pipe';
import { AuthService } from 'src/app/shared/services/auth.service';
import { SharedModule } from 'src/app/shared/shared.module';
import { GridModule } from '@progress/kendo-angular-grid';
import { ListVillesComponent } from './pages/ville/list-villes/list-villes.component';
import { ListSitesComponent } from './pages/site/list-sites/list-sites.component';
import { ListRegionComponent } from './pages/region/list-region/list-region.component';
import { ListClientSitesComponent } from './pages/client-site/list-client-sites/list-client-sites.component';
import { WinclientAcceuilComponent } from './pages/acceuil/winclient-acceuil.component';
import { ProvinceComponent } from './pages/province/province.component';
import { LocaliteComponent } from './pages/localite/localite.component';
import { ListAnomaliesClientGroupeComponent } from './pages/anomalies/list-anomalies-client-groupe/list-anomalies-client-groupe.component';

@NgModule({
  declarations: [
    // FournisseurTypeaheadComponent,
    // ProduitSearchSidebarComponent,
    // TypeFournisseurPipe
    ListClientGroupeComponent,
    ListVillesComponent,
    ListSitesComponent,
    ListRegionComponent,
    ListClientSitesComponent,
    WinclientAcceuilComponent,
    ProvinceComponent,
    LocaliteComponent,
    ListAnomaliesClientGroupeComponent
  ],
  imports: [
    CommonModule,
    ReferencesClientsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgbModule,
    SharedModule,
    GridModule,
    NgbAccordionModule
  ],
  exports: [
    // FournisseurTypeaheadComponent,
    // ProduitSearchSidebarComponent,
    // TypeFournisseurPipe
  ]
})


export class referencesClientsModule {

  constructor(private authService: AuthService) {
  }



}
