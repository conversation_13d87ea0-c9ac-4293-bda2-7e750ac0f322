



export class detailsRemoteBlFournisseur{
    id: number;
    nLigne: number;
    codeProduitSite: string;
    ppv: number;
    pph: number;
    tva: number;
    designation: string;
    qtiteCmde: number;
    qtiteLivr: number;
    datePeremption: string;

}


export class CmdBlRemoteFournisseur{
    private id: number;
    private dateBl: Date;
    private numerCmd: number;
    private numeroBl: number;
    private codeClientSite: string;
    private details: detailsRemoteBlFournisseur[];

}

