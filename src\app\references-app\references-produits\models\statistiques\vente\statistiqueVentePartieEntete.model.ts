import { TypeVente } from 'src/app/references-app/references-produits/enums/vente/TypeVente.enum';
import { TypeEncaissementVente } from 'src/app/references-app/references-produits/enums/vente/TypeEncaissementVente.enum';
import { ModePaiement } from 'src/app/references-app/references-produits/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { Client } from 'src/app/references-app/references-produits/models/tiers/client/client.model';
import { Medecin } from 'src/app/references-app/references-produits/models/common/medecin.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';


export class StatistiqueVentePartieEntete { 
    client?: Client;
    dateFacture?: any;
    dateVente?: any;
    id?: number;
    medecin?: Medecin;
    modePaiement?: ModePaiement;
    numeroFacture?: number;
    numeroVente?: number;
    operateur?: Operateur;
    typeEncaissement?: TypeEncaissementVente;
    typeVente?: TypeVente;
}

