import { ModePaiement } from "src/app/references-app/references-produits/enums/common/ModePaiement.enum"
import { ConventionAssurance } from "../../assurance/conventionAssurance.model"
import { Medecin } from "../../common/medecin.model"
import { CategorieProduit } from "../../produit/base/categorieProduit.model"
import { FamilleTarifaire } from "../../produit/base/familleTarifaire.model"
import { FormeProduit } from "../../produit/base/formeProduit.model"
import { Client } from "../../tiers/client/client.model"
import { Fournisseur } from "../../tiers/fournisseur/fournisseur.model"
import { Operateur } from "../../common/operateur.model"
import { Produit } from "../../produit/base/produit.model"
import { Rayon } from "../../produit/base/rayon.model"
import { TypeEncaissementVente } from "src/app/references-app/references-produits/enums/vente/TypeEncaissementVente.enum"
import { Depot } from "../../produit/stock/depot.model"


export class ArreteCaisseCriteria {
    categorieProduit: CategorieProduit // 
    client: Client
    convention: ConventionAssurance
    dateDebut: string //
    dateFin: string //
    familleTarifaire: FamilleTarifaire //
    forme: FormeProduit //
    laboratoire: Fournisseur
    listeTypesVentes: string
    medecin: Medecin //
    modePaiement: ModePaiement
    operateur: Operateur //
    produit: Produit
    rayon: Rayon
    typeClient: string
    typeEncaissement: TypeEncaissementVente
    zone: Depot //


    constructor() {

    }
}