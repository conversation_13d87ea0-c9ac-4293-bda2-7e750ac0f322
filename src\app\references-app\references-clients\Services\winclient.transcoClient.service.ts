import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { Page } from '../../referential/models/Page/page.model';
import { TranscoClient, TranscoClientCriteria } from '../models/transcoClient';


@Injectable({
  providedIn: 'root'
})
export class TranscoClientService {

  constructor(private http: HttpClient) { }
  


  getAllTranscoClientPaginated({page =0}:{page:number}) {
    return this.http.get<Page<TranscoClient>>(`${env.winclient_base_url}/api/winclient/transco-clients`, { 
        params:{page}
     });
  }

  searchTranscoClient(transcoClientCriteria: Partial<TranscoClientCriteria>) {
    return this.http.post<TranscoClient[]>(`${env.winclient_base_url}/api/winclient/transco-clients/search`, transcoClientCriteria);
  }

  createTranscoClient(transcoClient: TranscoClient) {
    return this.http.post<TranscoClient>(`${env.winclient_base_url}/api/winclient/transco-clients`, transcoClient);
  }

  // updateTranscoClient(id:string,transcoClient: TranscoClient) {
  //   return this.http.put<TranscoClient>(`${env.winclient_base_url}/api/winclient/transco-client/${id}`, transcoClient);
  // }

  getTranscoClientById(id: number) {
    return this.http.get<TranscoClient>(`${env.winclient_base_url}/api/winclient/transco-clients/${id}`);
  }

  deleteTranscoClient(id: number) {
    return this.http.delete<TranscoClient>(`${env.winclient_base_url}/api/winclient/transco-clients/${id}`);
  }

  
  
 
}