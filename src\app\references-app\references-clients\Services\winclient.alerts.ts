import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment as env } from 'src/environments/environment';
import { Page } from "../../referential/models/Page/page.model";
import { AlertClient } from "../models/alertClient";

@Injectable({
  providedIn: 'root'
})
export class WinclientAlertsService {
  constructor(private httpClient: HttpClient) { }


  getAlerts({page = 0, size = 21}: { page?: number, size?: number}) {
    return this.httpClient.get<Page<AlertClient>>(`${env.winclient_base_url}/api/winclient/alertes`,{params: { page, size }});
  }

  changeAlertStatus({id, etat,userId}:{id: number, etat: number,userId: string}) {
    return this.httpClient.put(`${env.winclient_base_url}/api/winclient/alertes/${id}/etat`, {},{params: { etat,userId },responseType:'text'});
  }
 
}