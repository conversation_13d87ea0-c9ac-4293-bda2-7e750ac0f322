import { TypeStatistiqueCriteria } from "src/app/references-app/references-produits/enums/statistiques/TypeStatistiqueCriteria.enum";
import { CategorieProduit } from "../../../produit/base/categorieProduit.model";
import { FormeProduit } from "../../../produit/base/formeProduit.model";
import { Rayon } from "../../../produit/base/rayon.model";
import { Fournisseur } from "../../../tiers/fournisseur/fournisseur.model";

export class AssistantCmdAchatCriteria {

    allProduits?: boolean
    categorie?: CategorieProduit;
    forme?: FormeProduit;
    laboratoire?: Fournisseur;
    rayon?: Rayon;

    couvMax: number
    couvMin: number


    typeQteCmd?: TypeStatistiqueCriteria;
    typeStatAssistantsCmdAchat?: TypeStatistiqueCriteria

    nbrJour?: number;
}   