import {Injectable, Injector} from '@angular/core';
import {
    HttpRequest,
    HttpResponse,
    HttpHandler,
    HttpEvent,
    HttpInterceptor,
    HttpClient,
    HttpBackend
} from '@angular/common/http';
import {Observable, of, throwError} from 'rxjs';
import {delay, mergeMap, materialize, dematerialize} from 'rxjs/operators';
import {FakeDataService} from './fake-data.service';
import {fakeData} from './data/data.home';

function isNumeric(value) {
    return /^-?\d+$/.test(value);
}

@Injectable()
export class FakeBackendInterceptor implements HttpInterceptor {

    constructor(private fakeDataService: FakeDataService) {
    }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        const {url, method, headers, body, urlWithParams, params} = request;
        // wrap in delayed observable to simulate server api call

        const handleRoute = () => {
            const mainRouteKeys = url.split('/').slice(1);
            if (url && isNumeric(mainRouteKeys[mainRouteKeys.length - 1]) && getSubData(mainRouteKeys.slice(0, mainRouteKeys.length - 1), fakeData)) {
                return ok(this.fakeDataService.getDataItem(mainRouteKeys, mainRouteKeys[mainRouteKeys.length - 1]))
            }
            if (url && getSubData(mainRouteKeys, fakeData)) {
                return ok(this.fakeDataService.getDataList(mainRouteKeys, params, body));
            }
            // if (url && url.includes('/auth')) {
            //     return ok(this.fakeDataService.login(body));
            // }
            // if (url && url.endsWith('search')) {
            //     return ok(this.fakeDataService.getDataList(mainRouteKeys, params, body));
            // }
            if (url && url.endsWith('edit') && getSubData(mainRouteKeys.slice(0,mainRouteKeys.length - 1), fakeData)) {
                console.log('we are entering here');
                const urlPaths = url.split('/');
                return ok(this.fakeDataService.addItemToList(mainRouteKeys.slice(0,mainRouteKeys.length - 1), body));
            }
            return next.handle(request);
        };

        function getSubData(keys, fullData) {

            let data = fullData;

            for (const key of keys) {
                if (data) {
                    data = data[key];
                } else {
                    return null;
                }
            }
            return data;
        }

        function getItems() {
            return ok({test: 'hello'});
        }

        // route functions


        // helper functions

        function ok(response?) {
            return of(new HttpResponse({status: 200, body: response}));
        }

        function error(message) {
            return throwError({error: {message}});
        }

        function unauthorized() {
            return throwError({status: 401, error: {message: 'Unauthorised'}});
        }

        function isLoggedIn() {
            return headers.get('Authorization') === 'Bearer fake-jwt-token';
        }

        function idFromUrl() {
            const urlParts = url.split('/');
            return parseInt(urlParts[urlParts.length - 1], null);
        }

        return (of(null) as any)
            .pipe(mergeMap(handleRoute))
            .pipe(materialize()) // call materialize and dematerialize to ensure delay even if an error is thrown (https://github.com/Reactive-Extensions/RxJS/issues/648)
            .pipe(delay(500))
            .pipe(dematerialize());
    }
}

