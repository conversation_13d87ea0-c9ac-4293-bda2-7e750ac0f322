{"assures": [{"id": 1, "nom": "Reilly Group"}, {"id": 2, "nom": "Keebler and Sons"}, {"id": 3, "nom": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>"}, {"id": 4, "nom": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>"}, {"id": 5, "nom": "Carter Group"}, {"id": 6, "nom": "Schaden Group"}, {"id": 7, "nom": "<PERSON><PERSON><PERSON>"}, {"id": 8, "nom": "Block, Keebler and Jaskolski"}, {"id": 9, "nom": "Considine, Simonis and Hane"}, {"id": 10, "nom": "<PERSON><PERSON><PERSON>, Rau and Flatley"}, {"id": 11, "nom": "Cormier and Sons"}, {"id": 12, "nom": "Bernier<PERSON><PERSON>"}, {"id": 13, "nom": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>"}, {"id": 14, "nom": "Prosacco and Sons"}, {"id": 15, "nom": "Mueller Inc"}, {"id": 16, "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 17, "nom": "Lind-<PERSON><PERSON>"}, {"id": 18, "nom": "<PERSON><PERSON><PERSON>"}, {"id": 19, "nom": "Wisoky-<PERSON><PERSON><PERSON>"}, {"id": 20, "nom": "<PERSON><PERSON><PERSON> and Sons"}], "beneficiares": [{"id": 1, "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 2, "nom": "<PERSON><PERSON><PERSON>-Feest"}, {"id": 3, "nom": "<PERSON><PERSON><PERSON>"}, {"id": 4, "nom": "<PERSON><PERSON><PERSON>, Wolff and Mitchell"}, {"id": 5, "nom": "<PERSON><PERSON><PERSON>, <PERSON> and Schulist"}, {"id": 6, "nom": "Pacocha-Sawayn"}, {"id": 7, "nom": "<PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>"}, {"id": 8, "nom": "<PERSON><PERSON>-<PERSON>"}, {"id": 9, "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 10, "nom": "Stamm<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 11, "nom": "Williamson Inc"}, {"id": 12, "nom": "Johnston and Sons"}, {"id": 13, "nom": "Will LLC"}, {"id": 14, "nom": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>"}, {"id": 15, "nom": "Aufderhar<PERSON><PERSON>"}, {"id": 16, "nom": "Breitenberg-Breitenberg"}, {"id": 17, "nom": "McCullough Group"}, {"id": 18, "nom": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>"}, {"id": 19, "nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 20, "nom": "Hermiston-Stoltenberg"}], "ventes": [{"id": 1, "numVente": 3058, "dateVente": "2021-06-30T03:55:49Z", "assure": 3, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 75127, "tauxRemb": 61, "statut": "NF"}, {"id": 2, "numVente": 7128, "dateVente": "2021-07-15T00:02:27Z", "assure": 14, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 74923, "tauxRemb": 91, "statut": "AV"}, {"id": 3, "numVente": 4635, "dateVente": "2021-06-14T21:44:32Z", "assure": 19, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 42591, "tauxRemb": 88, "statut": "NF"}, {"id": 4, "numVente": 9814, "dateVente": "2021-07-25T08:59:28Z", "assure": 17, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 11339, "tauxRemb": 42, "statut": "AV"}, {"id": 5, "numVente": 1346, "dateVente": "2022-02-18T06:50:48Z", "assure": 2, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 91832, "tauxRemb": 19, "statut": "AV"}, {"id": 6, "numVente": 2902, "dateVente": "2021-05-31T00:09:55Z", "assure": 15, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 29192, "tauxRemb": 79, "statut": "NF"}, {"id": 7, "numVente": 5002, "dateVente": "2021-06-12T20:07:22Z", "assure": 19, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 75364, "tauxRemb": 90, "statut": "AV"}, {"id": 8, "numVente": 7397, "dateVente": "2021-11-09T00:44:41Z", "assure": 1, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 56555, "tauxRemb": 51, "statut": "NF"}, {"id": 9, "numVente": 6226, "dateVente": "2021-11-02T03:03:58Z", "assure": 5, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 79046, "tauxRemb": 21, "statut": "NF"}, {"id": 10, "numVente": 4012, "dateVente": "2021-07-30T00:49:15Z", "assure": 19, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 33031, "tauxRemb": 37, "statut": "NF"}, {"id": 11, "numVente": 3599, "dateVente": "2021-09-23T12:31:17Z", "assure": 12, "beneficiare": 12, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 68243, "tauxRemb": 20, "statut": "AV"}, {"id": 12, "numVente": 6538, "dateVente": "2021-09-13T11:00:11Z", "assure": 7, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 36956, "tauxRemb": 75, "statut": "NF"}, {"id": 13, "numVente": 8681, "dateVente": "2021-11-19T16:27:14Z", "assure": 13, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 52517, "tauxRemb": 8, "statut": "NF"}, {"id": 14, "numVente": 7846, "dateVente": "2022-03-17T22:10:07Z", "assure": 2, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 97884, "tauxRemb": 100, "statut": "NF"}, {"id": 15, "numVente": 4377, "dateVente": "2021-06-18T07:36:20Z", "assure": 12, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 71475, "tauxRemb": 50, "statut": "AV"}, {"id": 16, "numVente": 2584, "dateVente": "2022-03-09T18:34:58Z", "assure": 9, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 26821, "tauxRemb": 24, "statut": "NF"}, {"id": 17, "numVente": 2642, "dateVente": "2022-05-11T16:49:21Z", "assure": 17, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 72465, "tauxRemb": 54, "statut": "NF"}, {"id": 18, "numVente": 9602, "dateVente": "2021-09-13T17:37:50Z", "assure": 11, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 96041, "tauxRemb": 98, "statut": "NF"}, {"id": 19, "numVente": 6834, "dateVente": "2021-07-30T17:46:19Z", "assure": 1, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 43428, "tauxRemb": 35, "statut": "NF"}, {"id": 20, "numVente": 4690, "dateVente": "2022-03-21T00:36:06Z", "assure": 11, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 54006, "tauxRemb": 65, "statut": "AV"}, {"id": 21, "numVente": 2489, "dateVente": "2022-01-04T21:06:07Z", "assure": 3, "beneficiare": 7, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 97466, "tauxRemb": 89, "statut": "NF"}, {"id": 22, "numVente": 9068, "dateVente": "2021-12-18T04:15:49Z", "assure": 20, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 53222, "tauxRemb": 71, "statut": "AV"}, {"id": 23, "numVente": 1410, "dateVente": "2021-10-30T11:52:44Z", "assure": 12, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 89811, "tauxRemb": 69, "statut": "NF"}, {"id": 24, "numVente": 2396, "dateVente": "2021-06-30T13:06:45Z", "assure": 11, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 17268, "tauxRemb": 32, "statut": "NF"}, {"id": 25, "numVente": 2797, "dateVente": "2022-04-07T14:20:59Z", "assure": 11, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 79416, "tauxRemb": 99, "statut": "AV"}, {"id": 26, "numVente": 9407, "dateVente": "2022-02-04T01:05:43Z", "assure": 16, "beneficiare": 5, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 79543, "tauxRemb": 50, "statut": "NF"}, {"id": 27, "numVente": 7605, "dateVente": "2021-11-21T02:22:04Z", "assure": 16, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 72185, "tauxRemb": 93, "statut": "NF"}, {"id": 28, "numVente": 6053, "dateVente": "2021-06-13T00:26:52Z", "assure": 5, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 87942, "tauxRemb": 98, "statut": "NF"}, {"id": 29, "numVente": 4316, "dateVente": "2021-08-21T15:57:21Z", "assure": 4, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 59408, "tauxRemb": 38, "statut": "AV"}, {"id": 30, "numVente": 9141, "dateVente": "2021-07-21T01:50:37Z", "assure": 15, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 54487, "tauxRemb": 31, "statut": "AV"}, {"id": 31, "numVente": 2516, "dateVente": "2021-08-16T05:50:53Z", "assure": 20, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 71628, "tauxRemb": 1, "statut": "NF"}, {"id": 32, "numVente": 1923, "dateVente": "2021-10-18T02:11:31Z", "assure": 16, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 12924, "tauxRemb": 95, "statut": "AV"}, {"id": 33, "numVente": 8291, "dateVente": "2022-01-11T08:22:54Z", "assure": 16, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 37199, "tauxRemb": 93, "statut": "NF"}, {"id": 34, "numVente": 1574, "dateVente": "2021-06-29T12:33:42Z", "assure": 9, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 53735, "tauxRemb": 51, "statut": "NF"}, {"id": 35, "numVente": 5044, "dateVente": "2022-05-15T09:27:36Z", "assure": 2, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 47181, "tauxRemb": 42, "statut": "NF"}, {"id": 36, "numVente": 5221, "dateVente": "2021-11-25T04:14:57Z", "assure": 20, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 62355, "tauxRemb": 43, "statut": "AV"}, {"id": 37, "numVente": 4261, "dateVente": "2021-10-16T22:07:06Z", "assure": 18, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 96634, "tauxRemb": 3, "statut": "AV"}, {"id": 38, "numVente": 5650, "dateVente": "2021-12-23T13:19:10Z", "assure": 15, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 46065, "tauxRemb": 50, "statut": "AV"}, {"id": 39, "numVente": 5395, "dateVente": "2022-01-29T19:13:23Z", "assure": 2, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 89017, "tauxRemb": 99, "statut": "NF"}, {"id": 40, "numVente": 1225, "dateVente": "2021-10-19T02:24:30Z", "assure": 1, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 61591, "tauxRemb": 33, "statut": "AV"}, {"id": 41, "numVente": 4750, "dateVente": "2021-12-03T06:38:47Z", "assure": 13, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 42776, "tauxRemb": 45, "statut": "NF"}, {"id": 42, "numVente": 3443, "dateVente": "2021-11-24T17:31:52Z", "assure": 19, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 95489, "tauxRemb": 93, "statut": "NF"}, {"id": 43, "numVente": 5965, "dateVente": "2021-11-29T07:41:10Z", "assure": 4, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 65533, "tauxRemb": 60, "statut": "NF"}, {"id": 44, "numVente": 8190, "dateVente": "2021-10-21T04:09:10Z", "assure": 12, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 33740, "tauxRemb": 78, "statut": "NF"}, {"id": 45, "numVente": 7406, "dateVente": "2022-02-16T21:49:17Z", "assure": 13, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 36681, "tauxRemb": 82, "statut": "NF"}, {"id": 46, "numVente": 2143, "dateVente": "2022-02-15T14:37:37Z", "assure": 11, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 38292, "tauxRemb": 43, "statut": "AV"}, {"id": 47, "numVente": 5889, "dateVente": "2021-07-03T15:36:20Z", "assure": 13, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 23102, "tauxRemb": 67, "statut": "NF"}, {"id": 48, "numVente": 4036, "dateVente": "2021-06-03T12:41:50Z", "assure": 8, "beneficiare": 7, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 79746, "tauxRemb": 13, "statut": "AV"}, {"id": 49, "numVente": 4802, "dateVente": "2022-03-29T16:55:33Z", "assure": 16, "beneficiare": 12, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 18128, "tauxRemb": 15, "statut": "AV"}, {"id": 50, "numVente": 5387, "dateVente": "2021-09-26T03:49:48Z", "assure": 1, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 64650, "tauxRemb": 40, "statut": "AV"}, {"id": 51, "numVente": 8582, "dateVente": "2021-05-21T21:44:17Z", "assure": 13, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 34509, "tauxRemb": 48, "statut": "AV"}, {"id": 52, "numVente": 6498, "dateVente": "2022-01-09T06:19:42Z", "assure": 15, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 15459, "tauxRemb": 28, "statut": "NF"}, {"id": 53, "numVente": 1879, "dateVente": "2022-01-16T10:32:03Z", "assure": 17, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 50993, "tauxRemb": 68, "statut": "AV"}, {"id": 54, "numVente": 5603, "dateVente": "2021-07-26T07:47:13Z", "assure": 15, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 53802, "tauxRemb": 91, "statut": "AV"}, {"id": 55, "numVente": 2191, "dateVente": "2021-07-25T04:15:39Z", "assure": 15, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 64097, "tauxRemb": 16, "statut": "AV"}, {"id": 56, "numVente": 1778, "dateVente": "2021-06-03T18:20:35Z", "assure": 10, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 29421, "tauxRemb": 63, "statut": "AV"}, {"id": 57, "numVente": 7517, "dateVente": "2021-08-29T01:49:04Z", "assure": 17, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 88391, "tauxRemb": 87, "statut": "NF"}, {"id": 58, "numVente": 4727, "dateVente": "2021-08-24T22:13:30Z", "assure": 19, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 92438, "tauxRemb": 76, "statut": "NF"}, {"id": 59, "numVente": 1160, "dateVente": "2021-11-02T17:53:23Z", "assure": 4, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 53778, "tauxRemb": 43, "statut": "NF"}, {"id": 60, "numVente": 6898, "dateVente": "2021-07-23T09:56:59Z", "assure": 8, "beneficiare": 12, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 65610, "tauxRemb": 31, "statut": "AV"}, {"id": 61, "numVente": 8190, "dateVente": "2021-06-18T20:47:18Z", "assure": 17, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 61687, "tauxRemb": 94, "statut": "AV"}, {"id": 62, "numVente": 9841, "dateVente": "2021-10-28T12:07:26Z", "assure": 7, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 72446, "tauxRemb": 45, "statut": "AV"}, {"id": 63, "numVente": 1297, "dateVente": "2022-01-30T23:02:06Z", "assure": 9, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 15537, "tauxRemb": 87, "statut": "AV"}, {"id": 64, "numVente": 7881, "dateVente": "2021-08-24T13:35:28Z", "assure": 12, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 64478, "tauxRemb": 89, "statut": "NF"}, {"id": 65, "numVente": 4157, "dateVente": "2021-10-24T02:33:02Z", "assure": 4, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 76030, "tauxRemb": 96, "statut": "NF"}, {"id": 66, "numVente": 4398, "dateVente": "2022-01-21T15:10:43Z", "assure": 4, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 69418, "tauxRemb": 72, "statut": "NF"}, {"id": 67, "numVente": 4555, "dateVente": "2021-12-27T10:13:50Z", "assure": 11, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 86722, "tauxRemb": 84, "statut": "NF"}, {"id": 68, "numVente": 3662, "dateVente": "2022-04-10T06:18:58Z", "assure": 20, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 35953, "tauxRemb": 89, "statut": "AV"}, {"id": 69, "numVente": 7166, "dateVente": "2021-12-28T07:50:20Z", "assure": 13, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 22532, "tauxRemb": 5, "statut": "AV"}, {"id": 70, "numVente": 3686, "dateVente": "2021-08-24T03:17:54Z", "assure": 9, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 97560, "tauxRemb": 57, "statut": "NF"}, {"id": 71, "numVente": 2319, "dateVente": "2022-05-09T14:51:19Z", "assure": 12, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 41973, "tauxRemb": 28, "statut": "NF"}, {"id": 72, "numVente": 7785, "dateVente": "2021-05-26T01:28:04Z", "assure": 11, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 74376, "tauxRemb": 41, "statut": "AV"}, {"id": 73, "numVente": 9398, "dateVente": "2021-06-14T13:36:16Z", "assure": 2, "beneficiare": 7, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 54088, "tauxRemb": 58, "statut": "NF"}, {"id": 74, "numVente": 4449, "dateVente": "2021-12-03T18:28:32Z", "assure": 15, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 68874, "tauxRemb": 99, "statut": "AV"}, {"id": 75, "numVente": 8970, "dateVente": "2021-06-19T20:50:51Z", "assure": 19, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 95702, "tauxRemb": 54, "statut": "AV"}, {"id": 76, "numVente": 7569, "dateVente": "2022-03-22T23:07:28Z", "assure": 15, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 37553, "tauxRemb": 87, "statut": "NF"}, {"id": 77, "numVente": 6286, "dateVente": "2022-03-09T09:27:34Z", "assure": 15, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 29472, "tauxRemb": 70, "statut": "AV"}, {"id": 78, "numVente": 6048, "dateVente": "2021-10-29T04:29:19Z", "assure": 5, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 43949, "tauxRemb": 74, "statut": "NF"}, {"id": 79, "numVente": 1915, "dateVente": "2022-01-24T10:59:51Z", "assure": 18, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 64552, "tauxRemb": 72, "statut": "AV"}, {"id": 80, "numVente": 9632, "dateVente": "2022-04-17T02:15:40Z", "assure": 4, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 93918, "tauxRemb": 78, "statut": "NF"}, {"id": 81, "numVente": 8800, "dateVente": "2021-06-24T04:43:16Z", "assure": 16, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 28578, "tauxRemb": 3, "statut": "NF"}, {"id": 82, "numVente": 9841, "dateVente": "2022-01-11T12:12:49Z", "assure": 15, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 53098, "tauxRemb": 16, "statut": "NF"}, {"id": 83, "numVente": 3083, "dateVente": "2021-12-29T09:06:09Z", "assure": 7, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 20748, "tauxRemb": 74, "statut": "NF"}, {"id": 84, "numVente": 9874, "dateVente": "2021-07-24T23:25:10Z", "assure": 2, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 60439, "tauxRemb": 22, "statut": "NF"}, {"id": 85, "numVente": 4990, "dateVente": "2022-05-13T16:17:55Z", "assure": 15, "beneficiare": 7, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 89884, "tauxRemb": 18, "statut": "AV"}, {"id": 86, "numVente": 4314, "dateVente": "2021-10-21T17:42:19Z", "assure": 20, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 36006, "tauxRemb": 53, "statut": "AV"}, {"id": 87, "numVente": 2283, "dateVente": "2021-12-08T13:00:08Z", "assure": 15, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 87583, "tauxRemb": 68, "statut": "AV"}, {"id": 88, "numVente": 6905, "dateVente": "2021-08-16T15:45:38Z", "assure": 4, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 59890, "tauxRemb": 22, "statut": "AV"}, {"id": 89, "numVente": 9689, "dateVente": "2021-12-25T07:32:38Z", "assure": 1, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 70729, "tauxRemb": 57, "statut": "NF"}, {"id": 90, "numVente": 9479, "dateVente": "2022-04-27T11:54:22Z", "assure": 3, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 80626, "tauxRemb": 61, "statut": "AV"}, {"id": 91, "numVente": 8638, "dateVente": "2021-07-21T15:33:19Z", "assure": 19, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 94133, "tauxRemb": 96, "statut": "NF"}, {"id": 92, "numVente": 8865, "dateVente": "2021-06-08T21:07:14Z", "assure": 19, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 67904, "tauxRemb": 64, "statut": "NF"}, {"id": 93, "numVente": 2820, "dateVente": "2021-12-19T04:45:54Z", "assure": 13, "beneficiare": 17, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 62848, "tauxRemb": 77, "statut": "NF"}, {"id": 94, "numVente": 6264, "dateVente": "2021-09-20T17:51:51Z", "assure": 18, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 99822, "tauxRemb": 92, "statut": "NF"}, {"id": 95, "numVente": 2725, "dateVente": "2021-08-15T03:41:30Z", "assure": 12, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 14914, "tauxRemb": 89, "statut": "NF"}, {"id": 96, "numVente": 7923, "dateVente": "2022-01-11T00:42:01Z", "assure": 5, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 43162, "tauxRemb": 72, "statut": "NF"}, {"id": 97, "numVente": 8899, "dateVente": "2021-06-28T01:28:56Z", "assure": 7, "beneficiare": 3, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 15922, "tauxRemb": 13, "statut": "AV"}, {"id": 98, "numVente": 9243, "dateVente": "2021-10-29T10:49:37Z", "assure": 2, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 44874, "tauxRemb": 95, "statut": "AV"}, {"id": 99, "numVente": 8439, "dateVente": "2022-04-03T02:55:37Z", "assure": 6, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 80263, "tauxRemb": 97, "statut": "NF"}, {"id": 100, "numVente": 4934, "dateVente": "2021-10-01T17:49:36Z", "assure": 10, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 98007, "tauxRemb": 94, "statut": "NF"}, {"id": 101, "numVente": 9423, "dateVente": "2021-06-18T20:15:24Z", "assure": 13, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 36644, "tauxRemb": 84, "statut": "AV"}, {"id": 102, "numVente": 6510, "dateVente": "2022-01-23T04:22:04Z", "assure": 15, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 35689, "tauxRemb": 88, "statut": "AV"}, {"id": 103, "numVente": 7510, "dateVente": "2021-09-02T01:55:07Z", "assure": 13, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 72863, "tauxRemb": 33, "statut": "AV"}, {"id": 104, "numVente": 2551, "dateVente": "2022-02-05T16:13:15Z", "assure": 17, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 12963, "tauxRemb": 81, "statut": "AV"}, {"id": 105, "numVente": 5742, "dateVente": "2022-03-30T14:16:13Z", "assure": 12, "beneficiare": 17, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 83392, "tauxRemb": 53, "statut": "AV"}, {"id": 106, "numVente": 5728, "dateVente": "2021-07-09T09:19:55Z", "assure": 11, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 16707, "tauxRemb": 75, "statut": "AV"}, {"id": 107, "numVente": 1184, "dateVente": "2021-09-13T13:00:32Z", "assure": 13, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 74907, "tauxRemb": 35, "statut": "NF"}, {"id": 108, "numVente": 9335, "dateVente": "2021-12-07T22:40:16Z", "assure": 17, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 64250, "tauxRemb": 72, "statut": "NF"}, {"id": 109, "numVente": 2193, "dateVente": "2021-10-10T10:34:29Z", "assure": 13, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 99038, "tauxRemb": 57, "statut": "AV"}, {"id": 110, "numVente": 9073, "dateVente": "2021-11-30T14:05:43Z", "assure": 20, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 49595, "tauxRemb": 64, "statut": "AV"}, {"id": 111, "numVente": 3522, "dateVente": "2021-11-15T01:08:33Z", "assure": 13, "beneficiare": 5, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 81969, "tauxRemb": 53, "statut": "AV"}, {"id": 112, "numVente": 9433, "dateVente": "2021-10-20T20:25:02Z", "assure": 12, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 76962, "tauxRemb": 53, "statut": "AV"}, {"id": 113, "numVente": 6336, "dateVente": "2022-01-21T01:08:01Z", "assure": 3, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 89312, "tauxRemb": 83, "statut": "NF"}, {"id": 114, "numVente": 6352, "dateVente": "2021-09-24T22:01:00Z", "assure": 3, "beneficiare": 17, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 58148, "tauxRemb": 61, "statut": "NF"}, {"id": 115, "numVente": 4251, "dateVente": "2022-01-31T16:53:04Z", "assure": 2, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 13408, "tauxRemb": 66, "statut": "AV"}, {"id": 116, "numVente": 5908, "dateVente": "2021-11-28T15:08:22Z", "assure": 2, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 89001, "tauxRemb": 12, "statut": "AV"}, {"id": 117, "numVente": 4637, "dateVente": "2021-08-11T02:10:42Z", "assure": 13, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 37464, "tauxRemb": 82, "statut": "NF"}, {"id": 118, "numVente": 7185, "dateVente": "2022-01-21T03:05:00Z", "assure": 12, "beneficiare": 3, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 88096, "tauxRemb": 81, "statut": "AV"}, {"id": 119, "numVente": 5317, "dateVente": "2021-07-25T19:23:09Z", "assure": 13, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 30731, "tauxRemb": 30, "statut": "NF"}, {"id": 120, "numVente": 9972, "dateVente": "2021-12-20T12:21:07Z", "assure": 13, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 20451, "tauxRemb": 26, "statut": "AV"}, {"id": 121, "numVente": 8267, "dateVente": "2021-12-29T15:01:30Z", "assure": 12, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 73316, "tauxRemb": 29, "statut": "NF"}, {"id": 122, "numVente": 1586, "dateVente": "2021-10-03T03:50:10Z", "assure": 20, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 54736, "tauxRemb": 61, "statut": "NF"}, {"id": 123, "numVente": 3929, "dateVente": "2021-10-30T11:06:04Z", "assure": 15, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 10900, "tauxRemb": 9, "statut": "NF"}, {"id": 124, "numVente": 2827, "dateVente": "2021-06-10T10:04:21Z", "assure": 7, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 44477, "tauxRemb": 31, "statut": "NF"}, {"id": 125, "numVente": 4990, "dateVente": "2021-09-09T23:06:29Z", "assure": 18, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 39961, "tauxRemb": 48, "statut": "NF"}, {"id": 126, "numVente": 4360, "dateVente": "2022-04-24T13:54:35Z", "assure": 2, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 85441, "tauxRemb": 78, "statut": "AV"}, {"id": 127, "numVente": 1973, "dateVente": "2021-06-08T04:19:11Z", "assure": 20, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 43502, "tauxRemb": 33, "statut": "AV"}, {"id": 128, "numVente": 1034, "dateVente": "2021-06-22T02:08:02Z", "assure": 1, "beneficiare": 12, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 71225, "tauxRemb": 56, "statut": "NF"}, {"id": 129, "numVente": 6898, "dateVente": "2021-09-22T21:55:02Z", "assure": 6, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 89665, "tauxRemb": 65, "statut": "NF"}, {"id": 130, "numVente": 3598, "dateVente": "2022-05-01T19:13:24Z", "assure": 18, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 52271, "tauxRemb": 84, "statut": "NF"}, {"id": 131, "numVente": 4000, "dateVente": "2021-09-15T21:59:26Z", "assure": 1, "beneficiare": 17, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 51286, "tauxRemb": 79, "statut": "AV"}, {"id": 132, "numVente": 1205, "dateVente": "2021-11-17T05:01:17Z", "assure": 4, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 51896, "tauxRemb": 22, "statut": "AV"}, {"id": 133, "numVente": 1892, "dateVente": "2021-07-31T19:08:01Z", "assure": 4, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 76909, "tauxRemb": 56, "statut": "AV"}, {"id": 134, "numVente": 6997, "dateVente": "2022-02-13T18:14:18Z", "assure": 1, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 56298, "tauxRemb": 79, "statut": "NF"}, {"id": 135, "numVente": 6144, "dateVente": "2022-04-11T06:58:55Z", "assure": 4, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 82610, "tauxRemb": 71, "statut": "AV"}, {"id": 136, "numVente": 2453, "dateVente": "2022-05-12T18:54:21Z", "assure": 19, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 87418, "tauxRemb": 60, "statut": "AV"}, {"id": 137, "numVente": 8958, "dateVente": "2021-09-29T10:42:30Z", "assure": 7, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 78121, "tauxRemb": 50, "statut": "NF"}, {"id": 138, "numVente": 1696, "dateVente": "2022-04-21T16:31:24Z", "assure": 9, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 30870, "tauxRemb": 91, "statut": "NF"}, {"id": 139, "numVente": 6637, "dateVente": "2022-03-20T14:17:19Z", "assure": 9, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 35156, "tauxRemb": 80, "statut": "AV"}, {"id": 140, "numVente": 7564, "dateVente": "2021-07-30T17:29:17Z", "assure": 3, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 33494, "tauxRemb": 31, "statut": "AV"}, {"id": 141, "numVente": 3554, "dateVente": "2021-10-18T23:15:26Z", "assure": 14, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 83333, "tauxRemb": 100, "statut": "AV"}, {"id": 142, "numVente": 1269, "dateVente": "2022-04-05T23:37:06Z", "assure": 19, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 45154, "tauxRemb": 94, "statut": "AV"}, {"id": 143, "numVente": 3264, "dateVente": "2022-04-17T17:12:12Z", "assure": 3, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 32257, "tauxRemb": 51, "statut": "NF"}, {"id": 144, "numVente": 7843, "dateVente": "2022-03-13T07:11:43Z", "assure": 17, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 40399, "tauxRemb": 71, "statut": "AV"}, {"id": 145, "numVente": 6620, "dateVente": "2021-08-08T07:13:17Z", "assure": 6, "beneficiare": 12, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 66709, "tauxRemb": 74, "statut": "NF"}, {"id": 146, "numVente": 4916, "dateVente": "2022-01-04T06:09:35Z", "assure": 20, "beneficiare": 5, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 42253, "tauxRemb": 24, "statut": "NF"}, {"id": 147, "numVente": 8183, "dateVente": "2022-02-12T02:31:33Z", "assure": 2, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 36765, "tauxRemb": 75, "statut": "NF"}, {"id": 148, "numVente": 4077, "dateVente": "2021-08-27T05:27:15Z", "assure": 20, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 65810, "tauxRemb": 45, "statut": "AV"}, {"id": 149, "numVente": 6525, "dateVente": "2021-07-18T13:29:13Z", "assure": 14, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 94064, "tauxRemb": 20, "statut": "AV"}, {"id": 150, "numVente": 4329, "dateVente": "2021-06-11T01:44:13Z", "assure": 11, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 22005, "tauxRemb": 84, "statut": "AV"}, {"id": 151, "numVente": 2263, "dateVente": "2022-02-04T00:33:43Z", "assure": 9, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 65082, "tauxRemb": 60, "statut": "AV"}, {"id": 152, "numVente": 2630, "dateVente": "2021-06-03T16:40:53Z", "assure": 13, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 81590, "tauxRemb": 26, "statut": "NF"}, {"id": 153, "numVente": 7166, "dateVente": "2022-04-26T05:01:47Z", "assure": 4, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 39184, "tauxRemb": 18, "statut": "NF"}, {"id": 154, "numVente": 8064, "dateVente": "2021-05-28T19:27:16Z", "assure": 4, "beneficiare": 17, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 73574, "tauxRemb": 6, "statut": "AV"}, {"id": 155, "numVente": 1055, "dateVente": "2021-10-13T05:13:54Z", "assure": 19, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 44313, "tauxRemb": 29, "statut": "AV"}, {"id": 156, "numVente": 4974, "dateVente": "2021-10-13T23:32:37Z", "assure": 8, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 27079, "tauxRemb": 65, "statut": "NF"}, {"id": 157, "numVente": 5121, "dateVente": "2021-10-25T00:33:49Z", "assure": 14, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 22982, "tauxRemb": 0, "statut": "NF"}, {"id": 158, "numVente": 2825, "dateVente": "2021-10-06T20:40:07Z", "assure": 4, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 83828, "tauxRemb": 65, "statut": "NF"}, {"id": 159, "numVente": 7244, "dateVente": "2022-04-14T04:09:38Z", "assure": 19, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 57466, "tauxRemb": 10, "statut": "AV"}, {"id": 160, "numVente": 5724, "dateVente": "2022-04-26T14:59:38Z", "assure": 2, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 39186, "tauxRemb": 7, "statut": "AV"}, {"id": 161, "numVente": 5493, "dateVente": "2021-07-31T03:26:53Z", "assure": 15, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 95408, "tauxRemb": 57, "statut": "AV"}, {"id": 162, "numVente": 6855, "dateVente": "2021-10-10T18:50:20Z", "assure": 17, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 57297, "tauxRemb": 31, "statut": "NF"}, {"id": 163, "numVente": 1231, "dateVente": "2022-03-11T03:12:59Z", "assure": 3, "beneficiare": 2, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 38196, "tauxRemb": 40, "statut": "AV"}, {"id": 164, "numVente": 6308, "dateVente": "2021-10-04T02:02:41Z", "assure": 13, "beneficiare": 7, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 59142, "tauxRemb": 93, "statut": "NF"}, {"id": 165, "numVente": 2849, "dateVente": "2021-06-11T20:17:58Z", "assure": 4, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 17897, "tauxRemb": 3, "statut": "AV"}, {"id": 166, "numVente": 6927, "dateVente": "2021-05-29T09:18:23Z", "assure": 5, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 47140, "tauxRemb": 44, "statut": "AV"}, {"id": 167, "numVente": 3386, "dateVente": "2021-08-30T09:28:50Z", "assure": 1, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 65862, "tauxRemb": 17, "statut": "NF"}, {"id": 168, "numVente": 5563, "dateVente": "2021-12-17T06:19:08Z", "assure": 18, "beneficiare": 16, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 15222, "tauxRemb": 35, "statut": "AV"}, {"id": 169, "numVente": 9130, "dateVente": "2021-08-28T02:37:32Z", "assure": 17, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 11801, "tauxRemb": 98, "statut": "AV"}, {"id": 170, "numVente": 6264, "dateVente": "2022-01-25T08:31:46Z", "assure": 10, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 62226, "tauxRemb": 83, "statut": "NF"}, {"id": 171, "numVente": 6398, "dateVente": "2022-04-01T10:12:01Z", "assure": 2, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 92181, "tauxRemb": 70, "statut": "NF"}, {"id": 172, "numVente": 2611, "dateVente": "2021-10-04T23:18:41Z", "assure": 12, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 68373, "tauxRemb": 46, "statut": "AV"}, {"id": 173, "numVente": 4113, "dateVente": "2021-11-18T16:32:05Z", "assure": 15, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 65690, "tauxRemb": 69, "statut": "AV"}, {"id": 174, "numVente": 2961, "dateVente": "2021-10-29T04:13:46Z", "assure": 20, "beneficiare": 7, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 40623, "tauxRemb": 60, "statut": "AV"}, {"id": 175, "numVente": 2250, "dateVente": "2021-11-24T09:23:51Z", "assure": 20, "beneficiare": 14, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 52149, "tauxRemb": 45, "statut": "NF"}, {"id": 176, "numVente": 2304, "dateVente": "2021-06-29T19:45:46Z", "assure": 1, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 91052, "tauxRemb": 35, "statut": "NF"}, {"id": 177, "numVente": 3916, "dateVente": "2021-09-16T10:59:29Z", "assure": 2, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 33319, "tauxRemb": 68, "statut": "NF"}, {"id": 178, "numVente": 2909, "dateVente": "2022-04-14T14:34:52Z", "assure": 20, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 26976, "tauxRemb": 83, "statut": "NF"}, {"id": 179, "numVente": 2046, "dateVente": "2021-06-13T08:10:40Z", "assure": 12, "beneficiare": 18, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 69384, "tauxRemb": 42, "statut": "AV"}, {"id": 180, "numVente": 7337, "dateVente": "2022-02-06T11:35:42Z", "assure": 7, "beneficiare": 12, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 95132, "tauxRemb": 100, "statut": "NF"}, {"id": 181, "numVente": 6124, "dateVente": "2022-01-27T20:19:38Z", "assure": 12, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 38285, "tauxRemb": 72, "statut": "AV"}, {"id": 182, "numVente": 7000, "dateVente": "2022-05-15T04:52:50Z", "assure": 8, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 56207, "tauxRemb": 1, "statut": "AV"}, {"id": 183, "numVente": 7699, "dateVente": "2021-12-24T20:48:47Z", "assure": 1, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 94616, "tauxRemb": 49, "statut": "AV"}, {"id": 184, "numVente": 4491, "dateVente": "2021-06-24T07:44:34Z", "assure": 15, "beneficiare": 9, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 16942, "tauxRemb": 79, "statut": "AV"}, {"id": 185, "numVente": 3262, "dateVente": "2021-08-28T04:09:11Z", "assure": 20, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 35152, "tauxRemb": 72, "statut": "NF"}, {"id": 186, "numVente": 3199, "dateVente": "2022-02-02T04:49:32Z", "assure": 7, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 87247, "tauxRemb": 42, "statut": "AV"}, {"id": 187, "numVente": 2313, "dateVente": "2021-10-10T14:18:58Z", "assure": 2, "beneficiare": 13, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 54902, "tauxRemb": 92, "statut": "NF"}, {"id": 188, "numVente": 5383, "dateVente": "2021-11-08T13:39:20Z", "assure": 15, "beneficiare": 3, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 76910, "tauxRemb": 94, "statut": "NF"}, {"id": 189, "numVente": 8911, "dateVente": "2022-05-03T14:20:59Z", "assure": 12, "beneficiare": 15, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 67090, "tauxRemb": 69, "statut": "NF"}, {"id": 190, "numVente": 7644, "dateVente": "2022-03-04T01:51:41Z", "assure": 8, "beneficiare": 20, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 64588, "tauxRemb": 60, "statut": "AV"}, {"id": 191, "numVente": 6922, "dateVente": "2022-04-08T07:12:57Z", "assure": 20, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 98510, "tauxRemb": 0, "statut": "AV"}, {"id": 192, "numVente": 6065, "dateVente": "2021-06-19T17:24:39Z", "assure": 7, "beneficiare": 4, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 54137, "tauxRemb": 90, "statut": "AV"}, {"id": 193, "numVente": 7696, "dateVente": "2022-03-20T08:05:31Z", "assure": 4, "beneficiare": 19, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 81751, "tauxRemb": 49, "statut": "NF"}, {"id": 194, "numVente": 2124, "dateVente": "2021-12-19T05:35:15Z", "assure": 10, "beneficiare": 11, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 29858, "tauxRemb": 73, "statut": "NF"}, {"id": 195, "numVente": 4046, "dateVente": "2022-04-09T15:54:00Z", "assure": 20, "beneficiare": 8, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 11960, "tauxRemb": 72, "statut": "NF"}, {"id": 196, "numVente": 2692, "dateVente": "2022-03-13T12:54:22Z", "assure": 5, "beneficiare": 17, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 63440, "tauxRemb": 80, "statut": "NF"}, {"id": 197, "numVente": 4237, "dateVente": "2021-11-15T04:39:00Z", "assure": 2, "beneficiare": 10, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 46584, "tauxRemb": 32, "statut": "NF"}, {"id": 198, "numVente": 2532, "dateVente": "2022-03-23T22:50:53Z", "assure": 14, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 95002, "tauxRemb": 78, "statut": "NF"}, {"id": 199, "numVente": 2661, "dateVente": "2022-01-02T20:20:40Z", "assure": 10, "beneficiare": 6, "convention": "Tier Payant Pharmacie", "organisme": "CNSS", "mntVente": 54361, "tauxRemb": 51, "statut": "NF"}, {"id": 200, "numVente": 4668, "dateVente": "2021-10-31T12:22:34Z", "assure": 1, "beneficiare": 1, "convention": "Tier Payant Pharmacie", "organisme": "CNOPS", "mntVente": 46423, "tauxRemb": 28, "statut": "NF"}], "factures": [{"id": 1, "numFacture": 7117, "mntFacture": 972529, "dateFacture": "2022-04-08T15:03:57Z", "ventes": [42, 42], "dateEnvoie": "2021-08-05T05:26:52Z", "dateReglement": "2021-12-11T17:45:26Z", "statut": "EN"}, {"id": 2, "numFacture": 7443, "mntFacture": 834399, "dateFacture": "2021-07-26T17:00:41Z", "ventes": [32, 12], "dateEnvoie": "2022-02-21T21:02:15Z", "dateReglement": "2021-09-07T16:46:04Z", "statut": "AN"}, {"id": 3, "numFacture": 2860, "mntFacture": 567001, "dateFacture": "2021-08-28T01:26:06Z", "ventes": [72], "dateEnvoie": "2022-03-06T14:45:12Z", "dateReglement": "2021-08-16T20:34:21Z", "statut": "RE"}, {"id": 4, "numFacture": 4895, "mntFacture": 813957, "dateFacture": "2021-11-29T03:19:01Z", "ventes": [62, 52], "dateEnvoie": "2022-05-13T23:29:42Z", "dateReglement": "2022-03-18T05:52:35Z", "statut": "RE"}, {"id": 5, "numFacture": 6115, "mntFacture": 710297, "dateFacture": "2021-08-10T13:15:51Z", "ventes": [12], "dateEnvoie": "2021-06-03T02:43:14Z", "dateReglement": "2022-03-18T23:24:52Z", "statut": "AN"}, {"id": 6, "numFacture": 4723, "mntFacture": 648593, "dateFacture": "2021-12-13T01:51:50Z", "ventes": [22, 62], "dateEnvoie": "2022-04-24T12:00:44Z", "dateReglement": "2021-12-14T15:38:11Z", "statut": "EC"}, {"id": 7, "numFacture": 6491, "mntFacture": 420930, "dateFacture": "2021-06-27T00:19:39Z", "ventes": [82, 92], "dateEnvoie": "2022-04-25T05:34:06Z", "dateReglement": "2021-07-11T19:50:25Z", "statut": "EN"}, {"id": 8, "numFacture": 9167, "mntFacture": 701005, "dateFacture": "2021-06-16T23:43:59Z", "ventes": [22, 32], "dateEnvoie": "2021-09-04T22:49:31Z", "dateReglement": "2021-10-01T11:00:45Z", "statut": "AN"}, {"id": 9, "numFacture": 9772, "mntFacture": 562446, "dateFacture": "2021-10-13T21:34:34Z", "ventes": [52, 2], "dateEnvoie": "2022-04-30T12:20:06Z", "dateReglement": "2021-12-31T05:58:33Z", "statut": "RE"}, {"id": 10, "numFacture": 1593, "mntFacture": 117397, "dateFacture": "2021-07-17T06:16:22Z", "ventes": [52, 92], "dateEnvoie": "2021-07-04T13:53:19Z", "dateReglement": "2021-09-09T07:59:57Z", "statut": "RE"}, {"id": 11, "numFacture": 7650, "mntFacture": 572314, "dateFacture": "2022-04-10T12:30:41Z", "ventes": [42, 12], "dateEnvoie": "2021-12-16T19:51:18Z", "dateReglement": "2021-11-16T11:42:15Z", "statut": "AN"}, {"id": 12, "numFacture": 8363, "mntFacture": 1203092, "dateFacture": "2021-12-20T12:07:34Z", "ventes": [2, 92], "dateEnvoie": "2022-02-04T17:51:43Z", "dateReglement": "2021-05-22T16:57:57Z", "statut": "RE"}, {"id": 13, "numFacture": 8487, "mntFacture": 311220, "dateFacture": "2022-02-09T02:17:33Z", "ventes": [2, 62], "dateEnvoie": "2022-02-21T19:01:52Z", "dateReglement": "2021-11-24T11:19:59Z", "statut": "RE"}, {"id": 14, "numFacture": 6757, "mntFacture": 715898, "dateFacture": "2022-05-17T23:26:08Z", "ventes": [2, 42], "dateEnvoie": "2021-05-28T08:36:57Z", "dateReglement": "2021-09-06T02:48:22Z", "statut": "EC"}, {"id": 15, "numFacture": 2734, "mntFacture": 799788, "dateFacture": "2021-08-19T16:53:32Z", "ventes": [32, 42], "dateEnvoie": "2021-05-20T12:02:59Z", "dateReglement": "2021-08-02T03:39:40Z", "statut": "EN"}, {"id": 16, "numFacture": 9296, "mntFacture": 998186, "dateFacture": "2021-08-24T11:32:10Z", "ventes": [2, 92], "dateEnvoie": "2021-11-07T04:09:01Z", "dateReglement": "2021-06-02T09:19:46Z", "statut": "AN"}, {"id": 17, "numFacture": 1452, "mntFacture": 514109, "dateFacture": "2021-07-04T16:13:44Z", "ventes": [32, 82], "dateEnvoie": "2022-01-06T10:03:51Z", "dateReglement": "2021-07-22T16:27:35Z", "statut": "EN"}, {"id": 18, "numFacture": 6589, "mntFacture": 918818, "dateFacture": "2022-05-01T00:41:01Z", "ventes": [52, 82], "dateEnvoie": "2021-10-24T16:13:25Z", "dateReglement": "2022-04-03T07:22:36Z", "statut": "EC"}, {"id": 19, "numFacture": 8351, "mntFacture": 1389740, "dateFacture": "2021-06-29T11:44:36Z", "ventes": [72, 22], "dateEnvoie": "2022-02-02T02:08:00Z", "dateReglement": "2022-03-06T02:11:35Z", "statut": "EC"}, {"id": 20, "numFacture": 8123, "mntFacture": 930775, "dateFacture": "2022-03-30T09:27:08Z", "ventes": [22, 12], "dateEnvoie": "2021-12-02T13:20:40Z", "dateReglement": "2021-12-02T14:47:47Z", "statut": "EN"}, {"id": 21, "numFacture": 7662, "mntFacture": 29928, "dateFacture": "2022-04-09T20:10:47Z", "ventes": [12, 22], "dateEnvoie": "2022-02-09T21:37:53Z", "dateReglement": "2022-03-08T00:36:57Z", "statut": "EN"}, {"id": 22, "numFacture": 5674, "mntFacture": 761780, "dateFacture": "2022-03-15T08:18:27Z", "ventes": [62, 52], "dateEnvoie": "2021-11-26T02:52:55Z", "dateReglement": "2021-06-30T06:53:37Z", "statut": "AN"}, {"id": 23, "numFacture": 6295, "mntFacture": 1245823, "dateFacture": "2021-06-24T17:51:41Z", "ventes": [2, 22], "dateEnvoie": "2021-08-17T18:22:54Z", "dateReglement": "2021-12-22T13:01:05Z", "statut": "EC"}, {"id": 24, "numFacture": 1452, "mntFacture": 483449, "dateFacture": "2022-04-20T01:56:28Z", "ventes": [12, 92], "dateEnvoie": "2022-01-31T18:25:24Z", "dateReglement": "2021-08-17T12:14:04Z", "statut": "RE"}, {"id": 25, "numFacture": 5752, "mntFacture": 545168, "dateFacture": "2021-08-07T23:00:57Z", "ventes": [72, 52], "dateEnvoie": "2021-10-22T23:38:28Z", "dateReglement": "2021-07-01T00:26:20Z", "statut": "EN"}, {"id": 26, "numFacture": 5145, "mntFacture": 853505, "dateFacture": "2022-04-10T23:46:44Z", "ventes": [22, 22], "dateEnvoie": "2021-12-27T04:44:32Z", "dateReglement": "2021-07-02T09:14:32Z", "statut": "EC"}, {"id": 27, "numFacture": 9125, "mntFacture": 1091880, "dateFacture": "2021-11-22T15:09:12Z", "ventes": [72, 62], "dateEnvoie": "2022-01-07T08:42:48Z", "dateReglement": "2022-02-07T15:26:57Z", "statut": "EN"}, {"id": 28, "numFacture": 1357, "mntFacture": 649043, "dateFacture": "2021-07-16T08:37:30Z", "ventes": [32, 22], "dateEnvoie": "2022-02-12T18:07:04Z", "dateReglement": "2021-10-28T16:30:57Z", "statut": "AN"}, {"id": 29, "numFacture": 9918, "mntFacture": 554993, "dateFacture": "2022-01-23T06:13:41Z", "ventes": [82, 32], "dateEnvoie": "2021-08-29T00:03:18Z", "dateReglement": "2022-05-03T22:49:53Z", "statut": "RE"}, {"id": 30, "numFacture": 2117, "mntFacture": 991489, "dateFacture": "2022-03-17T12:49:14Z", "ventes": [2, 32], "dateEnvoie": "2021-09-29T20:26:11Z", "dateReglement": "2021-10-07T04:00:05Z", "statut": "RE"}, {"id": 31, "numFacture": 3843, "mntFacture": 1029434, "dateFacture": "2021-10-07T23:53:32Z", "ventes": [2, 72], "dateEnvoie": "2021-12-13T15:04:54Z", "dateReglement": "2022-04-16T13:28:35Z", "statut": "AN"}, {"id": 32, "numFacture": 8344, "mntFacture": 1470506, "dateFacture": "2022-01-07T12:43:15Z", "ventes": [12, 42], "dateEnvoie": "2022-01-30T16:35:08Z", "dateReglement": "2021-08-31T08:36:14Z", "statut": "EN"}, {"id": 33, "numFacture": 6437, "mntFacture": 189414, "dateFacture": "2021-08-11T10:22:44Z", "ventes": [62, 22], "dateEnvoie": "2022-02-20T08:45:14Z", "dateReglement": "2022-04-30T02:55:04Z", "statut": "EC"}, {"id": 34, "numFacture": 6424, "mntFacture": 1498972, "dateFacture": "2021-09-24T08:04:41Z", "ventes": [42, 82], "dateEnvoie": "2021-09-04T04:26:06Z", "dateReglement": "2021-07-03T18:29:42Z", "statut": "EN"}, {"id": 35, "numFacture": 1557, "mntFacture": 759874, "dateFacture": "2021-06-30T03:52:39Z", "ventes": [32, 82], "dateEnvoie": "2021-09-05T20:35:49Z", "dateReglement": "2021-06-22T11:01:40Z", "statut": "RE"}, {"id": 36, "numFacture": 3234, "mntFacture": 355058, "dateFacture": "2021-09-28T01:03:05Z", "ventes": [42, 62], "dateEnvoie": "2021-11-17T17:28:05Z", "dateReglement": "2022-03-10T20:16:38Z", "statut": "RE"}, {"id": 37, "numFacture": 4161, "mntFacture": 1172909, "dateFacture": "2021-07-25T22:56:02Z", "ventes": [12, 72], "dateEnvoie": "2021-10-13T11:32:03Z", "dateReglement": "2021-09-25T09:02:39Z", "statut": "EN"}, {"id": 38, "numFacture": 7285, "mntFacture": 1434550, "dateFacture": "2021-11-17T10:40:06Z", "ventes": [2, 2], "dateEnvoie": "2021-07-18T10:21:02Z", "dateReglement": "2021-08-16T23:04:18Z", "statut": "EN"}, {"id": 39, "numFacture": 6032, "mntFacture": 668900, "dateFacture": "2021-10-10T07:42:54Z", "ventes": [12, 62], "dateEnvoie": "2022-05-08T23:26:21Z", "dateReglement": "2022-04-15T01:32:58Z", "statut": "EN"}, {"id": 40, "numFacture": 4696, "mntFacture": 381036, "dateFacture": "2022-04-12T14:26:52Z", "ventes": [52, 2], "dateEnvoie": "2022-04-11T21:27:23Z", "dateReglement": "2021-06-06T23:15:26Z", "statut": "EN"}, {"id": 41, "numFacture": 5136, "mntFacture": 563997, "dateFacture": "2021-09-23T06:45:51Z", "ventes": [62, 72], "dateEnvoie": "2021-12-10T18:57:12Z", "dateReglement": "2021-05-30T20:06:36Z", "statut": "AN"}, {"id": 42, "numFacture": 5695, "mntFacture": 1389977, "dateFacture": "2021-05-30T22:17:16Z", "ventes": [82, 52], "dateEnvoie": "2021-06-18T19:15:53Z", "dateReglement": "2021-10-16T19:39:40Z", "statut": "RE"}, {"id": 43, "numFacture": 4154, "mntFacture": 838639, "dateFacture": "2021-10-04T17:01:13Z", "ventes": [92, 42], "dateEnvoie": "2021-05-25T20:23:47Z", "dateReglement": "2022-02-17T21:29:10Z", "statut": "RE"}, {"id": 44, "numFacture": 7328, "mntFacture": 782384, "dateFacture": "2022-02-28T05:49:23Z", "ventes": [62, 42], "dateEnvoie": "2022-03-15T03:34:21Z", "dateReglement": "2021-06-19T17:39:32Z", "statut": "AN"}, {"id": 45, "numFacture": 1217, "mntFacture": 73456, "dateFacture": "2022-02-11T21:43:45Z", "ventes": [2, 2], "dateEnvoie": "2021-08-09T06:17:30Z", "dateReglement": "2022-02-19T23:35:58Z", "statut": "RE"}, {"id": 46, "numFacture": 9002, "mntFacture": 807564, "dateFacture": "2022-01-19T22:31:08Z", "ventes": [12, 62], "dateEnvoie": "2022-03-11T05:13:27Z", "dateReglement": "2022-03-07T16:43:30Z", "statut": "AN"}, {"id": 47, "numFacture": 3821, "mntFacture": 690882, "dateFacture": "2021-05-28T08:17:35Z", "ventes": [2, 42], "dateEnvoie": "2021-08-22T16:15:15Z", "dateReglement": "2021-12-23T09:16:21Z", "statut": "EC"}, {"id": 48, "numFacture": 7244, "mntFacture": 181853, "dateFacture": "2022-01-17T03:54:40Z", "ventes": [52, 32], "dateEnvoie": "2021-11-04T21:12:13Z", "dateReglement": "2022-02-20T20:31:45Z", "statut": "EN"}, {"id": 49, "numFacture": 2704, "mntFacture": 1315519, "dateFacture": "2022-05-14T11:49:50Z", "ventes": [22, 52], "dateEnvoie": "2021-09-28T08:43:23Z", "dateReglement": "2021-06-29T17:15:15Z", "statut": "AN"}, {"id": 50, "numFacture": 9537, "mntFacture": 1118534, "dateFacture": "2021-10-26T01:52:27Z", "ventes": [2, 82], "dateEnvoie": "2021-05-21T14:05:51Z", "dateReglement": "2021-11-24T18:10:43Z", "statut": "AN"}, {"id": 51, "numFacture": 4834, "mntFacture": 606939, "dateFacture": "2021-11-11T23:16:28Z", "ventes": [12, 72], "dateEnvoie": "2022-05-05T00:33:11Z", "dateReglement": "2022-01-27T06:29:39Z", "statut": "RE"}, {"id": 52, "numFacture": 6823, "mntFacture": 669537, "dateFacture": "2022-04-20T03:40:20Z", "ventes": [52, 32], "dateEnvoie": "2021-08-28T06:10:09Z", "dateReglement": "2021-05-25T04:20:55Z", "statut": "RE"}, {"id": 53, "numFacture": 1881, "mntFacture": 1204625, "dateFacture": "2021-11-05T05:26:37Z", "ventes": [32, 52], "dateEnvoie": "2021-08-27T13:28:42Z", "dateReglement": "2022-03-13T09:28:53Z", "statut": "EN"}, {"id": 54, "numFacture": 5069, "mntFacture": 200711, "dateFacture": "2022-04-19T10:53:14Z", "ventes": [92, 42], "dateEnvoie": "2022-01-09T21:45:17Z", "dateReglement": "2022-05-18T22:29:57Z", "statut": "EN"}, {"id": 55, "numFacture": 5156, "mntFacture": 1440204, "dateFacture": "2021-11-13T09:47:17Z", "ventes": [92, 92], "dateEnvoie": "2022-01-11T13:13:55Z", "dateReglement": "2021-09-19T14:02:38Z", "statut": "EN"}, {"id": 56, "numFacture": 3243, "mntFacture": 895807, "dateFacture": "2022-02-16T06:25:23Z", "ventes": [72, 2], "dateEnvoie": "2022-04-10T13:10:04Z", "dateReglement": "2022-02-19T05:38:17Z", "statut": "EC"}, {"id": 57, "numFacture": 2639, "mntFacture": 684729, "dateFacture": "2021-09-03T23:09:08Z", "ventes": [82, 82], "dateEnvoie": "2021-11-17T17:32:06Z", "dateReglement": "2022-05-14T03:42:16Z", "statut": "RE"}, {"id": 58, "numFacture": 7886, "mntFacture": 149113, "dateFacture": "2022-04-19T14:43:01Z", "ventes": [72, 72], "dateEnvoie": "2022-03-13T10:44:08Z", "dateReglement": "2022-01-13T21:51:36Z", "statut": "RE"}, {"id": 59, "numFacture": 8132, "mntFacture": 879869, "dateFacture": "2021-07-05T16:14:41Z", "ventes": [62, 2], "dateEnvoie": "2021-05-22T10:36:59Z", "dateReglement": "2021-10-09T19:01:26Z", "statut": "RE"}, {"id": 60, "numFacture": 7210, "mntFacture": 1316230, "dateFacture": "2021-12-09T17:29:14Z", "ventes": [62, 72], "dateEnvoie": "2021-10-04T14:55:30Z", "dateReglement": "2021-12-08T18:21:57Z", "statut": "RE"}, {"id": 61, "numFacture": 3060, "mntFacture": 1478501, "dateFacture": "2022-05-15T19:04:56Z", "ventes": [52, 72], "dateEnvoie": "2021-11-21T07:33:38Z", "dateReglement": "2021-11-17T07:15:49Z", "statut": "EN"}, {"id": 62, "numFacture": 8355, "mntFacture": 575453, "dateFacture": "2021-12-23T15:09:52Z", "ventes": [22, 32], "dateEnvoie": "2022-05-12T11:20:00Z", "dateReglement": "2022-04-10T22:12:22Z", "statut": "EN"}, {"id": 63, "numFacture": 7514, "mntFacture": 397570, "dateFacture": "2021-09-28T14:42:31Z", "ventes": [32, 42], "dateEnvoie": "2021-09-30T08:35:38Z", "dateReglement": "2022-04-08T15:59:02Z", "statut": "EN"}, {"id": 64, "numFacture": 1713, "mntFacture": 30143, "dateFacture": "2021-09-05T18:24:58Z", "ventes": [82, 12], "dateEnvoie": "2021-05-29T19:59:48Z", "dateReglement": "2021-11-06T12:20:29Z", "statut": "RE"}, {"id": 65, "numFacture": 7264, "mntFacture": 1068903, "dateFacture": "2021-07-14T11:43:23Z", "ventes": [32, 72], "dateEnvoie": "2022-01-30T13:06:41Z", "dateReglement": "2022-02-13T17:52:15Z", "statut": "AN"}, {"id": 66, "numFacture": 7459, "mntFacture": 593207, "dateFacture": "2021-12-01T20:06:45Z", "ventes": [42, 22], "dateEnvoie": "2021-09-20T14:18:36Z", "dateReglement": "2021-06-22T13:43:30Z", "statut": "AN"}, {"id": 67, "numFacture": 4752, "mntFacture": 953620, "dateFacture": "2022-03-17T19:11:03Z", "ventes": [32, 2], "dateEnvoie": "2021-12-07T08:24:46Z", "dateReglement": "2022-04-30T15:51:10Z", "statut": "EC"}, {"id": 68, "numFacture": 4003, "mntFacture": 809830, "dateFacture": "2021-08-01T09:39:36Z", "ventes": [52, 72], "dateEnvoie": "2021-06-14T09:47:56Z", "dateReglement": "2021-06-02T19:31:55Z", "statut": "AN"}, {"id": 69, "numFacture": 7117, "mntFacture": 751260, "dateFacture": "2021-12-08T15:51:21Z", "ventes": [42, 92], "dateEnvoie": "2022-04-08T00:19:38Z", "dateReglement": "2021-08-10T00:38:27Z", "statut": "RE"}, {"id": 70, "numFacture": 2733, "mntFacture": 1029105, "dateFacture": "2022-05-05T09:11:58Z", "ventes": [82, 82], "dateEnvoie": "2021-09-19T04:18:47Z", "dateReglement": "2022-02-23T08:51:49Z", "statut": "AN"}, {"id": 71, "numFacture": 9679, "mntFacture": 326465, "dateFacture": "2022-03-16T19:18:12Z", "ventes": [42, 82], "dateEnvoie": "2021-08-24T16:01:05Z", "dateReglement": "2022-01-30T07:24:05Z", "statut": "AN"}, {"id": 72, "numFacture": 3423, "mntFacture": 403725, "dateFacture": "2021-10-09T00:39:36Z", "ventes": [62, 42], "dateEnvoie": "2022-02-01T13:57:15Z", "dateReglement": "2021-08-07T00:10:42Z", "statut": "RE"}, {"id": 73, "numFacture": 7881, "mntFacture": 89751, "dateFacture": "2021-06-01T01:47:15Z", "ventes": [22, 42], "dateEnvoie": "2021-06-11T05:53:02Z", "dateReglement": "2022-01-22T06:07:40Z", "statut": "EN"}, {"id": 74, "numFacture": 4273, "mntFacture": 1140477, "dateFacture": "2022-01-13T12:43:09Z", "ventes": [92, 52], "dateEnvoie": "2021-07-13T05:29:39Z", "dateReglement": "2021-10-20T00:42:06Z", "statut": "AN"}, {"id": 75, "numFacture": 7621, "mntFacture": 793417, "dateFacture": "2021-11-12T10:26:26Z", "ventes": [62, 72], "dateEnvoie": "2022-03-05T19:58:51Z", "dateReglement": "2021-08-12T13:49:12Z", "statut": "AN"}, {"id": 76, "numFacture": 6616, "mntFacture": 1091218, "dateFacture": "2022-02-12T11:06:05Z", "ventes": [82, 2], "dateEnvoie": "2022-04-16T17:12:53Z", "dateReglement": "2022-03-19T17:38:36Z", "statut": "EN"}, {"id": 77, "numFacture": 4347, "mntFacture": 63693, "dateFacture": "2022-03-24T18:35:47Z", "ventes": [32, 52], "dateEnvoie": "2021-10-12T08:46:00Z", "dateReglement": "2022-02-23T14:15:11Z", "statut": "RE"}, {"id": 78, "numFacture": 9626, "mntFacture": 246832, "dateFacture": "2022-02-25T11:34:40Z", "ventes": [62, 22], "dateEnvoie": "2021-09-02T05:11:24Z", "dateReglement": "2021-11-12T06:17:03Z", "statut": "EN"}, {"id": 79, "numFacture": 7970, "mntFacture": 1044055, "dateFacture": "2021-09-19T02:40:36Z", "ventes": [42, 82], "dateEnvoie": "2022-02-22T04:00:53Z", "dateReglement": "2021-12-16T11:48:13Z", "statut": "RE"}, {"id": 80, "numFacture": 9069, "mntFacture": 749748, "dateFacture": "2021-10-18T15:14:45Z", "ventes": [32, 42], "dateEnvoie": "2021-11-04T22:26:30Z", "dateReglement": "2021-09-06T11:21:56Z", "statut": "RE"}, {"id": 81, "numFacture": 2073, "mntFacture": 1370934, "dateFacture": "2022-03-05T10:02:49Z", "ventes": [62, 52], "dateEnvoie": "2021-07-01T05:06:47Z", "dateReglement": "2021-11-10T01:23:16Z", "statut": "EC"}, {"id": 82, "numFacture": 7813, "mntFacture": 1453913, "dateFacture": "2021-06-16T16:04:41Z", "ventes": [22, 62], "dateEnvoie": "2021-06-19T00:08:51Z", "dateReglement": "2021-06-14T15:29:42Z", "statut": "EN"}, {"id": 83, "numFacture": 1483, "mntFacture": 1067274, "dateFacture": "2021-06-13T10:26:40Z", "ventes": [32, 12], "dateEnvoie": "2021-06-07T01:45:45Z", "dateReglement": "2021-12-24T12:08:26Z", "statut": "RE"}, {"id": 84, "numFacture": 6990, "mntFacture": 1338561, "dateFacture": "2022-03-10T03:33:59Z", "ventes": [2, 72], "dateEnvoie": "2021-08-14T22:41:19Z", "dateReglement": "2021-09-12T15:20:33Z", "statut": "EC"}, {"id": 85, "numFacture": 6214, "mntFacture": 1211460, "dateFacture": "2021-10-07T11:59:36Z", "ventes": [72, 42], "dateEnvoie": "2021-06-28T15:01:07Z", "dateReglement": "2022-01-08T16:48:16Z", "statut": "EC"}, {"id": 86, "numFacture": 2388, "mntFacture": 404537, "dateFacture": "2022-03-20T11:28:35Z", "ventes": [42, 62], "dateEnvoie": "2022-04-27T23:33:41Z", "dateReglement": "2021-12-12T06:32:49Z", "statut": "AN"}, {"id": 87, "numFacture": 9109, "mntFacture": 939256, "dateFacture": "2021-06-24T00:10:14Z", "ventes": [32, 62], "dateEnvoie": "2021-12-18T19:54:56Z", "dateReglement": "2022-04-21T14:44:10Z", "statut": "RE"}, {"id": 88, "numFacture": 9459, "mntFacture": 1195856, "dateFacture": "2022-02-20T04:49:20Z", "ventes": [52, 42], "dateEnvoie": "2021-07-28T18:00:18Z", "dateReglement": "2022-01-29T11:20:53Z", "statut": "EN"}, {"id": 89, "numFacture": 4405, "mntFacture": 1409525, "dateFacture": "2022-02-17T13:12:59Z", "ventes": [2, 42], "dateEnvoie": "2022-01-04T21:43:13Z", "dateReglement": "2021-06-06T04:20:28Z", "statut": "EN"}, {"id": 90, "numFacture": 8953, "mntFacture": 97391, "dateFacture": "2021-09-17T19:32:22Z", "ventes": [12, 22], "dateEnvoie": "2021-10-02T06:53:37Z", "dateReglement": "2022-04-23T11:28:30Z", "statut": "AN"}, {"id": 91, "numFacture": 7393, "mntFacture": 411898, "dateFacture": "2021-08-20T16:44:07Z", "ventes": [82, 92], "dateEnvoie": "2021-12-29T00:17:28Z", "dateReglement": "2021-11-13T13:15:43Z", "statut": "EC"}, {"id": 92, "numFacture": 8170, "mntFacture": 200366, "dateFacture": "2021-09-19T04:34:17Z", "ventes": [62, 22], "dateEnvoie": "2022-04-13T14:08:25Z", "dateReglement": "2021-07-08T16:21:56Z", "statut": "RE"}, {"id": 93, "numFacture": 5907, "mntFacture": 67465, "dateFacture": "2021-08-27T06:24:37Z", "ventes": [42, 62], "dateEnvoie": "2022-04-05T10:07:45Z", "dateReglement": "2021-08-10T04:40:12Z", "statut": "EN"}, {"id": 94, "numFacture": 3582, "mntFacture": 922373, "dateFacture": "2022-03-13T02:42:03Z", "ventes": [62, 52], "dateEnvoie": "2021-08-10T12:29:25Z", "dateReglement": "2021-09-24T21:23:38Z", "statut": "EN"}, {"id": 95, "numFacture": 1928, "mntFacture": 1010894, "dateFacture": "2021-07-12T12:00:08Z", "ventes": [62, 52], "dateEnvoie": "2022-05-07T19:59:30Z", "dateReglement": "2022-01-07T19:52:14Z", "statut": "EC"}, {"id": 96, "numFacture": 2330, "mntFacture": 14513, "dateFacture": "2022-02-06T06:33:00Z", "ventes": [2, 82], "dateEnvoie": "2021-06-01T05:47:32Z", "dateReglement": "2021-09-02T01:18:08Z", "statut": "AN"}, {"id": 97, "numFacture": 6257, "mntFacture": 817205, "dateFacture": "2021-12-11T04:05:27Z", "ventes": [72, 52], "dateEnvoie": "2021-07-11T03:11:50Z", "dateReglement": "2022-01-10T05:42:13Z", "statut": "AN"}, {"id": 98, "numFacture": 2023, "mntFacture": 1464693, "dateFacture": "2021-08-24T06:29:46Z", "ventes": [82, 72], "dateEnvoie": "2021-07-29T03:06:40Z", "dateReglement": "2021-10-06T14:00:41Z", "statut": "EN"}, {"id": 99, "numFacture": 8506, "mntFacture": 869933, "dateFacture": "2021-12-11T02:49:56Z", "ventes": [42, 72], "dateEnvoie": "2021-11-11T09:03:23Z", "dateReglement": "2022-01-31T07:15:55Z", "statut": "EN"}, {"id": 100, "numFacture": 3823, "mntFacture": 266442, "dateFacture": "2021-09-08T22:06:19Z", "ventes": [2, 12], "dateEnvoie": "2022-03-07T06:40:10Z", "dateReglement": "2022-03-20T12:15:13Z", "statut": "EN"}, {"id": 101, "numFacture": 9661, "mntFacture": 1396756, "dateFacture": "2021-09-04T12:07:47Z", "ventes": [92, 32], "dateEnvoie": "2021-09-06T09:49:57Z", "dateReglement": "2022-04-01T22:17:52Z", "statut": "RE"}, {"id": 102, "numFacture": 7243, "mntFacture": 1329848, "dateFacture": "2022-04-22T06:47:38Z", "ventes": [12, 52], "dateEnvoie": "2022-01-26T09:59:57Z", "dateReglement": "2021-08-27T20:09:26Z", "statut": "EC"}, {"id": 103, "numFacture": 2749, "mntFacture": 1221369, "dateFacture": "2021-05-25T08:21:38Z", "ventes": [72, 62], "dateEnvoie": "2021-10-11T21:45:46Z", "dateReglement": "2021-12-02T12:54:00Z", "statut": "AN"}, {"id": 104, "numFacture": 5562, "mntFacture": 866468, "dateFacture": "2021-06-04T02:58:27Z", "ventes": [82, 82], "dateEnvoie": "2021-10-30T01:43:21Z", "dateReglement": "2022-03-02T11:27:45Z", "statut": "RE"}, {"id": 105, "numFacture": 7216, "mntFacture": 215740, "dateFacture": "2021-11-27T02:19:22Z", "ventes": [92, 12], "dateEnvoie": "2022-03-25T05:38:50Z", "dateReglement": "2021-11-13T08:03:12Z", "statut": "AN"}, {"id": 106, "numFacture": 5706, "mntFacture": 226764, "dateFacture": "2021-08-08T14:57:24Z", "ventes": [12, 82], "dateEnvoie": "2021-06-04T12:17:06Z", "dateReglement": "2021-07-31T15:30:38Z", "statut": "AN"}, {"id": 107, "numFacture": 6016, "mntFacture": 411768, "dateFacture": "2021-12-22T11:44:06Z", "ventes": [52, 72], "dateEnvoie": "2021-09-08T12:28:02Z", "dateReglement": "2021-08-13T03:15:29Z", "statut": "RE"}, {"id": 108, "numFacture": 4631, "mntFacture": 56573, "dateFacture": "2021-06-30T13:59:47Z", "ventes": [72, 52], "dateEnvoie": "2021-10-24T16:28:06Z", "dateReglement": "2021-11-26T22:49:37Z", "statut": "EC"}, {"id": 109, "numFacture": 7787, "mntFacture": 306659, "dateFacture": "2021-06-11T20:24:12Z", "ventes": [2, 82], "dateEnvoie": "2022-02-07T09:25:56Z", "dateReglement": "2021-12-30T13:11:08Z", "statut": "EN"}, {"id": 110, "numFacture": 1071, "mntFacture": 1329993, "dateFacture": "2021-06-23T14:27:28Z", "ventes": [72, 52], "dateEnvoie": "2021-09-20T18:00:25Z", "dateReglement": "2022-01-04T17:00:20Z", "statut": "RE"}, {"id": 111, "numFacture": 1651, "mntFacture": 395278, "dateFacture": "2021-12-29T01:42:28Z", "ventes": [92, 52], "dateEnvoie": "2021-09-18T02:03:38Z", "dateReglement": "2021-09-18T12:30:58Z", "statut": "EN"}, {"id": 112, "numFacture": 9651, "mntFacture": 880241, "dateFacture": "2021-11-24T12:11:53Z", "ventes": [72, 82], "dateEnvoie": "2021-08-18T04:13:42Z", "dateReglement": "2021-08-24T21:16:47Z", "statut": "AN"}, {"id": 113, "numFacture": 4813, "mntFacture": 896848, "dateFacture": "2022-03-24T16:03:25Z", "ventes": [62, 92], "dateEnvoie": "2022-05-18T12:30:42Z", "dateReglement": "2021-05-30T16:19:57Z", "statut": "EC"}, {"id": 114, "numFacture": 3312, "mntFacture": 1194233, "dateFacture": "2021-10-09T14:21:21Z", "ventes": [42, 82], "dateEnvoie": "2022-03-09T05:16:14Z", "dateReglement": "2022-02-15T17:34:57Z", "statut": "RE"}, {"id": 115, "numFacture": 6095, "mntFacture": 21476, "dateFacture": "2022-05-17T09:20:58Z", "ventes": [72, 92], "dateEnvoie": "2021-09-26T10:07:11Z", "dateReglement": "2022-01-23T23:21:17Z", "statut": "EN"}, {"id": 116, "numFacture": 6751, "mntFacture": 1468902, "dateFacture": "2021-11-12T16:22:45Z", "ventes": [2, 52], "dateEnvoie": "2021-09-03T02:03:37Z", "dateReglement": "2021-09-18T04:39:34Z", "statut": "EN"}, {"id": 117, "numFacture": 6191, "mntFacture": 572489, "dateFacture": "2021-06-25T21:54:28Z", "ventes": [12, 92], "dateEnvoie": "2021-10-22T21:10:40Z", "dateReglement": "2021-09-01T18:05:05Z", "statut": "EC"}, {"id": 118, "numFacture": 5971, "mntFacture": 868050, "dateFacture": "2021-08-28T15:53:19Z", "ventes": [52, 22], "dateEnvoie": "2022-04-16T11:53:10Z", "dateReglement": "2022-03-14T04:51:03Z", "statut": "EN"}, {"id": 119, "numFacture": 5058, "mntFacture": 585095, "dateFacture": "2021-08-31T15:29:02Z", "ventes": [12, 62], "dateEnvoie": "2021-08-10T14:42:36Z", "dateReglement": "2021-09-17T15:57:53Z", "statut": "EN"}, {"id": 120, "numFacture": 5774, "mntFacture": 1469262, "dateFacture": "2021-10-21T04:43:18Z", "ventes": [32, 32], "dateEnvoie": "2021-12-15T09:45:29Z", "dateReglement": "2022-01-28T01:58:36Z", "statut": "EC"}, {"id": 121, "numFacture": 1535, "mntFacture": 927258, "dateFacture": "2021-12-21T23:40:11Z", "ventes": [92, 22], "dateEnvoie": "2022-03-05T03:14:40Z", "dateReglement": "2021-10-03T02:15:54Z", "statut": "AN"}, {"id": 122, "numFacture": 5250, "mntFacture": 1479061, "dateFacture": "2021-12-11T10:10:27Z", "ventes": [32, 42], "dateEnvoie": "2021-06-18T06:52:12Z", "dateReglement": "2021-10-06T16:38:52Z", "statut": "RE"}, {"id": 123, "numFacture": 2525, "mntFacture": 412324, "dateFacture": "2021-11-13T08:59:25Z", "ventes": [32, 82], "dateEnvoie": "2021-08-05T15:34:06Z", "dateReglement": "2022-05-06T01:18:47Z", "statut": "EN"}, {"id": 124, "numFacture": 9021, "mntFacture": 321099, "dateFacture": "2022-01-12T18:30:31Z", "ventes": [82, 92], "dateEnvoie": "2021-06-20T21:14:22Z", "dateReglement": "2022-01-21T17:52:24Z", "statut": "EN"}, {"id": 125, "numFacture": 8584, "mntFacture": 1416079, "dateFacture": "2021-09-23T20:30:06Z", "ventes": [92, 72], "dateEnvoie": "2022-02-08T15:08:49Z", "dateReglement": "2021-09-30T07:37:07Z", "statut": "AN"}, {"id": 126, "numFacture": 4572, "mntFacture": 550990, "dateFacture": "2021-10-08T00:43:11Z", "ventes": [42, 12], "dateEnvoie": "2021-07-30T17:27:08Z", "dateReglement": "2021-12-30T16:22:32Z", "statut": "RE"}, {"id": 127, "numFacture": 2353, "mntFacture": 406736, "dateFacture": "2022-04-14T21:18:36Z", "ventes": [22, 42], "dateEnvoie": "2021-11-06T02:32:47Z", "dateReglement": "2021-07-01T20:23:25Z", "statut": "AN"}, {"id": 128, "numFacture": 7544, "mntFacture": 1343073, "dateFacture": "2021-09-27T12:55:53Z", "ventes": [32, 62], "dateEnvoie": "2021-09-08T14:29:21Z", "dateReglement": "2022-01-19T22:00:12Z", "statut": "EN"}, {"id": 129, "numFacture": 8466, "mntFacture": 861911, "dateFacture": "2021-10-21T14:56:51Z", "ventes": [52, 72], "dateEnvoie": "2021-09-30T00:35:17Z", "dateReglement": "2021-11-22T07:47:35Z", "statut": "EN"}, {"id": 130, "numFacture": 4062, "mntFacture": 205118, "dateFacture": "2021-09-03T07:17:24Z", "ventes": [52, 12], "dateEnvoie": "2022-03-14T13:41:31Z", "dateReglement": "2021-06-13T08:31:21Z", "statut": "AN"}, {"id": 131, "numFacture": 2662, "mntFacture": 1476030, "dateFacture": "2021-08-07T21:06:35Z", "ventes": [62, 22], "dateEnvoie": "2021-06-15T22:13:50Z", "dateReglement": "2022-01-08T16:12:49Z", "statut": "AN"}, {"id": 132, "numFacture": 6872, "mntFacture": 882692, "dateFacture": "2022-04-23T07:10:07Z", "ventes": [12, 72], "dateEnvoie": "2022-02-22T16:35:37Z", "dateReglement": "2021-12-25T20:21:40Z", "statut": "EN"}, {"id": 133, "numFacture": 9153, "mntFacture": 1367023, "dateFacture": "2021-07-05T08:17:14Z", "ventes": [62, 62], "dateEnvoie": "2022-04-03T16:00:07Z", "dateReglement": "2021-07-11T23:12:53Z", "statut": "EC"}, {"id": 134, "numFacture": 4828, "mntFacture": 41666, "dateFacture": "2022-03-03T18:19:44Z", "ventes": [62, 52], "dateEnvoie": "2022-04-07T07:23:40Z", "dateReglement": "2021-10-15T12:11:46Z", "statut": "RE"}, {"id": 135, "numFacture": 7882, "mntFacture": 963466, "dateFacture": "2022-02-11T02:05:26Z", "ventes": [2, 82], "dateEnvoie": "2021-08-24T18:25:58Z", "dateReglement": "2021-06-22T13:36:41Z", "statut": "EC"}, {"id": 136, "numFacture": 5846, "mntFacture": 1114515, "dateFacture": "2021-12-08T12:18:04Z", "ventes": [62, 72], "dateEnvoie": "2021-11-06T13:16:44Z", "dateReglement": "2022-04-14T17:56:34Z", "statut": "RE"}, {"id": 137, "numFacture": 5239, "mntFacture": 24778, "dateFacture": "2022-05-18T15:11:47Z", "ventes": [32, 82], "dateEnvoie": "2022-04-05T13:45:56Z", "dateReglement": "2022-01-02T07:32:46Z", "statut": "EC"}, {"id": 138, "numFacture": 1795, "mntFacture": 138664, "dateFacture": "2022-04-29T15:50:24Z", "ventes": [62, 22], "dateEnvoie": "2022-03-31T11:30:35Z", "dateReglement": "2021-09-16T10:53:35Z", "statut": "EC"}, {"id": 139, "numFacture": 9106, "mntFacture": 330499, "dateFacture": "2021-10-27T19:04:22Z", "ventes": [82, 42], "dateEnvoie": "2021-06-18T03:54:55Z", "dateReglement": "2022-01-31T23:20:57Z", "statut": "EC"}, {"id": 140, "numFacture": 4658, "mntFacture": 925559, "dateFacture": "2022-02-07T00:35:37Z", "ventes": [52, 22], "dateEnvoie": "2022-05-16T23:45:52Z", "dateReglement": "2022-02-28T09:50:23Z", "statut": "AN"}, {"id": 141, "numFacture": 3334, "mntFacture": 171032, "dateFacture": "2022-01-20T10:00:34Z", "ventes": [52, 62], "dateEnvoie": "2021-11-17T10:17:39Z", "dateReglement": "2021-09-18T03:57:29Z", "statut": "EN"}, {"id": 142, "numFacture": 5241, "mntFacture": 1153303, "dateFacture": "2021-06-22T10:57:33Z", "ventes": [22, 52], "dateEnvoie": "2021-12-18T01:55:07Z", "dateReglement": "2022-05-07T14:35:40Z", "statut": "EN"}, {"id": 143, "numFacture": 1128, "mntFacture": 1457717, "dateFacture": "2021-11-13T08:20:43Z", "ventes": [42, 82], "dateEnvoie": "2021-09-08T21:45:33Z", "dateReglement": "2022-04-06T15:54:04Z", "statut": "EN"}, {"id": 144, "numFacture": 8744, "mntFacture": 948401, "dateFacture": "2022-03-27T21:34:44Z", "ventes": [82, 62], "dateEnvoie": "2021-12-22T08:13:17Z", "dateReglement": "2022-03-30T00:51:45Z", "statut": "EC"}, {"id": 145, "numFacture": 1220, "mntFacture": 656475, "dateFacture": "2021-07-05T11:28:53Z", "ventes": [22, 82], "dateEnvoie": "2021-08-18T21:18:50Z", "dateReglement": "2022-04-02T19:35:45Z", "statut": "EC"}, {"id": 146, "numFacture": 4748, "mntFacture": 430862, "dateFacture": "2021-10-04T08:43:09Z", "ventes": [22, 92], "dateEnvoie": "2021-08-18T00:52:27Z", "dateReglement": "2021-10-09T16:11:02Z", "statut": "AN"}, {"id": 147, "numFacture": 2698, "mntFacture": 454587, "dateFacture": "2021-11-10T16:57:26Z", "ventes": [22, 42], "dateEnvoie": "2021-07-06T19:56:14Z", "dateReglement": "2022-02-02T07:23:06Z", "statut": "EN"}, {"id": 148, "numFacture": 2285, "mntFacture": 798400, "dateFacture": "2022-03-14T07:43:31Z", "ventes": [62, 32], "dateEnvoie": "2022-03-07T16:50:31Z", "dateReglement": "2021-05-24T19:24:39Z", "statut": "EN"}, {"id": 149, "numFacture": 7424, "mntFacture": 1062128, "dateFacture": "2021-12-22T08:21:21Z", "ventes": [62, 62], "dateEnvoie": "2022-05-10T22:22:23Z", "dateReglement": "2022-02-05T11:23:47Z", "statut": "EN"}, {"id": 150, "numFacture": 9065, "mntFacture": 965699, "dateFacture": "2022-03-02T11:57:18Z", "ventes": [72, 32], "dateEnvoie": "2021-10-15T05:41:52Z", "dateReglement": "2022-03-17T21:15:54Z", "statut": "AN"}, {"id": 151, "numFacture": 7255, "mntFacture": 221783, "dateFacture": "2022-04-17T16:18:06Z", "ventes": [42, 22], "dateEnvoie": "2022-02-01T09:10:23Z", "dateReglement": "2022-04-23T17:35:23Z", "statut": "AN"}, {"id": 152, "numFacture": 5838, "mntFacture": 505318, "dateFacture": "2021-09-06T01:33:10Z", "ventes": [12, 62], "dateEnvoie": "2021-08-25T21:41:02Z", "dateReglement": "2022-01-26T19:02:54Z", "statut": "RE"}, {"id": 153, "numFacture": 3113, "mntFacture": 1314312, "dateFacture": "2022-04-07T12:16:21Z", "ventes": [2, 22], "dateEnvoie": "2021-09-22T16:30:14Z", "dateReglement": "2021-12-17T11:05:09Z", "statut": "EN"}, {"id": 154, "numFacture": 8975, "mntFacture": 756552, "dateFacture": "2022-04-04T07:45:21Z", "ventes": [2, 72], "dateEnvoie": "2021-08-13T04:33:04Z", "dateReglement": "2022-02-06T03:59:37Z", "statut": "AN"}, {"id": 155, "numFacture": 4545, "mntFacture": 1290670, "dateFacture": "2022-04-17T19:13:20Z", "ventes": [42, 42], "dateEnvoie": "2021-12-29T08:54:22Z", "dateReglement": "2021-07-15T06:07:58Z", "statut": "EN"}, {"id": 156, "numFacture": 7117, "mntFacture": 835992, "dateFacture": "2021-09-12T23:59:15Z", "ventes": [2, 12], "dateEnvoie": "2022-04-16T19:54:11Z", "dateReglement": "2021-10-28T13:17:35Z", "statut": "EN"}, {"id": 157, "numFacture": 3328, "mntFacture": 885712, "dateFacture": "2022-01-29T16:07:13Z", "ventes": [12, 42], "dateEnvoie": "2021-10-31T13:42:29Z", "dateReglement": "2021-08-13T19:17:27Z", "statut": "RE"}, {"id": 158, "numFacture": 9777, "mntFacture": 1274613, "dateFacture": "2021-06-27T14:40:07Z", "ventes": [52, 12], "dateEnvoie": "2022-05-11T10:38:45Z", "dateReglement": "2022-03-30T12:35:02Z", "statut": "RE"}, {"id": 159, "numFacture": 2321, "mntFacture": 918606, "dateFacture": "2021-11-23T16:43:02Z", "ventes": [12, 92], "dateEnvoie": "2021-11-09T14:00:31Z", "dateReglement": "2021-09-12T03:08:05Z", "statut": "EC"}, {"id": 160, "numFacture": 4979, "mntFacture": 1361277, "dateFacture": "2021-07-15T19:41:29Z", "ventes": [82, 12], "dateEnvoie": "2021-10-07T15:38:40Z", "dateReglement": "2022-02-26T20:43:50Z", "statut": "AN"}, {"id": 161, "numFacture": 9471, "mntFacture": 1028313, "dateFacture": "2021-07-24T15:51:27Z", "ventes": [42, 92], "dateEnvoie": "2021-09-27T00:05:42Z", "dateReglement": "2021-11-12T16:56:06Z", "statut": "RE"}, {"id": 162, "numFacture": 9783, "mntFacture": 606091, "dateFacture": "2022-01-09T10:38:29Z", "ventes": [22, 22], "dateEnvoie": "2021-07-17T17:31:02Z", "dateReglement": "2022-03-22T12:18:38Z", "statut": "EN"}, {"id": 163, "numFacture": 5665, "mntFacture": 55906, "dateFacture": "2022-02-26T18:43:15Z", "ventes": [92, 82], "dateEnvoie": "2021-08-28T13:15:01Z", "dateReglement": "2021-11-13T05:00:28Z", "statut": "EN"}, {"id": 164, "numFacture": 2324, "mntFacture": 365041, "dateFacture": "2022-03-20T11:47:14Z", "ventes": [32, 92], "dateEnvoie": "2022-01-15T19:58:45Z", "dateReglement": "2021-08-01T22:45:31Z", "statut": "RE"}, {"id": 165, "numFacture": 5976, "mntFacture": 971218, "dateFacture": "2021-12-16T00:10:18Z", "ventes": [72, 22], "dateEnvoie": "2021-06-13T09:16:41Z", "dateReglement": "2022-03-31T13:40:13Z", "statut": "EN"}, {"id": 166, "numFacture": 7205, "mntFacture": 490769, "dateFacture": "2021-08-14T21:47:13Z", "ventes": [72, 22], "dateEnvoie": "2021-08-23T07:57:26Z", "dateReglement": "2021-05-25T10:28:00Z", "statut": "EC"}, {"id": 167, "numFacture": 8846, "mntFacture": 92462, "dateFacture": "2021-05-30T11:44:35Z", "ventes": [42, 62], "dateEnvoie": "2022-03-13T22:07:27Z", "dateReglement": "2021-10-04T12:53:18Z", "statut": "AN"}, {"id": 168, "numFacture": 3563, "mntFacture": 390757, "dateFacture": "2021-07-31T20:10:24Z", "ventes": [92, 92], "dateEnvoie": "2022-04-05T18:08:16Z", "dateReglement": "2021-08-18T06:46:01Z", "statut": "EN"}, {"id": 169, "numFacture": 1971, "mntFacture": 401182, "dateFacture": "2021-06-18T04:11:19Z", "ventes": [12, 52], "dateEnvoie": "2021-08-25T00:51:39Z", "dateReglement": "2021-12-14T15:27:34Z", "statut": "EN"}, {"id": 170, "numFacture": 4355, "mntFacture": 1439695, "dateFacture": "2022-04-05T16:06:35Z", "ventes": [52, 2], "dateEnvoie": "2022-04-28T14:58:19Z", "dateReglement": "2021-09-12T01:37:37Z", "statut": "EN"}, {"id": 171, "numFacture": 4708, "mntFacture": 1123036, "dateFacture": "2021-06-24T04:22:11Z", "ventes": [52, 12], "dateEnvoie": "2021-08-27T07:40:14Z", "dateReglement": "2021-05-25T19:31:49Z", "statut": "AN"}, {"id": 172, "numFacture": 4435, "mntFacture": 19975, "dateFacture": "2022-05-09T16:46:04Z", "ventes": [32, 92], "dateEnvoie": "2021-12-05T02:37:23Z", "dateReglement": "2022-01-25T23:13:21Z", "statut": "AN"}, {"id": 173, "numFacture": 7698, "mntFacture": 581005, "dateFacture": "2021-10-03T18:45:00Z", "ventes": [92, 62], "dateEnvoie": "2021-10-05T21:19:23Z", "dateReglement": "2021-08-10T15:56:18Z", "statut": "RE"}, {"id": 174, "numFacture": 3487, "mntFacture": 446916, "dateFacture": "2021-12-21T12:41:25Z", "ventes": [22, 62], "dateEnvoie": "2021-11-20T10:30:37Z", "dateReglement": "2021-11-18T00:38:00Z", "statut": "AN"}, {"id": 175, "numFacture": 5360, "mntFacture": 1421349, "dateFacture": "2022-01-22T10:56:46Z", "ventes": [42, 22], "dateEnvoie": "2021-11-23T10:50:06Z", "dateReglement": "2021-09-04T20:34:33Z", "statut": "EN"}, {"id": 176, "numFacture": 5990, "mntFacture": 381966, "dateFacture": "2022-03-12T05:34:56Z", "ventes": [32, 52], "dateEnvoie": "2021-09-17T20:30:51Z", "dateReglement": "2021-09-13T19:13:37Z", "statut": "RE"}, {"id": 177, "numFacture": 8423, "mntFacture": 1257401, "dateFacture": "2021-06-04T04:50:40Z", "ventes": [12, 82], "dateEnvoie": "2021-06-28T06:31:51Z", "dateReglement": "2021-11-21T08:24:04Z", "statut": "EC"}, {"id": 178, "numFacture": 3256, "mntFacture": 1409526, "dateFacture": "2021-06-24T21:24:15Z", "ventes": [42, 62], "dateEnvoie": "2022-05-08T04:31:53Z", "dateReglement": "2022-04-02T17:41:02Z", "statut": "AN"}, {"id": 179, "numFacture": 5199, "mntFacture": 589888, "dateFacture": "2021-12-13T22:43:52Z", "ventes": [92, 72], "dateEnvoie": "2021-10-19T10:17:18Z", "dateReglement": "2021-08-05T08:11:30Z", "statut": "EN"}, {"id": 180, "numFacture": 8994, "mntFacture": 1148223, "dateFacture": "2021-08-18T04:36:11Z", "ventes": [82, 92], "dateEnvoie": "2022-05-12T00:27:53Z", "dateReglement": "2021-11-14T15:45:02Z", "statut": "RE"}, {"id": 181, "numFacture": 6583, "mntFacture": 1003461, "dateFacture": "2022-01-29T18:03:28Z", "ventes": [72, 2], "dateEnvoie": "2021-10-25T13:43:56Z", "dateReglement": "2022-01-27T03:03:40Z", "statut": "RE"}, {"id": 182, "numFacture": 3844, "mntFacture": 657675, "dateFacture": "2022-04-19T00:26:06Z", "ventes": [62, 12], "dateEnvoie": "2022-04-15T07:27:28Z", "dateReglement": "2021-06-03T17:12:55Z", "statut": "EC"}, {"id": 183, "numFacture": 3774, "mntFacture": 498702, "dateFacture": "2021-12-20T13:36:10Z", "ventes": [92, 72], "dateEnvoie": "2021-10-29T04:43:58Z", "dateReglement": "2021-12-05T00:29:55Z", "statut": "EC"}, {"id": 184, "numFacture": 9058, "mntFacture": 339711, "dateFacture": "2022-02-03T23:34:29Z", "ventes": [72, 52], "dateEnvoie": "2021-12-29T09:57:09Z", "dateReglement": "2022-01-04T13:49:27Z", "statut": "EC"}, {"id": 185, "numFacture": 7245, "mntFacture": 119522, "dateFacture": "2021-11-06T03:49:00Z", "ventes": [42, 22], "dateEnvoie": "2021-08-12T07:17:42Z", "dateReglement": "2021-10-10T21:46:26Z", "statut": "RE"}, {"id": 186, "numFacture": 4900, "mntFacture": 66998, "dateFacture": "2021-10-10T07:19:53Z", "ventes": [72, 12], "dateEnvoie": "2021-06-10T22:32:01Z", "dateReglement": "2022-04-09T17:25:34Z", "statut": "EN"}, {"id": 187, "numFacture": 7231, "mntFacture": 249240, "dateFacture": "2021-10-01T03:50:34Z", "ventes": [72, 42], "dateEnvoie": "2021-05-25T07:34:47Z", "dateReglement": "2021-07-13T19:32:43Z", "statut": "AN"}, {"id": 188, "numFacture": 3897, "mntFacture": 505804, "dateFacture": "2022-03-28T16:14:23Z", "ventes": [92, 32], "dateEnvoie": "2021-06-05T14:47:30Z", "dateReglement": "2021-06-27T10:35:20Z", "statut": "RE"}, {"id": 189, "numFacture": 4030, "mntFacture": 1363709, "dateFacture": "2021-08-21T08:21:32Z", "ventes": [62, 72], "dateEnvoie": "2021-12-07T11:27:25Z", "dateReglement": "2021-10-19T01:46:57Z", "statut": "AN"}, {"id": 190, "numFacture": 1359, "mntFacture": 970675, "dateFacture": "2021-06-17T18:29:47Z", "ventes": [2, 32], "dateEnvoie": "2022-02-10T21:31:17Z", "dateReglement": "2021-08-10T09:39:48Z", "statut": "EN"}, {"id": 191, "numFacture": 1032, "mntFacture": 438475, "dateFacture": "2022-02-19T22:56:04Z", "ventes": [92, 32], "dateEnvoie": "2021-11-30T14:48:44Z", "dateReglement": "2021-12-12T23:23:22Z", "statut": "EC"}, {"id": 192, "numFacture": 3339, "mntFacture": 555031, "dateFacture": "2022-03-05T03:19:52Z", "ventes": [92, 42], "dateEnvoie": "2021-07-21T06:52:09Z", "dateReglement": "2022-02-06T07:41:41Z", "statut": "EC"}, {"id": 193, "numFacture": 2989, "mntFacture": 826032, "dateFacture": "2021-10-22T02:16:38Z", "ventes": [92, 82], "dateEnvoie": "2022-03-10T18:32:32Z", "dateReglement": "2021-08-02T20:53:41Z", "statut": "EN"}, {"id": 194, "numFacture": 5662, "mntFacture": 684203, "dateFacture": "2021-07-16T09:03:42Z", "ventes": [72, 32], "dateEnvoie": "2022-02-14T07:09:31Z", "dateReglement": "2021-11-16T03:52:51Z", "statut": "EC"}, {"id": 195, "numFacture": 2501, "mntFacture": 888103, "dateFacture": "2021-12-15T07:41:15Z", "ventes": [2, 62], "dateEnvoie": "2022-02-28T21:22:01Z", "dateReglement": "2021-10-06T12:27:47Z", "statut": "EC"}, {"id": 196, "numFacture": 5978, "mntFacture": 264964, "dateFacture": "2022-01-04T15:33:58Z", "ventes": [62, 92], "dateEnvoie": "2021-09-13T21:23:25Z", "dateReglement": "2022-03-26T20:34:46Z", "statut": "RE"}, {"id": 197, "numFacture": 7026, "mntFacture": 1292603, "dateFacture": "2022-04-19T07:33:07Z", "ventes": [42, 92], "dateEnvoie": "2021-07-26T12:58:21Z", "dateReglement": "2022-04-24T00:51:45Z", "statut": "EN"}, {"id": 198, "numFacture": 6644, "mntFacture": 550364, "dateFacture": "2021-10-08T18:54:54Z", "ventes": [72, 62], "dateEnvoie": "2021-09-27T12:26:00Z", "dateReglement": "2022-04-15T11:23:16Z", "statut": "RE"}, {"id": 199, "numFacture": 7115, "mntFacture": 984321, "dateFacture": "2021-08-17T15:55:10Z", "ventes": [32, 2], "dateEnvoie": "2022-04-21T13:59:03Z", "dateReglement": "2021-07-16T13:27:24Z", "statut": "AN"}, {"id": 200, "numFacture": 7811, "mntFacture": 1290311, "dateFacture": "2021-09-10T03:45:29Z", "ventes": [52, 32], "dateEnvoie": "2021-11-04T09:30:07Z", "dateReglement": "2021-12-31T09:18:12Z", "statut": "RE"}, {"id": 201, "numFacture": 3785, "mntFacture": 779825, "dateFacture": "2021-09-23T20:11:00Z", "ventes": [22, 22], "dateEnvoie": "2021-07-01T22:32:09Z", "dateReglement": "2021-12-21T10:25:48Z", "statut": "EN"}, {"id": 202, "numFacture": 3991, "mntFacture": 527040, "dateFacture": "2021-08-07T20:01:31Z", "ventes": [32, 72], "dateEnvoie": "2022-04-26T19:18:41Z", "dateReglement": "2021-09-13T05:03:53Z", "statut": "AN"}, {"id": 203, "numFacture": 6900, "mntFacture": 817391, "dateFacture": "2021-08-13T20:55:59Z", "ventes": [72, 2], "dateEnvoie": "2021-07-24T15:40:12Z", "dateReglement": "2022-05-15T10:12:49Z", "statut": "RE"}, {"id": 204, "numFacture": 9674, "mntFacture": 1469587, "dateFacture": "2022-01-11T14:42:03Z", "ventes": [92, 42], "dateEnvoie": "2022-02-20T01:03:22Z", "dateReglement": "2021-11-03T09:39:33Z", "statut": "EN"}, {"id": 205, "numFacture": 4025, "mntFacture": 913081, "dateFacture": "2022-02-08T13:20:00Z", "ventes": [52, 92], "dateEnvoie": "2021-08-05T04:46:01Z", "dateReglement": "2021-07-31T04:17:11Z", "statut": "EN"}, {"id": 206, "numFacture": 5249, "mntFacture": 821510, "dateFacture": "2022-01-30T13:37:35Z", "ventes": [12, 82], "dateEnvoie": "2022-02-21T00:17:15Z", "dateReglement": "2021-09-24T00:11:29Z", "statut": "EC"}, {"id": 207, "numFacture": 2909, "mntFacture": 137249, "dateFacture": "2022-01-30T14:42:12Z", "ventes": [92, 52], "dateEnvoie": "2021-11-15T02:57:04Z", "dateReglement": "2022-01-13T12:53:14Z", "statut": "EN"}, {"id": 208, "numFacture": 3396, "mntFacture": 1294879, "dateFacture": "2021-10-04T10:16:18Z", "ventes": [32, 92], "dateEnvoie": "2021-09-30T02:28:39Z", "dateReglement": "2021-08-11T15:47:17Z", "statut": "EC"}, {"id": 209, "numFacture": 9063, "mntFacture": 801785, "dateFacture": "2022-01-28T13:20:55Z", "ventes": [62, 82], "dateEnvoie": "2021-11-10T17:33:07Z", "dateReglement": "2022-03-11T02:46:19Z", "statut": "EC"}, {"id": 210, "numFacture": 1975, "mntFacture": 1178839, "dateFacture": "2021-11-15T00:11:10Z", "ventes": [52, 22], "dateEnvoie": "2021-06-18T04:04:04Z", "dateReglement": "2021-10-20T15:39:50Z", "statut": "AN"}, {"id": 211, "numFacture": 2828, "mntFacture": 1071740, "dateFacture": "2022-01-30T02:03:51Z", "ventes": [2, 72], "dateEnvoie": "2021-07-26T03:57:34Z", "dateReglement": "2021-12-28T13:45:41Z", "statut": "EN"}, {"id": 212, "numFacture": 5760, "mntFacture": 1484047, "dateFacture": "2021-09-05T18:47:53Z", "ventes": [62, 2], "dateEnvoie": "2021-08-18T00:54:32Z", "dateReglement": "2021-08-23T04:14:25Z", "statut": "EN"}, {"id": 213, "numFacture": 5557, "mntFacture": 45442, "dateFacture": "2021-11-02T12:38:12Z", "ventes": [2, 42], "dateEnvoie": "2021-08-18T02:48:15Z", "dateReglement": "2022-05-02T04:59:48Z", "statut": "RE"}, {"id": 214, "numFacture": 8693, "mntFacture": 1163596, "dateFacture": "2022-02-10T19:58:10Z", "ventes": [22, 52], "dateEnvoie": "2022-02-20T23:10:05Z", "dateReglement": "2021-05-23T12:59:35Z", "statut": "EN"}, {"id": 215, "numFacture": 8518, "mntFacture": 311604, "dateFacture": "2022-03-15T20:06:47Z", "ventes": [92, 92], "dateEnvoie": "2021-11-06T16:28:59Z", "dateReglement": "2022-04-28T20:53:35Z", "statut": "AN"}, {"id": 216, "numFacture": 6936, "mntFacture": 70088, "dateFacture": "2021-08-21T01:14:03Z", "ventes": [82, 52], "dateEnvoie": "2021-05-25T03:12:04Z", "dateReglement": "2021-08-08T12:55:07Z", "statut": "AN"}, {"id": 217, "numFacture": 7345, "mntFacture": 246883, "dateFacture": "2021-05-25T10:28:10Z", "ventes": [82, 12], "dateEnvoie": "2022-03-10T06:07:40Z", "dateReglement": "2021-08-08T23:52:03Z", "statut": "RE"}, {"id": 218, "numFacture": 6905, "mntFacture": 67980, "dateFacture": "2021-12-25T02:09:06Z", "ventes": [42, 22], "dateEnvoie": "2021-11-26T23:40:14Z", "dateReglement": "2022-02-16T14:09:26Z", "statut": "EC"}, {"id": 219, "numFacture": 1206, "mntFacture": 1048099, "dateFacture": "2021-09-26T06:10:12Z", "ventes": [62, 12], "dateEnvoie": "2021-11-14T20:10:29Z", "dateReglement": "2021-11-10T04:22:42Z", "statut": "EN"}, {"id": 220, "numFacture": 7434, "mntFacture": 115156, "dateFacture": "2022-03-31T11:03:16Z", "ventes": [2, 2], "dateEnvoie": "2021-08-03T05:32:08Z", "dateReglement": "2022-02-26T23:51:32Z", "statut": "EN"}, {"id": 221, "numFacture": 1426, "mntFacture": 1053407, "dateFacture": "2022-02-22T23:22:12Z", "ventes": [72, 32], "dateEnvoie": "2021-09-13T18:42:06Z", "dateReglement": "2021-12-17T19:35:50Z", "statut": "AN"}, {"id": 222, "numFacture": 6679, "mntFacture": 557789, "dateFacture": "2021-12-27T02:27:21Z", "ventes": [2, 12], "dateEnvoie": "2021-11-05T06:21:46Z", "dateReglement": "2022-03-02T09:12:06Z", "statut": "RE"}, {"id": 223, "numFacture": 5494, "mntFacture": 345070, "dateFacture": "2021-12-26T21:19:03Z", "ventes": [22, 42], "dateEnvoie": "2021-06-27T19:05:05Z", "dateReglement": "2022-01-28T17:27:52Z", "statut": "EN"}, {"id": 224, "numFacture": 1120, "mntFacture": 150131, "dateFacture": "2021-10-08T07:51:58Z", "ventes": [62, 92], "dateEnvoie": "2021-07-11T10:10:30Z", "dateReglement": "2022-04-06T05:35:24Z", "statut": "EC"}, {"id": 225, "numFacture": 5461, "mntFacture": 294491, "dateFacture": "2021-09-18T03:57:24Z", "ventes": [62, 2], "dateEnvoie": "2021-12-20T19:44:41Z", "dateReglement": "2021-11-27T05:33:35Z", "statut": "EN"}, {"id": 226, "numFacture": 3097, "mntFacture": 1206663, "dateFacture": "2021-08-27T04:34:15Z", "ventes": [32, 2], "dateEnvoie": "2021-09-26T10:25:20Z", "dateReglement": "2022-05-07T06:02:06Z", "statut": "EN"}, {"id": 227, "numFacture": 2845, "mntFacture": 564382, "dateFacture": "2021-11-10T02:45:57Z", "ventes": [42, 72], "dateEnvoie": "2021-09-04T10:58:17Z", "dateReglement": "2022-04-26T18:04:20Z", "statut": "EC"}, {"id": 228, "numFacture": 7247, "mntFacture": 336132, "dateFacture": "2021-12-25T16:07:33Z", "ventes": [62, 62], "dateEnvoie": "2022-02-24T21:46:22Z", "dateReglement": "2021-12-24T23:10:43Z", "statut": "AN"}, {"id": 229, "numFacture": 7860, "mntFacture": 1162866, "dateFacture": "2021-10-23T04:52:20Z", "ventes": [12, 82], "dateEnvoie": "2021-08-31T12:43:13Z", "dateReglement": "2022-05-05T11:08:00Z", "statut": "EN"}, {"id": 230, "numFacture": 5976, "mntFacture": 704098, "dateFacture": "2021-07-02T13:27:03Z", "ventes": [32, 82], "dateEnvoie": "2022-01-27T15:19:05Z", "dateReglement": "2022-01-01T05:19:19Z", "statut": "RE"}, {"id": 231, "numFacture": 6112, "mntFacture": 528519, "dateFacture": "2021-09-16T03:36:48Z", "ventes": [62, 12], "dateEnvoie": "2021-12-12T12:44:08Z", "dateReglement": "2022-04-15T22:39:33Z", "statut": "EN"}, {"id": 232, "numFacture": 1689, "mntFacture": 1054164, "dateFacture": "2021-08-22T19:57:10Z", "ventes": [2, 62], "dateEnvoie": "2021-07-01T02:01:18Z", "dateReglement": "2022-01-12T10:57:47Z", "statut": "EC"}, {"id": 233, "numFacture": 9927, "mntFacture": 1038998, "dateFacture": "2021-06-08T19:52:48Z", "ventes": [52, 52], "dateEnvoie": "2021-05-19T02:59:59Z", "dateReglement": "2022-04-18T02:57:20Z", "statut": "AN"}, {"id": 234, "numFacture": 7402, "mntFacture": 243367, "dateFacture": "2021-11-02T17:17:23Z", "ventes": [22, 62], "dateEnvoie": "2021-06-27T09:51:47Z", "dateReglement": "2022-05-10T10:32:50Z", "statut": "RE"}, {"id": 235, "numFacture": 3099, "mntFacture": 1094827, "dateFacture": "2021-07-06T10:05:50Z", "ventes": [2, 12], "dateEnvoie": "2021-12-12T19:10:50Z", "dateReglement": "2022-04-10T00:52:44Z", "statut": "EC"}, {"id": 236, "numFacture": 7591, "mntFacture": 432547, "dateFacture": "2022-03-29T10:13:14Z", "ventes": [12, 2], "dateEnvoie": "2022-02-20T18:19:05Z", "dateReglement": "2021-12-24T22:00:11Z", "statut": "EC"}, {"id": 237, "numFacture": 1643, "mntFacture": 153749, "dateFacture": "2022-01-23T20:26:46Z", "ventes": [42, 32], "dateEnvoie": "2022-03-16T19:25:05Z", "dateReglement": "2021-11-28T18:21:53Z", "statut": "AN"}, {"id": 238, "numFacture": 2103, "mntFacture": 242045, "dateFacture": "2022-03-02T15:15:29Z", "ventes": [42, 52], "dateEnvoie": "2021-10-06T18:57:54Z", "dateReglement": "2021-07-12T02:34:26Z", "statut": "AN"}, {"id": 239, "numFacture": 1651, "mntFacture": 344874, "dateFacture": "2021-05-19T01:00:38Z", "ventes": [2, 42], "dateEnvoie": "2021-08-31T00:04:57Z", "dateReglement": "2022-05-06T08:57:40Z", "statut": "EN"}, {"id": 240, "numFacture": 2310, "mntFacture": 1403488, "dateFacture": "2021-07-24T11:23:30Z", "ventes": [62, 42], "dateEnvoie": "2021-10-21T19:28:32Z", "dateReglement": "2022-01-23T07:43:39Z", "statut": "EN"}, {"id": 241, "numFacture": 5082, "mntFacture": 433111, "dateFacture": "2022-03-15T08:13:22Z", "ventes": [12, 12], "dateEnvoie": "2022-05-18T16:26:02Z", "dateReglement": "2022-03-04T09:50:32Z", "statut": "EN"}, {"id": 242, "numFacture": 4277, "mntFacture": 107653, "dateFacture": "2022-03-31T09:55:27Z", "ventes": [32, 32], "dateEnvoie": "2022-03-15T10:39:20Z", "dateReglement": "2021-08-23T04:40:32Z", "statut": "RE"}, {"id": 243, "numFacture": 6848, "mntFacture": 1057533, "dateFacture": "2021-10-07T01:23:19Z", "ventes": [22, 92], "dateEnvoie": "2022-05-01T20:19:59Z", "dateReglement": "2021-08-31T20:41:12Z", "statut": "EC"}, {"id": 244, "numFacture": 3787, "mntFacture": 1235947, "dateFacture": "2021-10-16T10:37:37Z", "ventes": [2, 92], "dateEnvoie": "2021-07-18T21:23:48Z", "dateReglement": "2022-03-23T10:34:08Z", "statut": "AN"}, {"id": 245, "numFacture": 6206, "mntFacture": 1111299, "dateFacture": "2021-11-14T17:59:31Z", "ventes": [22, 42], "dateEnvoie": "2022-03-07T04:56:12Z", "dateReglement": "2021-10-14T11:37:47Z", "statut": "AN"}, {"id": 246, "numFacture": 9781, "mntFacture": 1181504, "dateFacture": "2022-01-20T06:03:57Z", "ventes": [92, 12], "dateEnvoie": "2021-07-05T18:21:24Z", "dateReglement": "2021-08-25T07:38:18Z", "statut": "EC"}, {"id": 247, "numFacture": 2227, "mntFacture": 1250275, "dateFacture": "2022-01-06T04:10:22Z", "ventes": [22, 12], "dateEnvoie": "2021-11-10T01:10:27Z", "dateReglement": "2021-10-09T20:45:39Z", "statut": "AN"}, {"id": 248, "numFacture": 4823, "mntFacture": 1117473, "dateFacture": "2021-06-13T17:25:12Z", "ventes": [2, 42], "dateEnvoie": "2021-10-13T02:50:19Z", "dateReglement": "2022-02-19T18:48:18Z", "statut": "EN"}, {"id": 249, "numFacture": 9228, "mntFacture": 1026003, "dateFacture": "2021-12-10T03:18:12Z", "ventes": [82, 52], "dateEnvoie": "2022-01-11T17:39:01Z", "dateReglement": "2021-06-13T05:23:49Z", "statut": "AN"}, {"id": 250, "numFacture": 1714, "mntFacture": 106540, "dateFacture": "2022-03-26T06:25:03Z", "ventes": [72, 92], "dateEnvoie": "2021-12-21T07:18:23Z", "dateReglement": "2022-05-10T03:24:21Z", "statut": "RE"}, {"id": 251, "numFacture": 9732, "mntFacture": 980373, "dateFacture": "2021-10-18T12:40:14Z", "ventes": [92, 82], "dateEnvoie": "2021-09-07T18:43:22Z", "dateReglement": "2021-06-20T15:17:39Z", "statut": "EN"}, {"id": 252, "numFacture": 8450, "mntFacture": 800671, "dateFacture": "2022-05-05T22:35:40Z", "ventes": [72, 62], "dateEnvoie": "2022-03-29T16:54:09Z", "dateReglement": "2021-12-30T08:10:30Z", "statut": "EC"}, {"id": 253, "numFacture": 3019, "mntFacture": 756195, "dateFacture": "2021-07-09T20:35:30Z", "ventes": [12, 2], "dateEnvoie": "2021-11-08T02:58:12Z", "dateReglement": "2022-04-24T18:54:08Z", "statut": "AN"}, {"id": 254, "numFacture": 2233, "mntFacture": 1363022, "dateFacture": "2021-09-17T14:52:33Z", "ventes": [12, 22], "dateEnvoie": "2021-08-13T13:16:16Z", "dateReglement": "2021-08-18T21:47:40Z", "statut": "RE"}, {"id": 255, "numFacture": 5211, "mntFacture": 526149, "dateFacture": "2021-08-27T20:41:31Z", "ventes": [62, 32], "dateEnvoie": "2021-11-20T04:24:31Z", "dateReglement": "2021-09-07T03:46:34Z", "statut": "EC"}, {"id": 256, "numFacture": 1862, "mntFacture": 1406300, "dateFacture": "2022-05-12T02:34:18Z", "ventes": [52, 22], "dateEnvoie": "2022-02-16T04:07:17Z", "dateReglement": "2021-12-01T00:48:53Z", "statut": "RE"}, {"id": 257, "numFacture": 6870, "mntFacture": 939984, "dateFacture": "2022-03-26T06:34:39Z", "ventes": [82, 52], "dateEnvoie": "2022-01-28T00:33:52Z", "dateReglement": "2021-07-15T03:48:24Z", "statut": "AN"}, {"id": 258, "numFacture": 6219, "mntFacture": 998920, "dateFacture": "2021-09-04T23:51:16Z", "ventes": [12, 52], "dateEnvoie": "2022-05-13T13:56:46Z", "dateReglement": "2021-10-02T10:52:56Z", "statut": "EC"}, {"id": 259, "numFacture": 7113, "mntFacture": 1331433, "dateFacture": "2022-03-14T03:03:37Z", "ventes": [32, 72], "dateEnvoie": "2021-11-30T18:59:33Z", "dateReglement": "2021-09-21T10:48:43Z", "statut": "EC"}, {"id": 260, "numFacture": 4937, "mntFacture": 320881, "dateFacture": "2022-02-04T13:13:47Z", "ventes": [32, 2], "dateEnvoie": "2022-01-27T18:54:33Z", "dateReglement": "2022-02-13T12:18:53Z", "statut": "AN"}, {"id": 261, "numFacture": 8746, "mntFacture": 834462, "dateFacture": "2022-01-28T18:43:43Z", "ventes": [62, 72], "dateEnvoie": "2021-07-04T01:23:51Z", "dateReglement": "2021-11-26T03:25:24Z", "statut": "EN"}, {"id": 262, "numFacture": 4076, "mntFacture": 1255388, "dateFacture": "2022-01-19T06:42:23Z", "ventes": [52, 72], "dateEnvoie": "2022-05-13T11:39:58Z", "dateReglement": "2021-09-26T15:39:40Z", "statut": "AN"}, {"id": 263, "numFacture": 6282, "mntFacture": 776765, "dateFacture": "2022-05-14T06:51:11Z", "ventes": [2, 92], "dateEnvoie": "2021-07-24T12:25:07Z", "dateReglement": "2021-09-15T00:28:49Z", "statut": "AN"}, {"id": 264, "numFacture": 8953, "mntFacture": 1227172, "dateFacture": "2021-05-29T10:05:33Z", "ventes": [52, 62], "dateEnvoie": "2021-09-10T00:26:08Z", "dateReglement": "2021-07-03T00:14:23Z", "statut": "AN"}, {"id": 265, "numFacture": 1315, "mntFacture": 1359794, "dateFacture": "2021-12-07T23:09:05Z", "ventes": [82, 22], "dateEnvoie": "2022-01-07T04:38:04Z", "dateReglement": "2021-12-26T13:06:40Z", "statut": "EN"}, {"id": 266, "numFacture": 9783, "mntFacture": 1395970, "dateFacture": "2021-11-04T02:46:39Z", "ventes": [2, 22], "dateEnvoie": "2022-05-17T07:06:33Z", "dateReglement": "2022-03-05T02:10:42Z", "statut": "EN"}, {"id": 267, "numFacture": 8748, "mntFacture": 1045576, "dateFacture": "2022-02-27T02:38:36Z", "ventes": [32, 42], "dateEnvoie": "2022-03-16T15:06:20Z", "dateReglement": "2021-12-03T09:07:35Z", "statut": "RE"}, {"id": 268, "numFacture": 1086, "mntFacture": 181586, "dateFacture": "2022-04-05T14:57:45Z", "ventes": [72, 32], "dateEnvoie": "2022-03-14T13:21:56Z", "dateReglement": "2021-07-31T21:22:12Z", "statut": "EC"}, {"id": 269, "numFacture": 9652, "mntFacture": 1405179, "dateFacture": "2021-08-14T18:53:15Z", "ventes": [42, 22], "dateEnvoie": "2021-07-04T05:43:47Z", "dateReglement": "2021-06-28T02:51:41Z", "statut": "AN"}, {"id": 270, "numFacture": 7213, "mntFacture": 460754, "dateFacture": "2022-02-26T16:33:29Z", "ventes": [12, 42], "dateEnvoie": "2021-12-11T10:30:20Z", "dateReglement": "2021-10-08T14:09:52Z", "statut": "EN"}, {"id": 271, "numFacture": 1654, "mntFacture": 436256, "dateFacture": "2021-06-20T08:49:45Z", "ventes": [52, 2], "dateEnvoie": "2021-10-01T08:16:10Z", "dateReglement": "2021-10-16T16:45:53Z", "statut": "EC"}, {"id": 272, "numFacture": 9781, "mntFacture": 1168395, "dateFacture": "2022-03-16T21:26:50Z", "ventes": [2, 32], "dateEnvoie": "2021-12-13T02:11:51Z", "dateReglement": "2021-07-08T14:58:36Z", "statut": "AN"}, {"id": 273, "numFacture": 6302, "mntFacture": 248958, "dateFacture": "2021-11-08T09:05:35Z", "ventes": [62, 92], "dateEnvoie": "2021-08-11T22:10:45Z", "dateReglement": "2022-04-13T02:21:25Z", "statut": "EN"}, {"id": 274, "numFacture": 1194, "mntFacture": 757219, "dateFacture": "2022-05-01T10:48:30Z", "ventes": [82, 32], "dateEnvoie": "2021-11-20T11:25:10Z", "dateReglement": "2022-03-01T03:09:46Z", "statut": "EN"}, {"id": 275, "numFacture": 3411, "mntFacture": 41174, "dateFacture": "2022-03-07T06:49:37Z", "ventes": [12, 32], "dateEnvoie": "2021-12-01T12:34:16Z", "dateReglement": "2021-11-30T10:56:37Z", "statut": "EN"}, {"id": 276, "numFacture": 6746, "mntFacture": 1456007, "dateFacture": "2021-11-26T01:35:39Z", "ventes": [12, 42], "dateEnvoie": "2022-03-03T10:00:33Z", "dateReglement": "2021-12-18T00:17:06Z", "statut": "EN"}, {"id": 277, "numFacture": 3811, "mntFacture": 355156, "dateFacture": "2022-03-24T15:45:22Z", "ventes": [62, 32], "dateEnvoie": "2021-05-27T00:13:13Z", "dateReglement": "2021-07-16T21:54:25Z", "statut": "RE"}, {"id": 278, "numFacture": 5662, "mntFacture": 246061, "dateFacture": "2021-12-18T06:40:29Z", "ventes": [22, 12], "dateEnvoie": "2022-01-30T12:03:17Z", "dateReglement": "2022-02-11T15:59:13Z", "statut": "RE"}, {"id": 279, "numFacture": 6918, "mntFacture": 102864, "dateFacture": "2021-11-19T16:21:30Z", "ventes": [62, 92], "dateEnvoie": "2022-01-16T23:35:45Z", "dateReglement": "2021-11-23T22:44:12Z", "statut": "EC"}, {"id": 280, "numFacture": 7150, "mntFacture": 1317435, "dateFacture": "2022-04-02T13:12:59Z", "ventes": [32, 42], "dateEnvoie": "2021-10-09T10:24:41Z", "dateReglement": "2022-04-24T15:11:32Z", "statut": "RE"}, {"id": 281, "numFacture": 1985, "mntFacture": 823884, "dateFacture": "2022-01-07T09:07:00Z", "ventes": [52, 52], "dateEnvoie": "2022-03-20T09:53:59Z", "dateReglement": "2021-05-20T08:08:29Z", "statut": "AN"}, {"id": 282, "numFacture": 6503, "mntFacture": 362162, "dateFacture": "2021-11-10T13:11:31Z", "ventes": [82, 22], "dateEnvoie": "2021-08-18T21:51:52Z", "dateReglement": "2022-03-07T18:58:32Z", "statut": "EC"}, {"id": 283, "numFacture": 5697, "mntFacture": 33993, "dateFacture": "2021-10-04T09:03:04Z", "ventes": [62, 2], "dateEnvoie": "2022-05-13T10:44:23Z", "dateReglement": "2021-11-20T08:09:02Z", "statut": "AN"}, {"id": 284, "numFacture": 1238, "mntFacture": 1290167, "dateFacture": "2022-04-07T23:12:46Z", "ventes": [92, 32], "dateEnvoie": "2022-04-02T03:27:59Z", "dateReglement": "2021-09-01T00:36:22Z", "statut": "EN"}, {"id": 285, "numFacture": 2980, "mntFacture": 475860, "dateFacture": "2021-10-17T01:50:21Z", "ventes": [12, 2], "dateEnvoie": "2022-05-14T06:40:50Z", "dateReglement": "2021-06-28T09:13:46Z", "statut": "EC"}, {"id": 286, "numFacture": 7146, "mntFacture": 1230000, "dateFacture": "2021-11-01T22:01:37Z", "ventes": [82, 52], "dateEnvoie": "2021-10-14T19:35:03Z", "dateReglement": "2022-04-29T19:26:01Z", "statut": "EN"}, {"id": 287, "numFacture": 6035, "mntFacture": 630243, "dateFacture": "2021-08-15T14:31:44Z", "ventes": [12, 2], "dateEnvoie": "2022-03-06T18:35:38Z", "dateReglement": "2021-08-12T12:44:39Z", "statut": "EC"}, {"id": 288, "numFacture": 6373, "mntFacture": 1222123, "dateFacture": "2021-07-06T11:58:04Z", "ventes": [32, 2], "dateEnvoie": "2021-12-25T03:07:44Z", "dateReglement": "2021-11-08T23:14:00Z", "statut": "RE"}, {"id": 289, "numFacture": 1698, "mntFacture": 282706, "dateFacture": "2021-12-12T09:25:25Z", "ventes": [92, 82], "dateEnvoie": "2022-01-20T07:25:34Z", "dateReglement": "2021-09-21T15:33:23Z", "statut": "EC"}, {"id": 290, "numFacture": 4904, "mntFacture": 463670, "dateFacture": "2022-03-04T02:12:00Z", "ventes": [42, 22], "dateEnvoie": "2022-01-04T09:40:24Z", "dateReglement": "2021-12-18T13:24:02Z", "statut": "AN"}, {"id": 291, "numFacture": 6823, "mntFacture": 1046756, "dateFacture": "2021-06-29T13:28:58Z", "ventes": [32, 92], "dateEnvoie": "2021-12-09T02:10:47Z", "dateReglement": "2022-01-04T01:41:19Z", "statut": "RE"}, {"id": 292, "numFacture": 7064, "mntFacture": 1497912, "dateFacture": "2022-03-14T04:50:13Z", "ventes": [82, 12], "dateEnvoie": "2021-11-07T00:56:05Z", "dateReglement": "2021-07-20T17:39:34Z", "statut": "AN"}, {"id": 293, "numFacture": 7513, "mntFacture": 1340206, "dateFacture": "2022-05-12T12:20:53Z", "ventes": [2, 62], "dateEnvoie": "2022-02-02T02:08:26Z", "dateReglement": "2021-07-04T06:36:48Z", "statut": "EC"}, {"id": 294, "numFacture": 2292, "mntFacture": 122312, "dateFacture": "2022-04-04T22:34:55Z", "ventes": [82, 52], "dateEnvoie": "2021-05-29T08:04:19Z", "dateReglement": "2021-08-22T14:43:55Z", "statut": "EC"}, {"id": 295, "numFacture": 2487, "mntFacture": 79613, "dateFacture": "2021-09-26T20:17:50Z", "ventes": [62, 72], "dateEnvoie": "2021-10-16T02:46:29Z", "dateReglement": "2022-02-17T07:09:05Z", "statut": "AN"}, {"id": 296, "numFacture": 9670, "mntFacture": 704670, "dateFacture": "2021-07-27T15:58:17Z", "ventes": [32, 52], "dateEnvoie": "2021-07-04T03:40:18Z", "dateReglement": "2021-12-04T15:46:49Z", "statut": "RE"}, {"id": 297, "numFacture": 8951, "mntFacture": 283883, "dateFacture": "2022-02-10T09:06:08Z", "ventes": [82, 72], "dateEnvoie": "2022-05-12T06:30:08Z", "dateReglement": "2022-04-09T15:53:38Z", "statut": "EC"}, {"id": 298, "numFacture": 3181, "mntFacture": 347915, "dateFacture": "2022-04-16T11:33:05Z", "ventes": [92, 82], "dateEnvoie": "2022-02-21T00:13:07Z", "dateReglement": "2021-11-16T20:07:45Z", "statut": "RE"}, {"id": 299, "numFacture": 4750, "mntFacture": 615755, "dateFacture": "2021-08-23T06:48:37Z", "ventes": [62, 72], "dateEnvoie": "2021-07-10T08:11:04Z", "dateReglement": "2021-09-05T18:46:07Z", "statut": "EC"}, {"id": 300, "numFacture": 2935, "mntFacture": 246187, "dateFacture": "2021-07-18T10:05:27Z", "ventes": [82, 72], "dateEnvoie": "2021-11-08T23:54:11Z", "dateReglement": "2021-05-19T18:06:05Z", "statut": "EN"}]}