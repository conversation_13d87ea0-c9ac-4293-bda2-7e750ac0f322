
export interface MenuItem {
    id?: number;
    key?: string;
    label?: string;
    icon?: string;
    style?: string;
    link?: string;
    collapsed?: boolean;
    children?: MenuItem[];
    isTitle?: boolean;
    badge?: any;
    parentKey?: string;
    authorities?: any;

    hideInEnvironment?: string | string[] ///  hide in a specific env (whitout like creating a new profil  for diff env)

}
