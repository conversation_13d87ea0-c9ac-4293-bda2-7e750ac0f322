import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';
import { DrawerComponent } from './drawer.component';

describe('DrawerComponent', () => {
  let component: DrawerComponent;
  let fixture: ComponentFixture<DrawerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DrawerComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DrawerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default values', () => {
    expect(component.isOpen).toBe(false);
    expect(component.position).toBe('right');
    expect(component.size).toBe('medium');
    expect(component.showCloseButton).toBe(true);
    expect(component.closeOnBackdropClick).toBe(true);
    expect(component.closeOnEscape).toBe(true);
  });

  it('should apply correct CSS classes', () => {
    component.isOpen = true;
    component.position = 'left';
    component.size = 'large';
    fixture.detectChanges();

    expect(component.drawerClasses).toContain('drawer');
    expect(component.drawerClasses).toContain('drawer--left');
    expect(component.drawerClasses).toContain('drawer--large');
    expect(component.drawerClasses).toContain('drawer--open');
  });

  it('should emit openChange when opened', () => {
    spyOn(component.openChange, 'emit');
    
    component.open();
    
    expect(component.openChange.emit).toHaveBeenCalledWith(true);
    expect(component.isOpen).toBe(true);
  });

  it('should emit openChange when closed', () => {
    component.isOpen = true;
    spyOn(component.openChange, 'emit');
    
    component.close();
    
    expect(component.openChange.emit).toHaveBeenCalledWith(false);
    expect(component.isOpen).toBe(false);
  });

  it('should toggle open state', () => {
    expect(component.isOpen).toBe(false);
    
    component.toggle();
    expect(component.isOpen).toBe(true);
    
    component.toggle();
    expect(component.isOpen).toBe(false);
  });

  it('should have proper ARIA attributes when open', () => {
    component.isOpen = true;
    component.title = 'Test Drawer';
    fixture.detectChanges();

    const drawerElement = fixture.debugElement.query(By.css('.drawer'));
    
    expect(drawerElement.nativeElement.getAttribute('role')).toBe('dialog');
    expect(drawerElement.nativeElement.getAttribute('aria-modal')).toBe('true');
    expect(drawerElement.nativeElement.getAttribute('aria-hidden')).toBe('false');
  });

  it('should have proper ARIA attributes when closed', () => {
    component.isOpen = false;
    fixture.detectChanges();

    const drawerElement = fixture.debugElement.query(By.css('.drawer'));
    
    expect(drawerElement.nativeElement.getAttribute('aria-hidden')).toBe('true');
  });

  it('should show backdrop when showBackdrop is true and drawer is open', () => {
    component.isOpen = true;
    component.showBackdrop = true;
    fixture.detectChanges();

    const backdrop = fixture.debugElement.query(By.css('.drawer-backdrop'));
    expect(backdrop).toBeTruthy();
  });

  it('should not show backdrop when showBackdrop is false', () => {
    component.isOpen = true;
    component.showBackdrop = false;
    fixture.detectChanges();

    const backdrop = fixture.debugElement.query(By.css('.drawer-backdrop'));
    expect(backdrop).toBeFalsy();
  });

  it('should show close button when showCloseButton is true', () => {
    component.isOpen = true;
    component.showCloseButton = true;
    component.title = 'Test';
    fixture.detectChanges();

    const closeButton = fixture.debugElement.query(By.css('.drawer__close-button'));
    expect(closeButton).toBeTruthy();
  });

  it('should close drawer when close button is clicked', () => {
    component.isOpen = true;
    component.showCloseButton = true;
    component.title = 'Test';
    fixture.detectChanges();

    const closeButton = fixture.debugElement.query(By.css('.drawer__close-button'));
    closeButton.nativeElement.click();

    expect(component.isOpen).toBe(false);
  });

  it('should close drawer when backdrop is clicked and closeOnBackdropClick is true', () => {
    component.isOpen = true;
    component.closeOnBackdropClick = true;
    fixture.detectChanges();

    const backdrop = fixture.debugElement.query(By.css('.drawer-backdrop'));
    backdrop.nativeElement.click();

    expect(component.isOpen).toBe(false);
  });

  it('should not close drawer when backdrop is clicked and closeOnBackdropClick is false', () => {
    component.isOpen = true;
    component.closeOnBackdropClick = false;
    fixture.detectChanges();

    const backdrop = fixture.debugElement.query(By.css('.drawer-backdrop'));
    backdrop.nativeElement.click();

    expect(component.isOpen).toBe(true);
  });

  it('should generate unique IDs', () => {
    const component1 = new DrawerComponent(null as any, null as any, null as any);
    const component2 = new DrawerComponent(null as any, null as any, null as any);
    
    expect(component1.drawerId).not.toBe(component2.drawerId);
    expect(component1.drawerLabelId).not.toBe(component2.drawerLabelId);
  });

  it('should use title for aria-label when no custom aria-label is provided', () => {
    component.title = 'My Drawer';
    component.ariaLabel = '';
    
    expect(component.ariaLabelValue).toBe('My Drawer');
  });

  it('should use custom aria-label when provided', () => {
    component.title = 'My Drawer';
    component.ariaLabel = 'Custom Label';
    
    expect(component.ariaLabelValue).toBe('Custom Label');
  });

  it('should use default label when no title or aria-label is provided', () => {
    component.title = '';
    component.ariaLabel = '';
    
    expect(component.ariaLabelValue).toBe('Drawer');
  });
});
