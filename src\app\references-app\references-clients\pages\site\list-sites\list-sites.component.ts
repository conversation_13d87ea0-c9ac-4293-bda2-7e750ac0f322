import { Navigation } from 'swiper/core';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { AlertService } from 'src/app/shared/services/alert.service';
import { map } from 'rxjs/operators';
import { WinClientRegion } from '../../../models/region';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { WinClientSite } from '../../../models/sites';
import { WinclientSitesService } from '../../../Services/winclient.sites.service';
import { WinclientRegionService } from '../../../Services/winclient.region.service';
import { WinClientVille } from '../../../models/ville';
import { filterBy } from '@progress/kendo-data-query';

@Component({
  selector: 'app-list-sites',
  templateUrl: './list-sites.component.html',
  styleUrls: ['./list-sites.component.scss']
})
export class ListSitesComponent implements OnInit {


  @ViewChild('updateOrCreateVilleModal') updateOrCreateVilleModal: TemplateRef<any>;

  modalMode : "EDIT" | "CREATE" = "CREATE";
  submited = false;
  currentEditingVille:any;
  modalRef : NgbModalRef;
  regions:WinClientRegion[];
  

  navigation : Pagination = {
    pageSize :21,
    skip:0,
  }

  sites:WinClientSite[] = [];
  siteFiltred:WinClientSite[] = [];

  siteForm = new FormGroup({
    id: new FormControl(null),
    libelleCourt: new FormControl('', [Validators.required,Validators.maxLength(30)]),
    libelleLong: new FormControl('', [Validators.required,Validators.maxLength(30)]),
    region: new FormControl(null,[Validators.required]),
    visible: new FormControl(true),
  });

  
  constructor(
    private modalService: NgbModal,
    private userInputService:UserInputService,
    private sitesService:WinclientSitesService,
    private alertService:AlertService,
    private regionService:WinclientRegionService,
  ) { }

  ngOnInit() {
    this.getListSites();
    this.getRegions();
  }

  getListSites(){
    this.sitesService.getAllSites().pipe(
      map(res => res.map((site)=>({...site,visible:site.visible === 'O' ? true: false})))).subscribe(res => {
      this.sites = res as unknown as WinClientSite[];
      this.siteFiltred = res as unknown as WinClientSite[];
      this.navigation.skip = 0;
      // this.sites.total = res.length;
    });
 
  }

  getRegions(){
    this.regionService.getAllRegionPaginated({page:0}).subscribe(res => {
      this.regions = res.content;
    });
  }


  


  openCreateVille() {
    this.modalMode = "CREATE";
    this.submited = false;
    this.modalRef = this.modalService.open(this.updateOrCreateVilleModal, { size: 'lg' });
    this.siteForm.reset({visible:true});
  }


  openEditVille(site:WinClientVille){
    this.modalMode = "EDIT";
    this.siteForm.patchValue(site);
    this.modalRef = this.modalService.open(this.updateOrCreateVilleModal, { size: 'lg' });
  }


   formErrors(field:string){
    return this.siteForm.controls[field];
  }


  confirmDeleteSite(site:any){
    this.userInputService.confirm("Confirmer la suppression du site ?","Voulez-vous vraiment supprimer ce site ?","Supprimer","Annuler").then(result => {
      if(result){
        this.deleteVille(site);
      }
    }).catch(() => { });
  }

  saveOrUpdateSite(){
    this.submited = true;
    if(this.siteForm.invalid){
      this.alertService.error("Veuillez remplir tous les champs");
      return;
    }


    const siteId = this.siteForm.value.id;

    const winclientSite = new WinClientSite({
      libelleCourt : this.siteForm.value.libelleCourt,
      libelleLong : this.siteForm.value.libelleLong,
      region : this.siteForm.value.region,
      visible : this.siteForm.value.visible,
    });

    if(this.modalMode == "EDIT"){
      this.updateSite(siteId,winclientSite);
    }else{
      this.createSite(winclientSite);
    }
    this.modalRef?.close();
  }

  pageChange(skip: number){
    this.navigation.skip =skip;
    const page = Math.ceil(skip / this.navigation.pageSize);
  }


  private deleteVille(id:number){
      this.sitesService.deleteSite(id).subscribe(res => {
        this.alertService.success("Le site a été supprimée avec succès");
        this.getListSites();
      });
  }

  private updateSite(id:number,site:Omit<WinClientSite, "id">){
    const payload = new WinClientSite({
       libelleCourt : site.libelleCourt,
       libelleLong : site.libelleLong,
       region : site.region,
       visible : site.visible ? 'O' : 'N',
    });

    this.sitesService.updateSite(id,payload).subscribe(res => {
      this.getListSites();
      this.alertService.success("Le site a été modifiée avec succès");
    });
  }

  private createSite(site:WinClientSite){

    const payload = new WinClientSite({
      libelleCourt : site.libelleCourt,
      libelleLong : site.libelleLong,
      region: site.region,
      visible : site.visible ? 'O' : 'N',
    });
    
    this.sitesService.createSite(payload).subscribe(res => {
      this.getListSites();
    });
  }

  compareFN(o1: WinClientRegion, o2: WinClientRegion): boolean {
    return o1 && o2 && o1.id === o2.id || o1 == o2;
  }

  filterSites(event: any) {
    const searchTerm = event.target.value?.trim().toLowerCase();
    this.navigation.skip = 0;
    const result = filterBy(this.siteFiltred,{
      logic: 'or',
      filters: [
        { field: 'libelleCourt', operator: 'contains', value: searchTerm },
        { field: 'libelleLong', operator: 'contains', value: searchTerm },
        { field: 'region.libRegion', operator: 'contains', value: searchTerm },
      ]
    });
    this.sites = result as WinClientSite[];
  }

  

}      

