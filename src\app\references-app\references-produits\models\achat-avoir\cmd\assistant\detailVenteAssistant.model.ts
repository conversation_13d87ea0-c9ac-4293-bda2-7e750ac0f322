// import { Moment } from 'moment';

import { Stock } from 'src/app/references-app/references-produits/models/produit/stock/stock.model';
import { Produit } from '../../../produit/base/produit.model';


export class DetailVenteAssistant {
    audited?: boolean;
    // dateCmd?: any;
    // nbrVenteJour?: number;
    // prixAchatStd?: number;
    // prixVenteStd?: number;
    produit?: Produit;
    // qtAchatRecu?: number;
    // qtCmd?: number;
    // qtEntree?: number;
    // qtVente?: number;
    seuilStkMax?: number;
    seuilStkMin?: number;
    // stock?: Stock;
    // totalStock?: number;
    ///

    moyenneConsomJr?: number
    qteCmd: number
    qteProp: number
    qteStock: number
    qteVenteJournee: number
    qteVenteJrnAfterLastCmd: number


    userModifiable?: boolean;
}
