import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { Page } from '../../referential/models/Page/page.model';
import { WinClientRegion } from '../models/region';

@Injectable({
  providedIn: 'root'
})
export class WinclientRegionService {

  constructor(private http: HttpClient) { }

  getAllRegionPaginated({page =0}:{page:number}) {
    return this.http.get<Page<WinClientRegion>>(`${env.winclient_base_url}/api/winclient/regions`, { 
        params:{page}
     });
  }

  getRegionById(id: number) {
    return this.http.get<WinClientRegion>(`${env.winclient_base_url}/api/winclient/regions/${id}`);
  }

  createRegion(region: WinClientRegion) {
    return this.http.post<WinClientRegion>(`${env.winclient_base_url}/api/winclient/regions`, region);
  }

  updateRegion(id:string,region: WinClientRegion) {
    return this.http.put<WinClientRegion>(`${env.winclient_base_url}/api/winclient/regions/${id}`, region);
  }

  deleteRegion(id: string) {
    return this.http.delete<WinClientRegion>(`${env.winclient_base_url}/api/winclient/regions/${id}`);
  }

}