import { Statut } from "src/app/references-app/references-produits/enums/common/Statut.enum"
import { Operateur } from "../../common/operateur.model"
import { Client } from "./client.model"
import { TypeOperationCompteClient } from "./compteClient.model"



export class CompteClientCriteria {
    client?: Client
    dateDebut?: string
    dateFin?: string
    isArchive?: boolean
    libelle?: string
    // typeOperation?: TypeOperationCompteClient
    operateur?: Operateur
    statut?: Statut

    listTypesOperations?: TypeOperationCompteClient[]
}