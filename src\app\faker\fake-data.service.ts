import {Injectable} from '@angular/core';
import {fakeData} from './data/data.home';
import * as moment from 'moment';
import {filteredData, sortedData} from "../shared/utils/arrayOperations.utils";
import {cleanSearchObject} from "../shared/utils/objectOperations.utils";

@Injectable({
    providedIn: 'root'
})
export class FakeDataService {
    data: any[] = [];

    constructor() {
        this.setData();
    }

    async setData() {
        this.data = fakeData as any;
    }

    login(body) {
        return {
            "accessToken": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImF1dGgiOiJST0xFX1NVUEVSQURNSU4iLCJleHAiOjE2NDYxNzIxMzJ9.M-VRXbsEk4hkMamsHKv2VoYwcNlje3uztNr_6wMhRMbtNgpvTsz9P7rP6pDNd4UVYZ4PHmIDncZVz5LyJmf6hQ",
            "idhash": "2",
            "lastname": "admin L",
            "firstname": "admin F",
            "authorities": [
                "ROLE_SUPERADMIN"
            ],
            "logo": null
        }
    }

    getSubData(keys, fullData) {
        let data = fullData;
        for (const key of keys) {
            if (data) {
                data = data[key];
            } else {
                return null;
            }
        }
        return data;
    }

    getDataList(mainKeys: string[], params, body?: object) {
        const pageNumber = parseInt(params.get('page'), null);
        const sizeNumber = parseInt(params.get('size'), null);
        const field = params.get('field');
        const dir = params.get('dir');
        const isPaginated = (pageNumber || pageNumber === 0) && sizeNumber;
        const pathKeys = mainKeys.slice(0, mainKeys.length - 1)
        const populatedData = this.populatedData(pathKeys, this.getSubData(mainKeys, this.data));
        const dataAfterSort = (dir && field) ? sortedData(populatedData, field, dir) : populatedData;

        const filteredPopulatedData = body ? filteredData(dataAfterSort, cleanSearchObject(body)) : dataAfterSort;


        if (isPaginated) {
            return {
                content: this.getDataSection(filteredPopulatedData, pageNumber, sizeNumber),
                totalElements: filteredPopulatedData.length
            };
        }
        return filteredPopulatedData;
        return [];
    }

    getDataItem(mainKeys: string[], id: string) {
        const pathKeys = mainKeys.slice(0, mainKeys.length - 1);
        const subData = this.getSubData(pathKeys, this.data);
        if (subData) {
            const object = subData.find(item => item.id === parseInt(id));
            if (object) {
                const populatedDataItem = this.populatedObject(pathKeys, object);
                return populatedDataItem;
            }
        }
        return null;
    }

    addItemToList(mainKeys: string[], body: any) {
        const wantedData = this.getSubData(mainKeys, this.data);
        let result = null;
        const savedBody = this.cleanBody(body);
        if (!body?.id) {
            const lastItemId = wantedData[wantedData.length - 1]?.id;
            body.id = lastItemId + 1;
            savedBody.id = lastItemId + 1;
            wantedData.push(savedBody);
        } else {
            const itemIndex = wantedData.findIndex(item => item.id === savedBody.id);
            wantedData[itemIndex] = {...wantedData[itemIndex], ...savedBody};
            result = wantedData[itemIndex];
        }

        return result;
    }

    cleanBody(body: any) {
        body = JSON.parse(JSON.stringify(body));
        // body = bo
        for (const key of Object.keys(body)) {
            if (body[key] && typeof body[key] === 'object' && body[key].id) {
                body[key] = body[key].id;
            }
        }
        return body;
    }





    getDataSection(list: any[], page: number, size: number) {
        return list.slice(page * size, (page + 1) * size);
    }

    populatedData(mainKeys: string[], rawData: any[]) {
        if (rawData && rawData.length > 0) {
            for (let i in rawData) {
                rawData[i] = this.populatedObject(mainKeys, rawData[i]);
            }
            return rawData;
        }
        return [];
    }


    getRawDataBeta(currentData, pathKeys: string[] | number[], key) {

        if (!currentData) return null;
        if (currentData[key]) return currentData[key];
        if (currentData?.config && currentData?.config[key]) return currentData?.config[key];
        return this.getRawDataBeta(currentData[pathKeys[0]], pathKeys.slice(1).length > 0 ? pathKeys.slice(1) : [0], key);
    }

    populatedObject(mainKeys: string[], object, depth = 3) {
        if (depth <= 0) return object;
        const objectKeys = Object.keys(object);
        for (const key of objectKeys) {
            const keyItems: any = this.getRawDataBeta(this.data, mainKeys, key + 's');
            const arrayKeyItems: any = this.getRawDataBeta(this.data, mainKeys, key);
            if (keyItems) {
                const result = keyItems.find(item => item.id === object[key]);
                object[key] = result ? this.populatedObject(mainKeys, result, depth - 1) : object[key];
            } else if (arrayKeyItems && object[key] && Array.isArray(object[key]) && object[key].length > 0) {
                for (let i in object[key]) {
                    const result = arrayKeyItems.find(item => item.id === object[key][i]);
                    object[key][i] = result ? this.populatedObject(mainKeys, result, depth - 1) : object[key][i];
                }
            }
        }
        return object;
    }


}


