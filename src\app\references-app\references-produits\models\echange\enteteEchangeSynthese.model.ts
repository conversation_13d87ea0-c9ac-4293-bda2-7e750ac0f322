import { SensEchange } from 'src/app/references-app/references-produits/enums/echange/SensEchange.enum';
import { TypePrixEchange } from 'src/app/references-app/references-produits/enums/echange/TypePrixEchange.enum';

import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';


export class EnteteEchangeSynthese { 
    audited?: boolean;
    ft?: FamilleTarifaire;
    mntEchange?: number;
    sensEchange?: SensEchange;
    typePrix?: TypePrixEchange;
    userModifiable?: boolean;
}

