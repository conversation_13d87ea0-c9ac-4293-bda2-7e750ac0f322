<div class="bg-white px-2" style="height: calc(100vh - 55px);margin-left:-5px;">
  <div class="card card body text-center p-1 border-0 mb-3">
    <h1 class="m-0 text-black">Bienvenue</h1>
    <p class="m-0 text-black">Groupement sites(Groupe UGP).</p>
</div>


<div class="row">
  <div class="col-md-6">
    <div class="card card-body py-1 px-2 bg-white  shadow-lg  flex-row k-gap-2 align-content-center"
     style="border-radius: 10px;align-items: center; border: 1px solid #dfdfdf !important;">
      <i class="mdi mdi-account-switch k-text-success" style="font-size:46px !important;"></i>
      <h3 class="m-0 text-black fs-3" style="line-height: 1;" >
        Groupement <br/>
       <span class="text-muted fs-5"> Codification Client</span>
      </h3>
    </div>
    <div class="row">
      <div class="col-md-6">
          <div class="dashboard-card stat-card secondary-card">
              <i class="mdi mdi-chart-line mdi-36px mb-3 text-danger" style="color: var(--primary-color)"></i>
                <div *ngIf="!isLoading" class="stat-value">{{winclientStats?.pourcentageAvecCodeGroupe | number:'1.2-2' }}%</div>
                <div *ngIf="isLoading" class="skeleton-loading-block" ></div>
              <div class="stat-label">Groupement</div>
          </div>
      </div>
      <div class="col-md-6">
          <div class="dashboard-card stat-card primary-card">
              <i class="mdi mdi-account-multiple mdi-36px mb-3 text-primary" style="color: var(--secondary-color)"></i>
              <div *ngIf="!isLoading" class="stat-value">{{winclientStats?.clientsSansCodeGroupe}}</div>
              <div *ngIf="isLoading" class="skeleton-loading-block" ></div>
              <div class="stat-label">Clients Restants</div>
          </div>
      </div>
      <div class="col-md-6">
          <div class="dashboard-card stat-card warning-card ">
              <i class="mdi mdi-account-remove mdi-36px mb-3 text-warning" style="color: var(--warning-color)"></i>
              <div *ngIf="!isLoading" class="stat-value">{{winclientStats?.clientsSignales}}</div>
              <div *ngIf="isLoading" class="skeleton-loading-block" ></div>
              <div class="stat-label">Clients Signalés</div>
          </div>
      </div>
      <div class="col-md-6">
          <div class="dashboard-card stat-card success-card">
              <i class="mdi mdi-checkbox-multiple-marked-circle-outline  mdi-36px mb-3 text-success" style="color: var(--warning-color)"></i>
              <div *ngIf="!isLoading" class="stat-value">0.00%</div>
              <div *ngIf="isLoading" class="skeleton-loading-block" ></div>
              <div class="stat-label">Vérification</div>
          </div>
      </div>
  </div>
  </div>
  
  <div class="row col-md-6">
    <div class="card card-body border-0  py-1 px-2  bg-white shadow-lg flex-row k-gap-2 align-items-center"  
    style="border-radius: 10px;align-items: center; border: 1px solid #dfdfdf !important;">
    <div>
      <i class="mdi mdi-update k-text-primary"  style="font-size:46px !important;"></i>
      <h3 class="m-0 text-black fs-3" style="line-height: 1;" >
        Mise à jour <br/>
       <span class="text-muted fs-5">Monitoring extraction</span>
      </h3>
    </div>
    </div>
    <div class="col-12 card card-body p-0 border-0" style="overflow: hidden; border-radius: 10px;">
      <kendo-grid [kendoGridBinding]="sitesMajStats" class="ref-grid" [loading]="isLoading" [height]="500">
        <kendo-grid-column field="libelleLong" title="Site"></kendo-grid-column>
        <kendo-grid-column field="derniereMaj" title="Date Maj Client">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span *ngIf="dataItem.derniereMaj" style="color:#239500;">{{dataItem.derniereMaj | date:'dd/MM/yyyy'}}</span>
            <span *ngIf="!dataItem.derniereMaj" class="text-danger">non disponible</span>
          </ng-template>
        </kendo-grid-column>
        <!-- <kendo-grid-column field="dateMajCommercial" title="Date Maj Commercial"></kendo-grid-column> -->
      </kendo-grid>
    </div>
  </div>
</div>
</div>