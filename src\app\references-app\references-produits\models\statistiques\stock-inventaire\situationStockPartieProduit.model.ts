// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { FournisseurAbrege } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseurAbrege.model';
import { Rayon } from 'src/app/references-app/references-produits/models/produit/base/rayon.model';
import { Taxe } from 'src/app/references-app/references-produits/models/common/taxe.model';


export class SituationStockPartieProduit { 
    categorie?: CategorieProduit;
    codeProduit?: string;
    dateDerniereLivraison?: any;
    dateDerniereVente?: any;
    datePeremption?: string;
    designationProduit?: string;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    idProduit?: number;
    laboratoire?: FournisseurAbrege;
    prixAchatUnit?: number;
    prixVenteUnit?: number;
    rayon?: Rayon;
    seuilStockMax?: number;
    seuilStockMin?: number;
    tva?: Taxe;
}
