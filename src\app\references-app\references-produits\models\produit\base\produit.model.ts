// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { Dci } from 'src/app/references-app/references-produits/models/produit/medical/dci.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { ProduitBase } from './produitBase.model';
import { ProduitCodebarre } from './produitCodebarre.model';
import { ProduitParametre } from './produitParametre.model';
import { Taxe } from 'src/app/references-app/references-produits/models/common/taxe.model';
import { Operateur } from '../../common/operateur.model';
import { TypeSuggestionEnum } from 'src/app/references-app/references-produits/enums/produit/type-suggestion.enum';
import { Ville } from '../../common/ville.model';


export class Produit {
    audited?: boolean;
    categorie?: CategorieProduit;
    codeBarre?: string;
    codeBarres?: ProduitCodebarre[];
    codePrd?: string;
    codeGroupe?: string;
    dateFinCom?: any;
    dci?: Dci;
    designation?: string;
    dosage?: string;
    estFabrique?: boolean;
    estMarche?: boolean;
    estOblgPrescription?: boolean;
    estPrinceps?: boolean;
    estPrixmarque?: boolean;
    estPsychotrope?: boolean;
    estRbrsblBase?: boolean;
    estStockable?: boolean;
    estToxique?: boolean;
    estTpaBase?: boolean;
    estVendable?: boolean;
    familleTarifaire?: FamilleTarifaire;
    formeGalenique?: FormeProduit;
    id?: number;
    idhash?: string;
    laboratoire?: Fournisseur;
    nomRacine?: string;
    pbrH?: number;
    pbrP?: number;
    presentation?: string;
    prixAchatStd?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixVenteStd?: number;
    produitBase?: ProduitBase;
    produitParametre?: ProduitParametre;
    tauxRemb?: number;
    totalStock?: number;
    tva?: Taxe;
    typeProcess?: string;
    userModifiable?: boolean;

    pphTtc?: number




    // TODO: HTO use this
    userCreation?: Operateur;
    userValidation?: Operateur;
    userAnnulation?: Operateur;
    dateValidation?: any;
    dateAnnulation?: any;

    /*********** suggestion *************/

    isSuggestion?: boolean;
    typeSuggestion?: TypeSuggestionEnum;
    motifAnnulSuggestion?: string;
    suggestionPrdId?: number;
    suggestionCodePrd?: string;

    suggestionTenantCode?: string;
    suggestionTenantRaisonSociale?: string;
    suggestionTenantVille?: Ville;

    /************************************/



    constructor() {

    }
}
