{"info": {"_postman_id": "0ff9d593-68a6-4688-983a-691dba4c158d", "name": "Authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "32602638"}, "item": [{"name": "Login<PERSON>enant", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["\r", "pm.test(\"setEnvironmentVariable Tenant Token\", function () {\r", "    var jsonData = pm.response.json();\r", "     const  bodyToken=jsonData.accessToken\r", "    pm.globals.set(\"tokenTenant\", \"BearerTenant \"+bodyToken);\r", "});\r", "\r", "\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\":\"{{defaultTenantUsername}}\",\r\n    \"password\":\"{{defaultTenantPassword}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/user/auth-tenant", "host": ["{{baseUrl}}"], "path": ["api", "user", "auth-tenant"]}}, "response": []}, {"name": "LoginUser", "event": [{"listen": "test", "script": {"exec": ["\r", "pm.test(\"setEnvironmentVariable Token User\", function () {\r", "    var jsonData = pm.response.json();\r", "     const  bodyToken=jsonData.accessToken\r", "    pm.globals.set(\"tokenUser\",bodyToken);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorizationtenant", "value": "{{tokenTenant}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"username\":\"{{defaultUserUsername}}\",\r\n    \"password\":\"{{defaultUserPassword}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/user/auth", "host": ["{{baseUrl}}"], "path": ["api", "user", "auth"]}}, "response": []}]}