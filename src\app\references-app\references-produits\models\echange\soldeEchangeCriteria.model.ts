import { TypeSoldeEchange } from 'src/app/references-app/references-produits/enums/echange/TypeSoldeEchange.enum';

// import { Moment } from 'moment';

import { Confrere } from 'src/app/references-app/references-produits/models/tiers/confrere/confrere.model';


export class SoldeEchangeCriteria { 
    confrere?: Confrere;
    dateSoldeMax?: any;
    dateSoldeMin?: any;
    numero?: string;
    sousStatut?: TypeSoldeEchange;
}

