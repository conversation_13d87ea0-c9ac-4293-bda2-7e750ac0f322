import { AfterViewInit, Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AuthenticationService } from 'src/app/core/service/auth.service';
import { EventService } from 'src/app/core/service/event.service';
import { AppsItem } from '../models/apps.model';
import { Language } from '../models/language.model';
import { NotificationItem } from '../models/notification.model';
import { ProfileOptionItem } from '../models/profileoption.model';
import { SearchResultItem, SearchUserItem } from '../models/search.model';
import { SIDEBAR_WIDTH_CONDENSED, SIDEBAR_WIDTH_FIXED } from '../models/layout.model';
import { Router } from '@angular/router';
import { Hotkey, HotkeysService } from 'angular2-hotkeys';
import { AuthService } from 'src/app/shared/services/auth.service';
import { Principal } from 'src/app/shared/models/principal';
import { Subscription } from 'rxjs';
import { AuthListenerService } from 'src/app/shared/services/auth-listener.service';
import { NetworkStatusService } from 'src/app/shared/services/network-status.service';

@Component({
  selector: 'app-topbar',
  templateUrl: './topbar.component.html',
  styleUrls: ['./topbar.component.scss']
})
export class TopbarComponent implements OnInit, AfterViewInit, OnDestroy {


  @Input() hideLogo: boolean = false;
  @Input() cssClasses: string = '';
  @Input() topbarDark: boolean = false;
  @Input() layoutType: string = '';

  notificationList: NotificationItem[] = [];
  languages: Language[] = [];
  apps: AppsItem[] = [];
  profileOptions: ProfileOptionItem[] = [];
  profileTenantOptions: ProfileOptionItem[] = [];/// added 
  selectedLanguage?: Language;
  searchResults: SearchResultItem[] = [];
  searchUsers: SearchUserItem[] = [];
  loggedInUser: any = {};
  loggedInTenant: any = {};
  topnavCollapsed: boolean = false;

  // output events
  @Output() settingsButtonClicked = new EventEmitter<boolean>();
  @Output() mobileMenuButtonClicked = new EventEmitter();
  autoclose: boolean = false;


  private subscription: Subscription;




  constructor(
    private authService: AuthService,
    private eventService: EventService,
    private router: Router,
    private _hotkeysService: HotkeysService,
    private authListenerService: AuthListenerService,
    public network: NetworkStatusService
  ) {


    this._hotkeysService.add(new Hotkey('ctrl+m', (event: KeyboardEvent): boolean => {
      console.log('Typed hotkey ctrl+m');
      this.router.navigateByUrl('/auth/logout');
      return false; // Prevent bubbling
    }));
    /// shortCut to redirect Vente
    // if (authService.hasAnyAuthority(['CREATION_VENTE']))
    //   this._hotkeysService.add(new Hotkey('alt+v', (event: KeyboardEvent): boolean => {
    //     this.router.navigateByUrl('/references-produits/ventes/edit/');
    //     return false; // Prevent bubbling
    //   }));




    this._hotkeysService.add(new Hotkey('alt+s', (event: KeyboardEvent): boolean => {
      // console.log('Typed hotkey ctrl+m');
      // this.router.navigateByUrl('/references-produits/ventes/edit/');
      if (this.authService.isTenantAuthenticated() && this.authService.isAuthenticated()) {
        this.authListenerService.toPushToken(false);
        this.authListenerService.toSetListUser(true)
        this.authListenerService.authQueriesSubject.next(0)  // current user is changing 
      }
      return false; // Prevent bubbling
    }));


  }


  ngAfterViewInit(): void {
    setTimeout(() => {
      this.onResizebar();
    }, .01);
  }


  @HostListener('window:resize', ['$event'])
  onResize(event) {

    this.onResizebar();

  }

  ngOnInit(): void {
    this.loggedInUser = this.authService.currentUser()
    this.loggedInTenant = this.authService.getTenantPrincipal()
    // get notifications
    this._fetchNotifications();
    // get supported languages
    this._fetchLanguages();
    // get apps
    this._fetchApps();
    // get profile menu options
    this._fetchProfileOptions();
    // get search results
    this._fetchSearchData();



    this.subscription = this.authService.principal$.subscribe((principal) => {
      this.loggedInUser = principal;
    });
  }




  /**
   * Fetches notifications
   */
  _fetchNotifications(): void {
    this.notificationList = [{
      text: 'Caleb Flakelar commented on Admin',
      subText: '1 min ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'primary',
      redirectTo: '/notification/1'
    },
    {
      text: 'New user registered.',
      subText: '5 min ago',
      icon: 'mdi mdi-account-plus',
      bgColor: 'info',
      redirectTo: '/notification/2'
    },
    {
      text: 'Cristina Pride',
      subText: 'Hi, How are you? What about our next meeting',
      avatar: 'assets/images/users/avatar-4.jpg',
      bgColor: 'success',
      redirectTo: '/notification/3'
    },
    {
      text: 'Caleb Flakelar commented on Admin',
      subText: '2 days ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'danger',
      redirectTo: '/notification/4'
    },
    {
      text: 'Caleb Flakelar commented on Admin',
      subText: '1 min ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'primary',
      redirectTo: '/notification/5'
    },
    {
      text: 'New user registered.',
      subText: '5 min ago',
      icon: 'mdi mdi-account-plus',
      bgColor: 'info',
      redirectTo: '/notification/6'
    },
    {
      text: 'Cristina Pride',
      subText: 'Hi, How are you? What about our next meeting',
      avatar: 'assets/images/users/avatar-1.jpg',
      bgColor: 'success',
      redirectTo: '/notification/7'
    },
    {
      text: 'Caleb Flakelar commented on Admin',
      subText: '2 days ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'danger',
      redirectTo: '/notification/8'
    }];
  }

  /**
   * Fetches supported languages
   */
  _fetchLanguages(): void {
    this.languages = [{
      id: 1,
      name: 'English',
      flag: 'assets/images/flags/us.jpg',
    },
    {
      id: 2,
      name: 'German',
      flag: 'assets/images/flags/germany.jpg',
    },
    {
      id: 3,
      name: 'Italian',
      flag: 'assets/images/flags/italy.jpg',
    },
    {
      id: 4,
      name: 'Spanish',
      flag: 'assets/images/flags/spain.jpg',
    },
    {
      id: 5,
      name: 'Russian',
      flag: 'assets/images/flags/russia.jpg',
    }];
    this.selectedLanguage = this.languages[0];
  }

  /**
   * Fetches brands
   */
  _fetchApps(): void {
    this.apps = [{
      id: 1,
      name: 'Slack',
      logo: 'assets/images/brands/slack.png',
    },
    {
      id: 2,
      name: 'Github',
      logo: 'assets/images/brands/github.png',
    },
    {
      id: 3,
      name: 'Dribbble',
      logo: 'assets/images/brands/dribbble.png',
    },
    {
      id: 4,
      name: 'Bitbucket',
      logo: 'assets/images/brands/bitbucket.png',
    },
    {
      id: 5,
      name: 'Dropbox',
      logo: 'assets/images/brands/dropbox.png',
    },
    {
      id: 6,
      name: 'G Suite',
      logo: 'assets/images/brands/g-suite.png',
    }];
  }

  /**
   * Fetches profile options
   */
  _fetchProfileOptions(): void {
    this.profileOptions = [
      {
        label: 'Mon compte',
        icon: 'mdi mdi-account-circle',
        redirectTo: '/webfix/account/moncompte',
      },
      // {
      //   label: 'Paramètres',
      //   icon: 'mdi mdi-account-edit',
      //   redirectTo: '/',
      // },
      // {
      //   label: 'Support',
      //   icon: 'mdi mdi-lifebuoy',
      //   redirectTo: '/',
      // },
      // {
      //   label: 'Écran verrouillé',
      //   icon: 'mdi mdi-lock-outline',
      //   redirectTo: '/account/lock-screen',
      // },
      {
        label: 'déconnecter utilisateur',
        icon: 'mdi mdi-logout',
        redirectTo: '/auth/logout',
      },
    ];

    ///
    this.profileTenantOptions = [


      // {
      //   label: 'déconnecter pharmacie',
      //   icon: 'mdi mdi-logout',
      //   redirectTo: '/auth/logout/tenant',
      // },
    ];

  }

  /**
   * Fetches search results
   */
  _fetchSearchData(): void {
    this.searchResults = [{
      id: 1,
      text: 'Analytics Report',
      icon: 'uil-notes',
    },
    {
      id: 2,
      text: 'How can I help you?',
      icon: 'uil-life-ring',
    },
    {
      id: 3,
      text: 'User profile settings',
      icon: 'uil-cog',
    }];
    this.searchUsers = [{
      id: 1,
      name: 'Erwin Brown',
      position: 'UI Designer',
      profile: 'assets/images/users/avatar-2.jpg'
    },
    {
      id: 2,
      name: 'Jacob Deo',
      position: 'Developer',
      profile: 'assets/images/users/avatar-5.jpg'
    }]

  }


  /**
  * Change the language
  * @param language selected language from dropdown
  */
  changeLanguage(language: Language) {
    this.selectedLanguage = language;
  }

  /**
   * Toggles the right sidebar
   */
  toggleRightSidebar() {
    this.settingsButtonClicked.emit();
  }

  /*
  * Toggle left sidebar width - condensed
  */

  toggleSidebarWidth(): void {
    if (document.body.hasAttribute('data-leftbar-compact-mode') && document.body.getAttribute('data-leftbar-compact-mode') === 'condensed') {
      // document.body.removeAttribute('data-leftbar-compact-mode');
      this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_FIXED);
      this.storeMenuState(SIDEBAR_WIDTH_FIXED);
    } else {
      // document.body.setAttribute('data-leftbar-compact-mode', 'condensed');
      this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_CONDENSED);
      this.storeMenuState(SIDEBAR_WIDTH_CONDENSED);
    }
  }

  storeMenuState(value) {
    localStorage.setItem('menu_webfix', value);
  }



  /**
   * Toggle the menu bar when having mobile screen
   */
  toggleMobileMenu(event: any) {
    this.topnavCollapsed = !this.topnavCollapsed;
    event.preventDefault();
    this.mobileMenuButtonClicked.emit();
  }



  onResizebar() {
    // console.log(window.innerWidth);
    if (window.innerWidth <= 1380) {
      this.autoclose = true;
      this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_CONDENSED);
    }

    // Sidebar change:: this is to prevent the left-sidebar to be toggled as fixed when the size of the screen change from small(condensed) to large(fixed)
    // else if (this.autoclose == true) {
    //   this.autoclose = false;
    //   this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_FIXED);
    // }
  }









  ngOnDestroy() {
    if (this.subscription)
      this.subscription.unsubscribe();
  }

}
