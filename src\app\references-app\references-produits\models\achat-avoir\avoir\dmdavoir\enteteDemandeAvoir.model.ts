import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';

// import { Moment } from 'moment';

import { Depot } from 'src/app/references-app/references-produits/models/produit/stock/depot.model';
import { DetailDemandeAvoir } from './detailDemandeAvoir.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';


export class EnteteDemandeAvoir { 
    audited?: boolean;
    dateDmd?: any;
    depot?: Depot;
    detailDemandeAvoirs?: DetailDemandeAvoir[];
    fournisseur?: Fournisseur;
    id?: number;
    mntAchatStd?: number;
    mntBrutHt?: number;
    mntBrutTtc?: number;
    mntNetHt?: number;
    mntNetTtc?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTva?: number;
    mntVenteStd?: number;
    nbrLigne?: number;
    numDmd?: number;
    raisonSociale?: string;
    statut?: Statut;
    totalQtAvoir?: number;
    totalQtLivre?: number;
    userModifiable?: boolean;
}
