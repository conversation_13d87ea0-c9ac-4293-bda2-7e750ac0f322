import { TypeAxeEnum } from "src/app/references-app/references-produits/enums/Compta/TypeAxe.enum";
import { TypeComptaEnum } from "src/app/references-app/references-produits/enums/Compta/TypeCompta.enum";
import { Statut } from "src/app/references-app/references-produits/enums/common/Statut.enum";

export interface ComptaAnalytiqueCriteria {
    annee?: number; 
    categorie?: string; 
    codeFt?: string; 
    dateVente?: string; 
    moisDebut?: number; 
    moisExact?: number; 
    moisFin?: number; 
    statut?: Statut; 
    typeAxe?: TypeAxeEnum; 
    typeCompta?: TypeComptaEnum; 
  }