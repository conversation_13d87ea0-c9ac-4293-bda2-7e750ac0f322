interface IFormeProduit {
  abbrevForme: string;
  codeForme: string;
  dateCreation: string;
  dateDernModif: string;
  dateSuppression: string;
  estActif: string;
  id: number;
  libelleForme: string;
  tenantId: number;
}

export class FormeProduit implements IFormeProduit {
  abbrevForme: string;
  codeForme: string;
  dateCreation: string;
  dateDernModif: string;
  dateSuppression: string;
  estActif: string;
  id: number;
  libelleForme: string;
  tenantId: number;

  constructor(forme?: Partial<IFormeProduit>) {
    Object.assign(this, forme);
  }
}