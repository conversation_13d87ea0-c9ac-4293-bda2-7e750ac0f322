import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { WinClientProvinces } from '../../Services/winclient.provinces';
import { Province } from '../../models/province';
import { WinClientLocalite } from '../../models/localite';
import { WinclientLocaliteService } from '../../Services/winclient.localite.service';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Observable, of, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, switchMap } from 'rxjs/operators';
import { AlertService } from 'src/app/shared/services/alert.service';

@Component({
  selector: 'app-localite',
  templateUrl: './localite.component.html',
  styleUrls: ['./localite.component.scss']
})
export class LocaliteComponent implements OnInit {

  @ViewChild('updateOrCreateLocaliteModal') updateOrCreateLocaliteModal: TemplateRef<any>;
  modalRef : NgbModalRef;
  modalMode : "EDIT" | "CREATE" = "CREATE";
  localite: WinClientLocalite[] = [];
  localiteFiltred: WinClientLocalite[] = [];
  provinces: Province[] = [];
  localiteForm :FormGroup;
  submited = false;
  navigation :Pagination = {
    pageSize :25,
    skip:0,
  };
  constructor(
    private  localiteService: WinclientLocaliteService,
    private modalService: NgbModal,
    private winClientProvinces: WinClientProvinces,
    private alertService:AlertService
  ) { }

 
  ngOnInit() {
    this.getLocalite();
    this.initLocaliteForm();
    this.getProvinces();
   }

   initLocaliteForm(){
    this.localiteForm = new FormGroup({
      id: new FormControl(null),
      libLocalite: new FormControl('', [Validators.required]),
      province: new FormControl(null,[Validators.required]),
    });
  }

  getLocalite() {
    this.localiteService.getAllLocalites().subscribe(
      (data) => {
        this.localite = data?.sort((a,b)=> a.libLocalite.localeCompare(b.libLocalite));
        this.localiteFiltred = data?.sort((a,b)=> a.libLocalite.localeCompare(b.libLocalite));
      }
    );
  }
  getProvinces() {
    this.winClientProvinces.getProvinces().subscribe(
      (data) => {
        this.provinces = data;
      }
    );
  }


  searchProvinceTypeahead: OperatorFunction<string, readonly Province[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      filter((term) => term.length >= 2),
      switchMap((term) => {
       const targetProvince =  this.provinces.filter((v) => v.libProvince.toLowerCase().indexOf(term.toLowerCase()) > -1);
        return of(targetProvince);
      })
    )

    
  provinceFormatter = (province: Province) =>  province.libProvince;
  

  pageChange(skip:number) {
    this.navigation.skip =skip;
    const page = Math.ceil(skip / this.navigation.pageSize);
  }


  formErrors(field:string){
    return this.localiteForm.controls[field];
  }


  openCreateLocalite() {
    this.modalMode = "CREATE";
    this.localiteForm.reset();
    this.modalRef = this.modalService.open(this.updateOrCreateLocaliteModal, { size: 'lg' });
  }


  openEditLocalite(localite:WinClientLocalite){
    this.modalMode = "EDIT";
    this.localiteForm.patchValue({
      libLocalite : localite.libLocalite,
      province : this.provinces.find(province => province.id === localite.provinceId),
      id : this.modalMode === "EDIT" ? localite.id : null
    });
    this.modalRef = this.modalService.open(this.updateOrCreateLocaliteModal, { size: 'lg' });
  }

  formToLocaliteModel(){
      const localite = new WinClientLocalite({
        libLocalite : this.localiteForm.value.libLocalite,
        provinceId : this.localiteForm.value.province?.id,
      });
      this.modalMode === "EDIT" && (localite.id = this.localiteForm.value.id);
      return localite;
  }


  saveOrUpdateLocalite(){
    this.submited = true;
   
    const localite = this.formToLocaliteModel() ;
    console.log("localite from form", localite)
    if(this.modalMode == "EDIT"){
      this.updateLocalite(localite);
    }else{
      // this.createLocalite(localite);
    }
  }


  filterLocalite(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.localite = this.localiteFiltred.filter((localite) =>
      localite.libLocalite.toLowerCase().includes(searchTerm)
    );
  }

  private createLocalite(localite:WinClientLocalite){
    this.localiteService.createLocalite(localite).subscribe(res => {
      this.getLocalite();
    });
  }


  private updateLocalite(localite:WinClientLocalite){
    console.log("localite from form", localite)
    this.localiteService.updateLocalite(localite.id,localite).subscribe(res => {
      this.submited = false;
      this.alertService.success(`La localite ${localite.libLocalite} a été modifiée avec succès`);
      this.localiteForm.reset();
      this.modalRef?.close();
      this.getLocalite();
    });
  }


}
