
import { Pays } from './pays.model';
import { SpecialiteMedecin } from './specialiteMedecin.model';
import { Ville } from 'src/app/references-app/references-produits/models/common/ville.model';


export class Medecin {
    adr1?: string;
    audited?: boolean;
    id?: number;
    nom?: string;
    pays?: Pays;
    specialite?: SpecialiteMedecin;
    userModifiable?: boolean;
    ville?: Ville;

    estActif?: boolean;        //TODO: OTH
}
