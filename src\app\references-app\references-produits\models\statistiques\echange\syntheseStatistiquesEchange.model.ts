


export class SyntheseStatistiquesEchange { 
    tauxRemise?: number;
    totalMontantEchange?: number;
    totalMontantEchangeEntree?: number;
    totalMontantEchangeEntreePph?: number;
    totalMontantEchangeEntreePpv?: number;
    totalMontantEchangeSortie?: number;
    totalMontantEchangeSortiePph?: number;
    totalMontantEchangeSortiePpv?: number;
    totalMontantRemise?: number;
    totalMontantRemiseEntree?: number;
    totalMontantRemiseSortie?: number;
    totalQuantiteEchange?: number;
    totalQuantiteEchangeEntree?: number;
    totalQuantiteEchangeSortie?: number;
    totalQuantiteUg?: number;
    totalQuantiteUgEntree?: number;
    totalQuantiteUgSortie?: number;
    totalSoldesConfreres?: number;
}
