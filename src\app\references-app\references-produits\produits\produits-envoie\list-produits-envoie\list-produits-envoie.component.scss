.checkbox-grid {
    appearance: none; 
    width: 20px;
    height: 20px;
    cursor: pointer;
    border: 2px solid #ccc;
    border-radius: 4px;
    background-color: #fff; 
    position: relative; 
    transition: all 0.2s ease;
  
    &:checked {
      background-color: #540B0E; 
      border-color: #540B0E;
    }
  
    &:checked::after {
      content: '\2713'; 
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white; 
      font-size: 14px;  
    }
  
    &:hover {
      border-color: #9E2A2B; 
    }
  }


  .site-option {
    flex: 0 0 calc(33.333% - 0.5rem); 
    background-color: #f8f9fa; 
    border: 1px solid #dee2e6; 
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-weight: 600;
    
    &.selected {
      background-color: #E09F3E; 
      border-color: #E09F3E;
      color: white;
    }
  
    &:hover {
      background-color: #e9ecef; 
    }
  
    &.selected:hover {
      background-color: #d18e2e; 
    }
  }
  
  .d-flex.flex-wrap.gap-2 {
    gap: 0.5rem; 
  }

  .sites-container {
    max-width: 500px; /* Optional: Limits horizontal width; adjust as needed */
    max-height: 200px; /* Sets a fixed height to allow at least two lines; adjust as needed */
    overflow-y: auto; /* Enables vertical scrolling when content exceeds height */
    display: flex; /* Uses flexbox for layout */
    flex-wrap: wrap; /* Allows sites to wrap into multiple lines */
    gap: 8px; /* Spacing between site options (equivalent to Bootstrap gap-2) */
    padding: 5px; /* Adds padding for aesthetics and scrollbar visibility */
  }
  
  .site-option {
    display: inline-block; /* Ensures each site option behaves as a block within the flex container */
    cursor: pointer; /* Indicates clickable items */
  }
  
  /* Optional: Style the scrollbar for better aesthetics */
  .sites-container::-webkit-scrollbar {
    width: 8px; /* Width of the vertical scrollbar */
  }
  
  .sites-container::-webkit-scrollbar-thumb {
    background-color: #888; /* Color of the scrollbar thumb */
    border-radius: 4px; /* Rounded edges */
  }
  
  .sites-container::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* Darker color on hover */
  }