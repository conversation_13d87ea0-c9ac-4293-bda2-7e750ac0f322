
export class StructureTicket {

  elementsTicket?: ElementTicket[];
  positionImage?: number;     // 0=do not display|1= Top|2= Bottom

  displayRaisonSocial?: boolean;
  displayNomResponsable?: boolean;
  displayRc?: boolean;
  displayIf?: boolean;
  displayPatente?: boolean;
  displayIce?: boolean;
  displayTel?: boolean;
  displayGsm?: boolean;
  displayVille?: boolean;
  displayAddress?: boolean;
  displayEmail?: boolean;

}


export class ElementTicket {

  label: string;
  attr: TicketSections;
  verticalPosition: DisplayPosition;    // E1 | E2 | P1 | P2 | NONE
  fontSize: number;    //1,2,3,4
  horizonalPosition: number;    //1= left | 2= center  |3= right
}


export enum DisplayPosition {
  E1 = "E1",
  E2 = "E2",
  P1 = "P1",
  P2 = "P2",
  NONE = ""
}

export enum TicketSections {
  RAISONS_SOCIALES = "raisonsSociales",
  MENTIONS_LEGALES = "mentionsLegales",
  COORDONNES = "coordonnes"
}