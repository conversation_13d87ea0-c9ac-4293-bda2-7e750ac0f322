
import { SyntheseAchat } from './syntheseAchat.model';
import { SyntheseAssurance } from './syntheseAssurance.model';
import { SyntheseClient } from './syntheseClient.model';
import { SyntheseCreditClient } from './syntheseCreditClient.model';
import { SyntheseEchange } from './syntheseEchange.model';
import { SyntheseProduit } from './syntheseProduit.model';
import { SyntheseStock } from './syntheseStock.model';
import { SyntheseVenteInv } from './syntheseVenteInv.model';


export class StatistiquesInventaire { 
    syntheseAchat?: SyntheseAchat;
    syntheseAssurance?: SyntheseAssurance;
    syntheseClient?: SyntheseClient;
    syntheseCreditClient?: SyntheseCreditClient;
    syntheseEchange?: SyntheseEchange;
    syntheseProduit?: SyntheseProduit;
    syntheseStock?: SyntheseStock;
    syntheseVente?: SyntheseVenteInv;
}
