
import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Taxe } from 'src/app/references-app/references-produits/models/common/taxe.model';


export class StatistiqueVentePartieProduit { 
    categorie?: CategorieProduit;
    codeProduit?: string;
    datePeremption?: string;
    designationProduit?: string;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    numeroLot?: string;
    ppv?: number;
    stock?: number;
    tva?: Taxe;
}
