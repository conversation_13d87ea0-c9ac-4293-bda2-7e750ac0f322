export enum ActionName {


    CMDACHAT = 'CMDACHAT',    // commande achat
    FACHAT = 'FACHAT', /// facture commande achat
    BLACHAT = 'BLACHAT',    // bla
    FTPA = 'FTPA', // facture TPA autre convention
    FTPADET = 'FTPADET',  // facture TPA detail
    FTPARECAP = 'FTPARECAP', /// facture TPA Recap
    FTPAAMO = 'FTPAAMO', /// facture TPA AMO
    BLAVOIR = 'BLAVOIR',   // bl avoir recu
    DMDAVOIR = 'DMDAVOIR',    // demande avoir

    VENTE = 'VENTE', // vente
    BRD = 'BRD', ///bordereau
    FTPAONE = 'FTPAONE', /// facture TPA AMO 
    FTPAPEC = 'FTPAPEC', // prise en charge

    FTPACLT = 'FTPACLT',

    DEVIS = "DEVIS",

    BLVC = "BLVC",  // Bon de livraison
    BLRVC = "BLRVC", /// Bon de retour

    RECAPONE = "RECAPONE",


    /// ECHANGE 
    SOLDEECHG = 'SOLDEECHG', // sold Echange
    ECHANGE = 'ECHANGE',   // echange
    /// BL
    BLACHAT1 = 'BLACHAT1',  // -- bl livraion livraison commande
    BLACHAT2 = 'BLACHAT2',  // -- bl livraion (etat detail / bl)
    BLACHAT3 = 'BLACHAT3',  // -- bl livraion (supprimé)
    FTPADETVT = 'FTPADETVT', //-- facture TPA detail par vente


    TRASFRTSTK = "TRASFRTSTK",

    BLAVOIRFRN = "BLAVOIRFRN",


    FACTCLT1 = 'FACTCLT1',  //Facture client
    FACTACHAT1 = 'FACTACHAT1',  //Facture achat frn
    INVENTAIRE = 'INVENTAIRE'  //inventaire






}

