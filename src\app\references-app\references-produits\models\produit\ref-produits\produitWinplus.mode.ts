interface FamilleTarifaireDto {
  id?: number;
  tenantId?: number;
  dateCreation?: string;
  dateDernModif?: string;
  dateSuppression?: string;
  codeFt?: string;
  libelleFt?: string;
  tauxMarge?: number;
  margeLibre?: string;
  estActif?: string;
  prdTransferable?: boolean;
}

interface FormeDto {
  id?: number;
  tenantId?: number;
  dateCreation?: string;
  dateDernModif?: string;
  dateSuppression?: string;
  codeForme?: string;
  libelleForme?: string;
  abbrevForme?: string;
  estActif?: string;
}

interface CategroieDto {
  id?: number;
  tenantId?: number;
  dateCreation?: string;
  dateDernModif?: string;
  dateSuppression?: string;
  codeCategorie?: string;
  libelleCategorie?: string;
  estActif?: string;
  forceSuggestionPrd?: boolean;
}

interface TaxeDto {
  id?: number;
  typeTaxe?: string;
  libelleTaxe?: string;
  tauxTaxe?: number;
}

interface FournisseurDto {
  id?: number;
  typeFrn?: string;
  raisonSociale?: string;
}

interface DciDto {
  id?: number;
  libelleDci?: string;
  code?: string;
}

interface IProduitWinplus {
  id?: number;
  dateCreation?: string;
  dateSuppression?: string;
  designation?: string;
  codeWinplus?: string;
  codeGroupe?: string;
  prixAchatStd?: number;
  prixVenteStd?: number;
  prixFabHt?: number;
  codeBarre?: string;
  dosage?: string;
  famille?: FamilleTarifaireDto;
  forme?: FormeDto;
  categorie?: CategroieDto;
  tva?: TaxeDto;
  isSuggestion?: string;
  labo?: FournisseurDto;
  dci?: DciDto;
}

export class ProduitWinplus implements IProduitWinplus {
  id?: number;
  dateCreation?: string;
  dateSuppression?: string;
  designation?: string;
  codeWinplus?: string;
  codeGroupe?: string;
  prixAchatStd?: number;
  prixVenteStd?: number;
  prixFabHt?: number;
  codeBarre?: string;
  dosage?: string;
  famille?: FamilleTarifaireDto;
  forme?: FormeDto;
  categorie?: CategroieDto;
  tva?: TaxeDto;
  isSuggestion?: string;
  labo?: FournisseurDto;
  dci?: DciDto;

  constructor(produit?: Partial<ProduitWinplus>) {
    Object.assign(this, produit);
  }
}
