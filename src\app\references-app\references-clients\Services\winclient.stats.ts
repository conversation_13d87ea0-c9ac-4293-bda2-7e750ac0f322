import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment as env } from 'src/environments/environment';
import { ClientStats } from "../models/winclient.stats";

@Injectable({
  providedIn: 'root'
})
export class WinclientStatsService {
  constructor(private httpClient: HttpClient) { }


  getStats(params: any = {}) {
    return this.httpClient.get<ClientStats>(`${env.winclient_base_url}/api/winclient/dashboard/indicateurs-clients-sites`,{params});
  }
}