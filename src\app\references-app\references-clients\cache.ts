import { RouteReuseStrategy } from '@angular/router/';
import { ActivatedRouteSnapshot, DetachedRouteHandle } from '@angular/router';
import { Observer, Subject } from 'rxjs';
import { Injectable } from '@angular/core';



@Injectable({
    providedIn: 'root'
})



/// store url to go back if navigate to (not by default)

export class MyCacheRouteReuseStrategy {








    // private routeStore = new Map<string, DetachedRouteHandle>(); /// store CompCache
    // ///  fromComp that will be stored and retrieved 
    // private fromCompsToComps = [
    //     {
    //         toComp: "ConsultationCommandeComponent",
    //         fromComp: "ListCommandesComponent", /// 

    //     },
    //     {
    //         toComp: "SaisieVenteComponent",
    //         fromComp: "VisualisationVentesComponent",
    //     },
    //     {
    //         toComp: "SaisieCommandeComponent",
    //         fromComp: "ListCommandesComponent",

    //     },
    //     {
    //         toComp: "SaisieVenteComponent",
    //         fromComp: "ListMouvmentTpComponent",
    //     },
    //     {
    //         toComp: "SaisieVenteComponent",
    //         fromComp: "SuiviFactureTpComponent",
    //     }


    // ]

    // private current: string = null
    // private future: string = null

    // /// 111111
    // shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    //     return future.routeConfig === curr.routeConfig;
    // }

    // /// 111
    // shouldDetach(route: ActivatedRouteSnapshot): boolean {
    //     const a = this.getPath(route)
    //     this.current = a
    //     let elem = this.fromCompsToComps.find(val => {
    //         return val.fromComp === this.current && val.toComp === this.future
    //     })
    //     return elem ? true : false

    // }
    // /// 222
    // store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle): void {
    //     const a = this.getPath(route)
    //     this.routeStore.set(a, handle);
    // }

    // /***************************** 3333  */
    // shouldAttach(route: ActivatedRouteSnapshot): boolean {
    //     const a = this.getPath(route)
    //     this.future = a
    //     let elem = this.fromCompsToComps.find(val => {

    //         return val.fromComp === this.future && val.toComp === this.current
    //     })
    //     const ab = this.routeStore.get(a)
    //     return (elem && ab) ? true : false

    // }

    // /// 
    // retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle {
    //     const a = this.getPath(route)
    //     let re = this.routeStore.get(a) as DetachedRouteHandle ?? null;
    //     return re

    // }

    // /// get name component
    // private getPath(route: ActivatedRouteSnapshot): string {
    //     if (route.routeConfig && route.routeConfig.component) {
    //         return route.routeConfig.component.name;
    //     }
    //     return '';
    // }

}