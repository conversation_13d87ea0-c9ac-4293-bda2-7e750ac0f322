<!-- statistics card start -->
<ng-container>
    <ng-template [ngTemplateOutlet]="SimpleCard" *ngIf="!bgClass"></ng-template>
    <ng-template [ngTemplateOutlet]="ColoredCard" *ngIf="bgClass"></ng-template>
</ng-container>
<!-- statistics card end -->

<!-- resuable templated -->
<ng-template #SimpleCard>
    <div class="card widget-flat">
        <div class="card-body-cus">
            <div class="float-end">
                <i class="widget-icon" [class]="icon"></i>
            </div>
            <h5 class="text-muted fw-normal mt-0" [title]="title">{{title}}</h5>
            <h3 class="mt-3 mb-3">{{stats}}</h3>
            <p class="mb-0 text-muted">
                <span class="me-2 badge {{badgeVariant}}" *ngIf="badgeVariant"> <i [class]="trendIcon"></i>
                    {{trendNumber}}</span>
                <span class="me-2" [class]="trendTextClass" *ngIf="!badgeVariant"><i [class]="trendIcon"></i>
                    {{trendNumber}}</span>
                <span class="text-nowrap">{{trendTime}}</span>
            </p>
        </div>
    </div>
</ng-template>

<ng-template #ColoredCard>
    <div class="card widget-flat text-white" [class]="bgClass">
        <div class="card-body-cus">
            <div class="float-end">
                <i class="widget-icon" [class]="icon"></i>
            </div>
            <h5 class="fw-normal mt-0" [title]="description">{{title}}</h5>
            <h3 class="mt-3 mb-3">{{stats}}</h3>
            <p class="mb-0">
                <span class="me-2 badge {{badgeVariant}}" *ngIf="badgeVariant"> <i [class]="trendIcon"></i>
                    {{trendNumber}}</span>
                <span class="me-2" [class]="trendTextClass" *ngIf="!badgeVariant"><i [class]="trendIcon"></i>
                    {{trendNumber}}</span>
                <span class="text-nowrap">{{trendTime}}</span>
            </p>
        </div>
    </div>
</ng-template>