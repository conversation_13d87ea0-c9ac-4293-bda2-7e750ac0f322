import { ModePaiement } from 'src/app/references-app/references-produits/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { Confrere } from 'src/app/references-app/references-produits/models/tiers/confrere/confrere.model';
import { EnteteSoldeEchangeSynthese } from './enteteSoldeEchangeSynthese.model';


export class EnteteSoldeEchangeInput { 
    audited?: boolean;
    confrere?: Confrere;
    dateDebut?: any;
    dateFin?: any;
    modeReglement?: ModePaiement;
    refReglement?: string;
    syntheses?: EnteteSoldeEchangeSynthese[];
    userModifiable?: boolean;
}

