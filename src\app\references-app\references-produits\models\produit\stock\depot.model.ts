
import { Localite } from 'src/app/references-app/references-produits/models/common/localite.model';
import { Pays } from 'src/app/references-app/references-produits/models/common/pays.model';
import { Ville } from 'src/app/references-app/references-produits/models/common/ville.model';


export class Depot {
    adr1?: string;
    adr2?: string;
    audited?: boolean;
    codeDepot?: string;
    codePostal?: string;
    id?: number;
    libelleDepot?: string;
    localite?: Localite;
    pays?: Pays;
    userModifiable?: boolean;
    ville?: Ville;
    primaire?: boolean;


    estActif?: boolean = true;  // TODO: OTH
}
