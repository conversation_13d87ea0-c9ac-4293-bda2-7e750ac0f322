
import { Depot } from '../stock/depot.model';
import { Process } from './process.model';
import { ProcessSortie } from './processSortie.model';


export class OrdreProduction {
    audited?: boolean;
    dateDeb?: any;
    dateFin?: any;
    id?: number;
    process?: Process;
    qteFab?: number;
    userModifiable?: boolean;

    detailOrdreProductions?: ProcessSortie[];  // the newly created process

    sortieDepot?: Depot
    // TODO:: Remove these fields since they r not used
    // datePeremption?: string;
    // intrantDepot?: Depot;
    // numLot?: string;
    // sortieDepot?: Depot;
}
