export const CodeSites :{ code_site: number; nom: string; code: string }[] = [
    { code_site: 0, nom: "Base Groupe", code: "SOPH" },
    { code_site: 1, nom: "UGP", code: "UGP" },
    { code_site: 2, nom: "CPM", code: "CPM" },
    { code_site: 3, nom: "DIPHARM", code: "DIPH" },
    { code_site: 4, nom: "GIPHAR", code: "GIPH" },
    { code_site: 5, nom: "SOPHACA_CASA", code: "SSDM" },
    { code_site: 7, nom: "TADLAPHARM_BM", code: "BM" },
    { code_site: 8, nom: "TADLAPHARM_FBS", code: "FBS" },
    { code_site: 9, nom: "SOPHATLENTIC", code: "SAFI" },
    { code_site: 10, nom: "MEDIREP", code: "MEDI" },
    { code_site: 11, nom: "SOPHACA_MAR", code: "<PERSON><PERSON>" },
    { code_site: 12, nom: "ORP", code: "ORP" },
    { code_site: 13, nom: "PHARMANORD", code: "NORD" },
    { code_site: 14, nom: "S<PERSON>HAGHARB", code: "SGHB" },
    { code_site: 16, nom: "PLATEFORME", code: "PLTF" },
    { code_site: 50, nom: "SOPHANORD", code: "SNRD" },
    { code_site: 51, nom: "VPS_SOPHANORD", code: "VNORD" },
    { code_site: 52, nom: "SRMLAAYOUNE", code: "SRML" },
    { code_site: 53, nom: "DISPHAT_TAZA", code: "DPTA" },
    { code_site: 54, nom: "SOPHACHARK", code: "SCHRK" },
    { code_site: 55, nom: "REPHAR", code: "REPR" },
    { code_site: 56, nom: "SOPHAFAS", code: "SFAS" },
    { code_site: 57, nom: "DISPHANAD", code: "DISP" },
    { code_site: 500, nom: "Cooper Benisnassen", code: "CBN" },
    { code_site: 501, nom: "LAPROPHAN", code: "LPH" },
  ];