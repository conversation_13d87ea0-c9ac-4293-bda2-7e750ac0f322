import { TypePrixEchange } from 'src/app/references-app/references-produits/enums/echange/TypePrixEchange.enum';
import { SensEchange } from 'src/app/references-app/references-produits/enums/echange/SensEchange.enum';

// import { Moment } from 'moment';

import { ConfrereAbrege } from 'src/app/references-app/references-produits/models/tiers/confrere/confrereAbrege.model';


export class StatistiqueEchangePartieEntete { 
    confrere?: ConfrereAbrege;
    dateEchange?: any;
    numeroEchange?: string;
    sensEchange?: SensEchange;
    typePrixEchange?: TypePrixEchange;
}

