import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {  ListProduitsEnvoieComponent } from './list-produits-envoie/list-produits-envoie.component';


const routes: Routes = [

  // {
  //   path: "edit",
  //   children: [
  //     {
  //       path: "", component: SaisieCommandeComponent, data: {
  //         idComp: "SaisieCommandeComponent"
  //       },
  //       canDeactivate: [CanDeactivateGuard]
  //     },
  //     {
  //       path: ":idCmd",
  //       component: SaisieCommandeComponent,
  //       resolve: { cmd: CommandeResolver },
  //       canDeactivate: [CanDeactivateGuard]
  //     },
  //   ],

  // },


  {
    path: "",
    children: [
      {
        path: "", component: ListProduitsEnvoieComponent, data: {
          idComp: "ListProduitsEnvoieComponent"
        }
      },
      // {
      //   path: 'consultation/:idCmd',
      //   component: ConsultationCommandeComponent,
      //   resolve: { cmd: CommandeResolver }
      // },
      
    ]
  },
  // {
  //   path: "commandeValid/:idCmd",
  //   component: CommandeValideComponent,
  //   resolve: { cmd: CommandeResolver }
  // }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProduitsWinplusRoutingModule { }
