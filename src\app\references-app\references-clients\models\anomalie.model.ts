import { Client<PERSON>roupe } from "./clientGroupe";

interface IAnomalie {
  anomalie: string;
  clientGroupeId: number;
  codeClientGroupe: string;
  codeClientGroupeDoublon: string;
  dateCreationAnomalie: string;
  dateTraitement?: string;
  pourcentageMatching?: number;
  id: number;
  statut?: StatutAnomalie;
}

enum AnomalieType {
  DOUBLON_EXACT = 'DOUBLON_EXACT',
  DOUBLON_PROBABLE = 'DOUBLON_PROBABLE',
  RAISON_VILLE = 'RAISON_VILLE',
  NOM_CAR_1 = 'NOM_CAR_1',
  NOM_TROP_COURT = 'NOM_TROP_COURT'
}

interface IAnomalieCriteria {
  anomalieType?: AnomalieType;
  codeClientGroupe?: string;
  dateCreationAnomalie?: string;
  dateTraitement?: string;
  statut?: string;
}


export class AnomalieCriteria implements IAnomalieCriteria {
  anomalieType?: AnomalieType;
  codeClientGroupe?: string;
  dateCreationAnomalie?: string;
  dateTraitement?: string;
  statut?: string;

  constructor(anomalieCriteria: Partial<IAnomalieCriteria>) {
    Object.assign(this, anomalieCriteria);
  }
}


export class Anomalie implements IAnomalie {
  anomalie: string;
  clientGroupeId: number;
  codeClientGroupe: string;
  clientGroupe: ClientGroupe;
  dateCreationAnomalie: string;
  dateTraitement: string;
  pourcentageMatching: number;
  codeClientGroupeDoublon: string;
  id: number;
  statut: StatutAnomalie;

  constructor(anomalie: Partial<IAnomalie>) {
    Object.assign(this, anomalie);
  }
}



export enum StatutAnomalie {
  FREMEE="fermee",
  OUVERTE="ouverte"
}