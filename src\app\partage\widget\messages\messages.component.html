<div class="card">
    <div class="card-body-cus">
        <div class="float-end" ngbDropdown placement="bottom-end">
            <a href="javascript:void(0)" class="arrow-none card-drop" id="messageDropdown" aria-expanded="false"
                ngbDropdownToggle>
                <i class="mdi mdi-dots-vertical"></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="messageDropdown">
                <!-- item-->
                <a ngbDropdownItem routerLink="/">Settings</a>
                <!-- item-->
                <a ngbDropdownItem routerLink="/">Action</a>
            </div>
        </div>
        <h4 class="header-title mb-3">Messages</h4>
        <div class="inbox-widget">
            <div class="inbox-item" *ngFor="let message of messages">
                <div class="inbox-item-img"><img [src]="message.avatar" class="rounded-circle" alt=""></div>
                <p class="inbox-item-author">{{message.sender}}</p>
                <p class="inbox-item-text">{{message.text}}</p>
                <p class="inbox-item-date">
                    <a href="javascript:void(0)" class="btn btn-sm btn-link text-info font-13"> Reply </a>
                </p>
            </div>
        </div>
    </div> <!-- end card-body-cus-->
</div> <!-- end card-->