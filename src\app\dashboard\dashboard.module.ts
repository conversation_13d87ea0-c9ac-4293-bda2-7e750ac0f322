import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { Select2Module } from 'ng-select2-component';
import { NgxMaskModule } from 'ngx-mask';
import { GridModule } from '@progress/kendo-angular-grid';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbAccordionModule, NgbDatepickerModule, NgbCollapseModule, NgbDropdownModule, NgbModalModule, NgbTypeaheadModule, NgbNavModule, NgbProgressbarModule, NgbAlertModule, NgbTimepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '../shared/shared.module';
import { dashboardComponent } from './dashboard.component';
import { PageTitleModule } from '../partage/page-title/page-title.module';
import { dashboardRoutingModule } from './dashboard-routing.module';
import { WidgetModule } from '../partage/widget/widget.module';
import { SimplebarAngularModule } from 'simplebar-angular';
import { NgApexchartsModule } from 'ng-apexcharts';
import { ClickOutsideModule } from 'ng-click-outside';
import { ButtonGroupModule } from '@progress/kendo-angular-buttons';



@NgModule({
  declarations: [dashboardComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NgbTypeaheadModule,
    Select2Module,
    NgbModalModule,
    NgbDatepickerModule,
    NgbAccordionModule,
    NgbCollapseModule,
    NgxMaskModule.forRoot(),
    SharedModule,
    GridModule,
    NgbDropdownModule,
    NgbAccordionModule,
    NgbNavModule,
    PageTitleModule,
    NgbAlertModule,
    NgbDatepickerModule,
    NgbProgressbarModule,
    NgApexchartsModule,
    SimplebarAngularModule,
    WidgetModule,
    dashboardRoutingModule,  
    ClickOutsideModule,
    NgbTimepickerModule,
    ButtonGroupModule
  ]
})
export class dashboardmodule { }
