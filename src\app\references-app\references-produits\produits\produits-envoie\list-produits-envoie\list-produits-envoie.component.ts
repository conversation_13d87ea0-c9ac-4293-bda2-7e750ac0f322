import { Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef, ViewChild, TemplateRef, ViewContainerRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommandeService } from '../../../Services/achats/commande.service';
import { Subject } from 'rxjs';
import { PageChangeEvent, GridDataResult, GridComponent } from '@progress/kendo-angular-grid';

import { filter, takeUntil } from 'rxjs/operators';

import { Operateur } from '../../../models/common/operateur.model';
import { BaseRestorableComponent } from 'src/app/shared/base-restorable.component';
import { type } from 'os';
import { title } from 'process';

import { AuthService } from 'src/app/shared/services/auth.service';
import { Principal } from 'src/app/shared/models/principal';
import { log } from 'console';
import { AfterViewInit } from '@angular/core';
import moment from 'moment';
import { Pagination, PaginationAndSorting } from 'src/app/references-app/referential/models/pagination.interface';
import { Filter, Filters, FiltersModalBetaService } from 'src/app/shared/filters/filters-modal-service/filters-modal-beta.service';
import { ProduitSiteCriteria, ProduitSiteCriteriaForm } from '../../../models/produit/ref-produits/ProduitSiteCriteria.model';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { CommunService } from 'src/app/references-app/referential/services/commun.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ProduitService } from '../../../Services/produit/produit.service';
import { ExportPdfService } from 'src/app/shared/export/export-pdf.service';
import { ProduitWinplusCriteria } from '../../../models/produit/ref-produits/produitsWinplus.model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormControl } from '@angular/forms';
import { Noeud } from '../../../models/produit/ref-produits/noeud.model';
import { EnvoiProduitRequest } from '../../../models/produit/ref-produits/envoi-produit-request.model';
import { FournisseurService } from '../../../Services/tiers/fournisseur.service';
import { ProduitSite } from '../../../models/produit/ref-produits/produitSite.model';
@Component({
  selector: 'app-list-produits-envoie',
  templateUrl: './list-produits-envoie.component.html',
  styleUrls: ['./list-produits-envoie.component.scss']
})
export class ListProduitsEnvoieComponent implements OnInit {
  @ViewChild('secondGrid') secondGrid: GridComponent; 
  
  @ViewChild("outlet", { read: ViewContainerRef }) outletRef: ViewContainerRef;
  @ViewChild("sendSelectedGrid", { read: TemplateRef }) contentRef: TemplateRef<any>;

//   statutEnum = StatutCommandeEnum;
//   isLabo: boolean = false;
  destroy$: Subject<boolean> = new Subject<boolean>();

  listProduitsSite: ProduitSite[] = [];
 
  navigation: Pagination = {
    skip: 0,       // Offset for Kendo Grid
    pageSize: 20,  // Number of items per page
  };


get showDeselectAllButton(): boolean {
  return this.availableSites.length > 0 && this.selectedSites.length === this.availableSites.length;
}

get showClearButton(): boolean {
  return this.selectedSites.length > 0 && !this.showDeselectAllButton;
}


// Data for the first grid (Produits Winplus)
  gridData: GridDataResult = {
    data: [],
    total: 0
  };

  // Data for the second grid (Produits à envoyer)
  selectedGridData: GridDataResult = {
    data: [],
    total: 0
  };

  // Array to track selected items
  selectedItems: any[] = [];

  // Modal-related properties
  @ViewChild('configureEnvoiModal') configureEnvoiModal: TemplateRef<any>;
  selectedAction: string = 'Ajout'; // Default to 'Ajout'
  isModification: boolean = false; // Switch state
  selectedSites: string[] = [];
  siteOptions: { value: string; label: string }[] = []; // Format for Select2

  availableSites: Noeud[] = [];

  // Filter reference **
  envoiTypeControl = new FormControl(true);  

  filtersRef: Filters = null;
  filterCriteria: Partial<ProduitSiteCriteriaForm>  = {};
//   principal: Principal;



  constructor(private router: Router, 
    private userServ: UserInputService, private communServ: CommunService, private alertService: AlertService, 
    private route: ActivatedRoute, private modalService: NgbModal, private noeudService: FournisseurService,
    private filtersModalService: FiltersModalBetaService,  private cdr: ChangeDetectorRef,  private userInputService: UserInputService,
    private exportPdfService: ExportPdfService, private productsService: ProduitService,  private authService: AuthService, 
    )  { }



  ngOnInit() {

   this.buildFilterModal();
  
    //  this.loadListProduitsWinplus();
  
    // this.initExport();


  }

  loadAllGrossistes(): void {
    this.productsService.getAllGrossiste().pipe(takeUntil(this.destroy$)).subscribe({
      next: (noeuds) => {
             this.availableSites = noeuds; // Filter out sites without code or name
          // Format sites for Select2
          this.siteOptions = noeuds.map(site => ({
            value: site.code,
            label: site.nom || site.code // Use name if available, else code
          }));
      },
    });
  }

  toggleSiteSelectionFromDropdown(event: any): void {
    this.selectedSites = event.value; // event.value is an array of selected codes
  }
   // Fetch available sites from the API
   // Fetch available sites and format for Select2
   loadAvailableSites(): void {
    this.noeudService.getFournisseurNoeudList()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (noeuds: Noeud[]) => {
          this.availableSites = noeuds.filter(site => site.code != ''); // Filter out sites without code or name
          // Format sites for Select2
          this.siteOptions = noeuds.filter(site => site.code != '').map(site => ({
            value: site.code,
            label: site.nom || site.code // Use name if available, else code
          }));
        }
      });
  }

  // Update selectedAction based on switch state
  updateAction(): void {
    this.selectedAction = this.envoiTypeControl?.value ;
  }

  loadListProduitsSite(): void {
    this.productsService.getProduitsSiteByCriteria(this.navigation , {...this.filterCriteria,codeSite:[0]}).subscribe(
      (page) => {
        this.gridData = {
          data: page.content,
          total: page.totalElements
        };
      }
    );
  }



//   /* -------------------------------------------------------------------------- */
//   /*                 selection process                                          */
//   /* -------------------------------------------------------------------------- */

// Check if an item is selected in the first grid
  isSelected(item: any): boolean {
    return this.selectedItems.some(selected => selected === item);
  }

  isSiteSelected(site: Noeud): boolean {
    return this.selectedSites.includes(site.code); 
  }
  private mapSitesToNoeuds(selectedSiteCodes: string[]): Noeud[] {
    return this.availableSites.filter(site => selectedSiteCodes.includes(site.code));
  }
  // Toggle selection in the first grid
  toggleSelection(item: any): void {
    if (this.isSelected(item)) {
      this.selectedItems = this.selectedItems.filter(selected => selected !== item);
    } else {
      this.selectedItems.push(item);
    }
    this.updateSelectedGrid();
  }

  // Check if an item is selected in the second grid
  isSelectedInSecondGrid(item: any): boolean {
    return this.selectedItems.some(selected => selected.codeWinplus === item.codeWinplus);
  }

  // Toggle selection in the second grid (only removes items)
  toggleSelectionInSecondGrid(event: Event, item: any): void {
    event.stopPropagation(); // Prevent the event from propagating to the next row
  
    const index = this.selectedItems.findIndex(selected => selected.codeWinplus === item.codeWinplus);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
    }
    this.outletRef.clear();
    this.outletRef.createEmbeddedView(this.contentRef);
    this.updateSelectedGrid();
  }

  // Select all items in the first grid
  selectAll(event: any): void {
    if (event.target.checked) {
      this.selectedItems = [...this.gridData.data];
    } else {
      this.selectedItems = [];
    }
    this.updateSelectedGrid();
  }

  // Deselect all items in the second grid
  selectAllSelected(event: any): void {
    if (!event.target.checked) {
      this.selectedItems = [];
      this.updateSelectedGrid();
    }
  }

  // Update the second grid with selected items
  updateSelectedGrid(): void {
    this.selectedGridData = {
      data: [...this.selectedItems],
      total: this.selectedItems.length
    };
  }
// Toggle site selection for checkboxes
toggleSiteSelection(site: Noeud): void {
  const index = this.selectedSites.indexOf(site.code); 
  if (index > -1) {
    this.selectedSites.splice(index, 1);
  } else {
    this.selectedSites.push(site.code);
  }
}
selectAllSites(): void {
  if(this.showDeselectAllButton){ // If all are selected, deselect them
    this.deselectAllSites();
    return;
  }
  this.selectedSites = this.availableSites.map(site => site.code);
}

deselectAllSites(): void {
  this.selectedSites = []; // Clear the selected sites array
}

trackByCodeWinplus(index: number, item: any): string {
  return item.codeWinplus + '-' + index;
}

  // Handle modal confirmation
  confirmEnvoi(modal: any): void {
    if (this.selectedAction && this.selectedSites.length > 0) {
      const request: EnvoiProduitRequest = {
        ajout: this.envoiTypeControl.value, 
        listProduits: this.selectedItems,
        listSites: this.mapSitesToNoeuds(this.selectedSites)
      };
      modal.close(request);
    }
  }
  // Open modal and handle result
  sendSelected(): void {
    this.selectedAction = 'Ajout'; 
    this.isModification = false; 
    this.selectedSites = [];
    // this.loadAvailableSites();
    this.loadAllGrossistes()
    const modalRef = this.modalService.open(this.configureEnvoiModal);
    modalRef.result.then((result: EnvoiProduitRequest) => {
      if (result) {
        // Add confirmation step before sending
        this.userInputService.confirm(
          "Confirmer l'envoi des produits",                        
          "Voulez-vous vraiment envoyer les produits sélectionnés aux sites choisis ?",   
          "Envoyer",                                              
          "Annuler"                                            
        ).then(confirmed => {
          if (confirmed) {
            this.productsService.envoieProduitWinplusToSite(result).subscribe({
              next: () => {
                this.alertService.success("Les produits ont été envoyés aux sites avec succès");
                this.selectedItems = [];
                this.updateSelectedGrid();
              }
            });
          }
        }).catch(() => {
          console.log('Confirmation dismissed'); // Handle cancel or dismiss
        });
      }
    }).catch(() => {
      console.log('Configure modal dismissed'); // Handle configuration modal dismissal
    });
  }

//   /* -------------------------------------------------------------------------- */
//   /*                              pagination event                              */
//   /* -------------------------------------------------------------------------- */
pageChange(event: any): void {
  const newSkip = event.skip !== undefined ? event.skip : event; // Fallback if event is just a number
  const newPageSize = event.pageSize || this.navigation.pageSize; // Keep existing pageSize if not provided

  this.navigation.skip = newSkip;
  this.navigation.pageSize = newPageSize;

  // Only fetch data if filters are applied
  if (this.hasCriteria(this.filterCriteria)) {
    this.loadListProduitsSite();
  }
}

  hasCriteria(filter: Partial<ProduitSiteCriteriaForm>): boolean {
    return Object.keys(filter ?? {}).length > 0;
  }

  



  buildFilterModal() {


    this.filtersRef = this.filtersModalService
      .ref<ProduitSiteCriteriaForm>()
      .addRow([
        new Filter("codeGroupe", "Code Groupe", "text"),   new Filter("codeBarre", "Code Barre", "text")
      ])
      .addRow([
        new Filter("designation", "Désignation", "text"),    
      ])
      // .addRow([
      //   new Filter("dateDFPDu", "Date Création Du", "date"), new Filter("dateDFPAu", "Au", "date")    
      // ])
      .addRow([
        new Filter("dateEnvoieDu", "Date Envoie Du", "date"), new Filter("dateEnvoieAu", "Au", "date")    
      ])
      .addRow([
        new Filter("envoyer", "Envoyé ?", "radio", null, [
          { label: "Oui", value: true },
         { label: "Non", value: false },
      ]),
      ])
     
    
  }


  handleFiltersChange(filterObj: ProduitSiteCriteriaForm): void {
    this.filterCriteria = filterObj ?? {};
    this.navigation.skip = 0; // Reset to first page

    // Only fetch data if filters are non-empty; otherwise, reset grid
    if (this.hasCriteria(this.filterCriteria)) {
      this.loadListProduitsSite();
    } else {
      this.gridData = { data: [], total: 0 }; // Reset grid to empty state
    }
  }



//   /* -------------------------------------------------------------------------- */
//   /*                          SECTION:: EXPORT SECTION                          */
//   /* -------------------------------------------------------------------------- */
//   exportRef: ExportPdf<EnteteCmdAchat>;

//   initExport() {
//     this.exportRef = this.exportPdfService
//       .ref<EnteteCmdAchat>()
//       .setFiltersRef(this.filtersRef)
//       .setTitle('Liste des commandes')
//       .setTableName("Table des Commandes")
//       .addColumn('dateCmd', 'Date Cmd.', { type: 'date', width: 55 })
//       .addColumn('numCmd', 'N° cmd.', {
//         type: "numero",
//         width: 40,
//         transform: (value) => {
//           return value > 0 ? value : null
//         }
//       })
//       .addColumn<Operateur>('operateur', 'Operateur', { keyAccessor: "code", width: 50 })
//       .addColumn('fournisseur', 'Fournisseur', { keyAccessor: ["raisonSociale"] })
//       // .addColumn('totalQtCmd', 'Qu antité Commandée', { type: 'integer' })
//       .addColumn('mntBrutTtc', 'Mnt cmd.', { type: 'decimal', width: 50 })
//       .addColumn('statut', 'Statut', {
//         width: 52,
//         transform: (value) => {
//           return this.statutPipe.transform(value)
//         }
//       })
//     // this.exportRef.setDataObservableFunc(this.commandeServ.listCommandes.bind(this.commandeServ));


//   }


//   fetchBlAndNavigate(commandeId: number): void {
//     this.blService.getAllEnteteBlByCmd(commandeId).subscribe(
//       (bls) => {
//         if (bls && bls.length > 0) {
//           // Navigate to the reception page if BLs are available
//           this.router.navigate(
//             ['/webfix/achat/receptionbl'],
//             { queryParams: { commandeId, returnUrl: this.router.url } }
//           );
//         } else {
//           this.alertServ.warning(
//             "Aucun BL disponible",
//             "Il n'y a aucun bon de livraison disponible pour cette commande."
//           );
//         }
//       }
//     );
//   }
  
  

  ngOnDestroy(): void {
    ///console.log("::: Destroy Comp ")
    this.destroy$.next(true);
    this.destroy$.unsubscribe();
  }





}
