import { Component, HostListener, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import moment from 'moment';
import { GroupByMethodEnum, PeriodCriteria } from 'src/app/shared/period-setter/period.model';
import { BarGraph, LineGraph, PieGraph, } from '../../models/dashboard/graph.model';
import { DashboardService } from '../../Services/dashboard/dashboard.service';
import { locales } from '../../models/dashboard/local.const';
import { DashboardSyntheseVente } from '../../models/dashboard/dashboardSyntheseVente.model';
import { Serie } from '../../models/dashboard/series.model';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {

  constructor(private chartService: DashboardService) { }
  lineChart
  //@HostListener('window:resize', ['$event'])
  period: PeriodCriteria = new PeriodCriteria()
  graphObject: any

  dashboardSyntheseVente: DashboardSyntheseVente

  isRecette: boolean = false

  switchElements = [
    { label: "Chiffre d'affaire", value: false }, { label: "Recette", value: true }
  ]

  dataCA: any[] = []
  dataRecette: any[] = []

  ngOnInit(): void {
    this.initGraph()
    /// set data par defaut
    this.period.dateDebut1 = moment(new Date()).startOf("month").format('YYYY-MM-DD HH:mm:ss')
    this.period.dateFin1 = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    this.period.groupByMethod = GroupByMethodEnum.Journalier
    this.getData()
    /// init graph


  }

  initGraph() {
    this.graphObject = new LineGraph().initGraph()
    window['Apex'] = {
      chart: {

        parentHeightOffset: 0,
        toolbar: {
          show: true
        },
        locales: locales
      },
      grid: {
        padding: {
          left: 0,
          right: 0
        }
      },
      colors: ['#3389b9', '#0acf97', '#fa5c7c', '#ffbc00', '#ff8243', '#a6192e', '#80bc2b'],
    };


    // if (this.isRecette) {
    //   this.graphObject.setGraphTitle("Recette")
    // } else {
    //   this.graphObject.setGraphTitle("Chiffre d'affaire")
    // }

  }

  formChange(per: PeriodCriteria) {
    this.period = per
    this.getData()
  }

  getData() {
    //if (!this.graphObject)

    this.chartService.searchDashboardVente(this.period).subscribe(res => {
      this.dataCA = res.seriesCa.map(val => {
        return {
          name: val?.name,
          data: val.dataPoints.map((point) => {
            //cat1.add(point.label)
            return {
              y: point.value,
              x: point.label,
            }
          })
        }
      })
      //
      this.dataRecette = res.seriesRecette.map(val => {
        return {
          name: val?.name,
          data: val.dataPoints.map((point) => {

            return {
              y: point.value,
              x: point.label,
            }
          })
        }
      })

      if (this.isRecette) {
        this.graphObject.setData(this.dataRecette).setGraphTitle("Recette")
      } else {
        this.graphObject.setData(this.dataCA).setGraphTitle("Chiffre d'affaire")
      }

    })


    this.chartService.syntheseDashboardVente(this.period).subscribe(res => {
      this.dashboardSyntheseVente = res
    })
  }

  /// set axis data
  setXaxis() {

    let mydatesToMax = this.period?.dateFin1
    let mydatesToMin = this.period?.dateDebut1
    /// get max & min dates
    if (this.period.toCompare && this.period?.dateFin2) {
      mydatesToMax = moment(this.period?.dateFin1) > moment(this.period?.dateFin2) ? this.period?.dateFin1 :
        this.period?.dateFin2

    }
    if (this.period.toCompare && this.period?.dateDebut2) {
      mydatesToMin = moment(this.period?.dateDebut1) < moment(this.period?.dateDebut2) ? this.period?.dateDebut1 : this.period?.dateDebut2
    }
    //this.lineChart["xaxis"]['categories'] = this.enumerateDaysBetweenDates(mydatesToMin, mydatesToMax)
  }

  enumerateDaysBetweenDates(startDate, endDate) {
    var dates = [];

    var currDate = moment(startDate).startOf('day');
    var lastDate = moment(endDate).startOf('day');

    while (currDate.add(1, 'days').diff(lastDate) < 0) {
      dates.push(currDate.clone().toDate());
    }

    return dates.map(d => moment(d).format("DD/MM/YYYY"));
  }


  setCategories(cat1, cat2) {
    //console.log('----', [...cat1, ...cat2])
    return [...cat2.map(c => { return moment(c).format('DD/MM/YY') }), ...cat1.map(c => { return moment(c).format('DD/MM/YY') })]
  }

  ///
  typeDataChange(e) {
    if (this.isRecette) {
      this.graphObject.setData(this.dataRecette).setGraphTitle("Recette")
    } else {
      this.graphObject.setData(this.dataCA).setGraphTitle("Chiffre d'affaire")
    }


  }
  ///
  ngOnDestroy(): void {

  }
}
