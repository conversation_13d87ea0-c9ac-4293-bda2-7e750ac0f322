import { TranscoClientService } from './../../../Services/winclient.transcoClient.service';
  import { ChangeDetectorRef, Component, HostListener, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { <PERSON>rid<PERSON>ataR<PERSON>ult, RowClassArgs } from '@progress/kendo-angular-grid';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
 import { Filters, FiltersModalBetaService } from 'src/app/shared/filters/filters-modal-service/filters-modal-beta.service';
import { ClientSite, ClientSiteFilter } from '../../../models/clientSite';
import { ClientSiteService } from '../../../Services/winclient.clientSite.service';
import { WinClientSite } from '../../../models/sites';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AlertService } from 'src/app/shared/services/alert.service';
import { WinclientVilleService } from '../../../Services/winclient.ville.service';
import { WinclientSitesService } from '../../../Services/winclient.sites.service';
import { WinClientVille } from '../../../models/ville';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment-timezone';
import { TranscoClient, TranscoClientCriteria } from '../../../models/transcoClient';
import { ClientGroupeService } from '../../../Services/winclient.clientGroupe.service';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { ClientGroupe, ClientGroupeCriteria } from '../../../models/clientGroupe';
import { WinclientClientAssistanService } from '../../../Services/winclient.clientAssistan';
import { ClientAssistantCriteria, ClientAssistantResponse, MatchAssociation } from '../../../models/clientAssistant';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, of, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, switchMap } from 'rxjs/operators';
import { WinClientLocalite } from '../../../models/localite';
import { WinclientLocaliteService } from '../../../Services/winclient.localite.service';
import { SortDescriptor } from '@progress/kendo-data-query';
 
@Component({
  selector: 'app-list-client-sites',
  templateUrl: './list-client-sites.component.html',
  styleUrls: ['./list-client-sites.component.scss']
})
export class ListClientSitesComponent implements OnInit {
 
  @ViewChild('filterModal') filterModal: TemplateRef<any>;
  @ViewChild('modalClientSiteRefGroupe') modalClientSiteRefGroupe: TemplateRef<any>;
  @ViewChild('modalCreateClientGroupe') modalCreateClientGroupe: TemplateRef<any>;

  createClientGroupeModalRef: NgbModalRef;
  clientSiteFilter : ClientSiteFilter = new ClientSiteFilter({});
  clientSiteFilterForm: FormGroup;
  clientGroupeSearchCriteria : ClientGroupeCriteria = new ClientGroupeCriteria({});
  clientGroupeForm : FormGroup;
  clientGroupeFilterForm : FormGroup;
  villes : WinClientVille[];
  localities: WinClientLocalite[];
  clientSiteSort: SortDescriptor[];
  sites: WinClientSite[] =[];
  clientsGroupe : GridDataResult = {data:[] as ClientGroupe[],total:0};
  ClientGroupeAssociation: any [] =[];
  isLoadingTransco = true;
  isLoadingAssistant = false;
  formSubmited = false;
  clickedCellId: number;
  clickedItem : ClientSite;
  transcoClient : TranscoClient;
  columns = ["cliAdresse","estActifSite","estSignale","tag","cliPatente","cliCategorie","dateMaj"]
  hiddenColumns = new Set<string>(this.columns);

  navigation : Pagination = {
    pageSize :21,
    skip:0,
    sortField: '',
    sortMethod: '',
  }
  clientGroupeNavigation : Pagination = {
    pageSize :21,
    skip:0,
  }

  clientsSite :GridDataResult = {
    data:[] as ClientSite[],
    total:0
  };
  selectedKey: any[]=[];
  isDrawerOpen: boolean =false;
  assistantClientValues: ClientAssistantResponse = null;
  isCollapsedAssistant: boolean =false;
  isCollapsedClientGroupe: any =true;
  isDrawerFilterOpen: any =false;
  isCreateClientGroupeDrawerOpen: boolean = false;
  preventInitialListLoad: boolean = false;


  get isAllColumnsVisible(): boolean {
    return this.hiddenColumns.size === 0;
  }
  get hasHiddenColumns(): boolean {
    return this.hiddenColumns.size > 0;
  }
  
  constructor(
    private clientSiteService: ClientSiteService,
    private fb: FormBuilder,
    private villeService : WinclientVilleService,
    private siteService : WinclientSitesService,
    private clientGroupeService : ClientGroupeService,
    private transcoClientService : TranscoClientService,
    private localiteService : WinclientLocaliteService,
    private inputService : UserInputService,
    private alertService : AlertService,
    private clientAssistanService : WinclientClientAssistanService,
    private activeRoute: ActivatedRoute,
    ) { 
      this.loadHiddenColumns();
    }

  get hasActiveFilters(): boolean {
    const filters = this.cleanFilterQueryParams(this.clientSiteFilter);
    delete filters.page;
    delete filters.size;
    return Object.keys(filters).length > 0;
  }

  ngOnInit() {
    this.initFilterForm();
    this.initClientGroupeForm();
    this.initClientGroupeFilterForm();
    this.initQueryParamListener();
    
    // prevent initial list load if filter is applied
    !this.preventInitialListLoad && this.getListClientSite(0);

    this.getVilles();
    this.getSites();
    this.getLocalities();
  }


    initQueryParamListener(){
      this.activeRoute.queryParams.subscribe(params => {
        const filterNouveauClient = params.filterNouveauClient;
        const interval = params.interval;
        if(filterNouveauClient == "true"){
          this.preventInitialListLoad = true;
           const clientSiteSearchCriteria = new ClientSiteFilter({
            dateDebut : moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'),
            isGrouped : false,
          });
          this.clientSiteFilterForm.patchValue({
            dateDebut: moment(clientSiteSearchCriteria.dateDebut),
            isGrouped: clientSiteSearchCriteria.isGrouped,
            nouveauClient: true,
          });

          if(interval && ['today','7d','15d','1m'].includes(interval)){
            const {dateDebut,dateFin} = this.getIntervalStartEndDate(interval);
            clientSiteSearchCriteria.dateDebut = dateDebut;
            clientSiteSearchCriteria.dateFin = dateFin;
            this.clientSiteFilterForm.patchValue({
              dateDebut: moment(dateDebut),
              dateFin: moment(dateFin),
            });
          }
          this.clientSiteFilter = clientSiteSearchCriteria;
          
          this.getListClientSite(0);
        }
      });
    }


    getIntervalStartEndDate(interval: string) {
      const now = moment();
      let dateDebut: moment.Moment;
      const dateFin = now.clone().endOf('day');
    
      dateDebut = now.clone().subtract(
        interval === 'today' ? 0 :
        interval === '7d' ? 7 :
        interval === '15d' ? 15 :
        interval === '1m' ? 1 : 0,
        interval === '1m' ? 'month' : 'days'
      ).startOf('day');
    
      return {
        dateDebut: dateDebut.format('YYYY-MM-DD HH:mm:ss'),
        dateFin: dateFin.format('YYYY-MM-DD HH:mm:ss'),
      }
    }


    initClientGroupeFilterForm(){
      this.clientGroupeFilterForm = this.fb.group({
        codeClientGroupe: [''],
        raisonSociale: [''],
        nomPharmacien: [''],
        ville: [null],
        adresse:['']
      });
    }

    initClientGroupeForm(){
      this.clientGroupeForm = this.fb.group({
        id:[null],
        codeClientGroupe: [null],
        raisonSociale: ['', Validators.required],
        nomDuPharmacien: ['', Validators.required],
        adresse: ['', Validators.required],
        adresseComplement: [''],
        localite: [''],
        ville: [null, Validators.required],
        telephone: ['', Validators.required],
        telephone2: [null],
        gsm : [null],
        whatsapp : [null],
        ice: [null],
        email: [null, [Validators.email]],
        patente: [''],
        inpe: [null],
        longitude: [null],
        latitude : [null]
      });
    }

  initFilterForm(){
    this.clientSiteFilterForm = this.fb.group({
      codeGroupe: [''],
      codeLocal: [''],
      raisonSociale: [''],
      nomPharmacien: [''],
      site: [null],
      ville: [null],
      isGrouped: [null],
      signalement: [null], 
      nouveauClient: [null],
      dateDebut: [null],
      dateFin: [null],
      tagGroupement: [''],
      tagClient: ['']
    });

    this.clientSiteFilterForm.get("dateDebut")?.disable();
    this.clientSiteFilterForm.get("dateFin")?.disable();
    this.clientSiteFilterForm.get("nouveauClient")?.valueChanges.subscribe(res => {
      if(!res){       
       this.clientSiteFilterForm.get("dateDebut")?.disable();
       this.clientSiteFilterForm.get("dateFin")?.disable();
      }else{
        this.clientSiteFilterForm.get("dateDebut")?.enable();
        this.clientSiteFilterForm.get("dateFin")?.enable();
      }
     })
  }

  buildSortField(){
    return this.clientSiteSort && this.clientSiteSort.length > 0 && this.clientSiteSort[0].dir ? `${this.clientSiteSort[0].field},${this.clientSiteSort[0].dir}` : null;
  }


  getListClientSite(page=0){
    this.navigation.skip = page * this.navigation.pageSize;
    this.clientSiteFilter.page = page;
    this.clientSiteFilter.size = this.navigation.pageSize;
    const sort = !this.buildSortField() ? {} : {sort:this.buildSortField()};
    this.clientSiteService.searchClientSite(this.clientSiteFilter,sort).subscribe(res => {
      this.clientsSite.data = res.content;
      this.clientsSite.total = res.totalElements;
      this.fillClientSiteWithLabelSite();
    });

  }

  getLocalities(){
    this.localiteService.getAllLocalites().subscribe(res => {
      this.localities = res;
    });
  }

  searchClientSiteTranscodage(clientSite:ClientSite){
    
    const transcoClientCriteria = new TranscoClientCriteria({
      codeClientSite: clientSite.cliCode,
      codeSite: clientSite.codeSite
    });

    this.isLoadingTransco = true;

    this.transcoClientService.searchTranscoClient(transcoClientCriteria).subscribe(res => {
      if(res.length > 0){
        this.transcoClient = res[0];
        this.clientGroupeService.searchClientGroupe({codeClientGroupe:res[0].codeClientGroupe}).subscribe({
          next: res => {
            this.ClientGroupeAssociation =[ res.content[0]];
            this.isLoadingTransco = false;
          },
          complete: () => {
            this.isLoadingTransco = false;
          }
        });
      }else{
        // console.log("No TRansco Found");
        // this.getClientAssistantResponse(clientSite);
        if(this.clientsGroupe.data.length != 0 && this.clientsGroupe.data.length !=  1 ){
          this.isLoadingTransco = false;
          return;
        }
        this.clientGroupeService.searchClientGroupe({page:0,size:21}).subscribe({
          next: res => {
            this.clientsGroupe.data = res.content;
            this.clientsGroupe.total = res.totalElements;
          },
          complete: () => {
            this.isLoadingTransco = false;
          }
        })
      }
    });
  }
  moveToClientGroupe(match:MatchAssociation){
    const clientGroupeCriteria = new ClientGroupeCriteria({
      codeClientGroupe: +match.code_client_groupe,
    });
    this.clientGroupeService.searchClientGroupe(clientGroupeCriteria).subscribe(res => {
      this.clientsGroupe.data = res.content;
      this.clientsGroupe.total = res.totalElements;
      if(res.content.length > 0){
        this.isCollapsedAssistant = true;
        this.isCollapsedClientGroupe = false;
      }
    });

  }

  clientHaveTransco(){
    return this.ClientGroupeAssociation.length > 0
  }

  formatter = (x: WinClientVille) => x.libelle;


  searchVilleTypeahead: OperatorFunction<string, readonly WinClientVille[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      filter((term) => term.length >= 2),
      switchMap((term) => {
       const targetVilles =  this.villes.filter((v) => v.libelle.toLowerCase().indexOf(term.toLowerCase()) > -1);
        return of(targetVilles);
      })
    )
  searchSiteTypeahead: OperatorFunction<string, readonly WinClientSite[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      filter((term) => term.length >= 2),
      switchMap((term) => {
       const targetSites =  this.sites.filter((v) => v.libelleCourt.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.libelleLong.toLowerCase().indexOf(term.toLowerCase()) > -1);
        return of(targetSites);
      })
    )


    searchLocaliteTypeahead: OperatorFunction<string, readonly WinClientLocalite[]> = (
        text$: Observable<string>
      ) =>
        text$.pipe(
          debounceTime(200),
          distinctUntilChanged(),
          filter((term) => term.length >= 2),
          switchMap((term) => {
           const targetLocalite =  this.localities.filter((v) => v.libLocalite.toLowerCase().indexOf(term.toLowerCase()) > -1);
            return of(targetLocalite);
          })
        )
    
      formatterLocalite = (localite: WinClientLocalite) =>  localite.libLocalite;

    formatterSite = (site: WinClientSite) => site.libelleCourt;

    onFilterSubmit(){
      console.log("Form Before filter",this.clientSiteFilterForm)
      const clientSiteSearchCriteria = this.FilterToClientSiteCriteria();
      const cleanedClientSiteSearchCriteria = this.cleanFilterQueryParams(clientSiteSearchCriteria);
      this.clientSiteFilter = cleanedClientSiteSearchCriteria;
      console.log("Search Client Site Criteria",cleanedClientSiteSearchCriteria);
      this.clientSiteService.searchClientSite(cleanedClientSiteSearchCriteria).subscribe((res)=>{
          this.clientsSite.data = res.content;
          this.clientsSite.total = res.totalElements;
          this.fillClientSiteWithLabelSite();
          this.toggleFilterDrawer(false);
      });
  
    }
    filterClientGroupe(){
      this.clientGroupeSearchCriteria = new ClientGroupeCriteria({
        codeClientGroupe: this.clientGroupeFilterForm.value.codeClientGroupe,
        raisonSociale: this.clientGroupeFilterForm.value.raisonSociale,
        nomPharmacien: this.clientGroupeFilterForm.value.nomPharmacien,
        villeLibelle: this.clientGroupeFilterForm.value.ville?.libelle,
        adress: this.clientGroupeFilterForm.value.adresse,
      })

      const cleanedCriteria = this.cleanFilterQueryParams(this.clientGroupeSearchCriteria);

      this.clientGroupeService.searchClientGroupe(cleanedCriteria).subscribe(res => {
        this.clientsGroupe.data = res.content;
        this.clientsGroupe.total = res.totalElements
      });
    }

    filterCommand(command:"reset"|"search"|"refresh"){

      switch(command){
        case "reset":
          this.clientSiteFilterForm.reset();
          this.clientSiteFilter = new ClientSiteFilter({});
          this.isDrawerFilterOpen = false;
          this.getListClientSite(0);
          break;
        case "search":
          this.clientSiteFilter  = this.cleanFilterQueryParams(this.FilterToClientSiteCriteria());
          this.getListClientSite(0);
          break;
        case "refresh":
          this.getListClientSite(0);
          break;
      }

    }

    toggleAssistantAI(){
      this.isCollapsedAssistant = !this.isCollapsedAssistant;
        this.isCollapsedClientGroupe = true;
      console.log("isCollapsedAssistant",this.isCollapsedAssistant)
    }
    toggleClientGroupeFilter(){
      this.isCollapsedClientGroupe = !this.isCollapsedClientGroupe;
      this.isCollapsedAssistant = true;
      console.log("isCollapsedClientGroupe",this.isCollapsedClientGroupe)
    }

    toggleClientGroupeDrawer(forcedValue=null){
      this.isCreateClientGroupeDrawerOpen = forcedValue ?? !this.isCreateClientGroupeDrawerOpen;
      if(!this.isCreateClientGroupeDrawerOpen){
        this.clientGroupeForm.reset();
        this.formSubmited = false;
      }
    }

    onSubmitCreateClientGroupe(){
      this.formSubmited = true;
      if(!this.clientGroupeForm.valid){
        this.alertService.error("Veuillez remplir tous les champs");
        return;
      }
     const clientGroupe = this.formToClientGroupe();
     this.clientGroupeService.createClientGroupe(clientGroupe).subscribe(res => {
      this.alertService.success("Client Groupe Créé avec succès");

      const transcoClient = new TranscoClient({
        codeClientGroupe: res.codeClientGroupe,
        codeClientSite: this.clickedItem.cliCode,
        codeSite: this.clickedItem.codeSite
      });
      this.transcoClientService.createTranscoClient(transcoClient).subscribe((res)=>{
            this.createClientGroupeModalRef?.close();
            this.alertService.success("Client Groupe Associé avec succès");
            this.toggleClientGroupeDrawer(false);
      });
  
     });
    }

    isColumnHidden(columnName: string): boolean {
      return this.hiddenColumns.has(columnName);
    }
    toggleColumnVisibility(columnName: string): void {
      if (this.hiddenColumns.has(columnName)) {
        this.hiddenColumns.delete(columnName);
        this.saveHiddenColumns();

      } else {
        this.hiddenColumns.add(columnName);
        this.saveHiddenColumns();

      }
    }
    saveHiddenColumns(){
      const hiddenColumns = Array.from(this.hiddenColumns);
      localStorage.setItem('hiddenColumns', JSON.stringify(hiddenColumns));
    }

    loadHiddenColumns(){
      const hiddenColumns = localStorage.getItem('hiddenColumns');
      if (hiddenColumns && Array.isArray(JSON.parse(hiddenColumns))) {
        this.hiddenColumns = new Set<string>(JSON.parse(hiddenColumns));
      }
    }

    getColumnTitle(columnName: string): string {
      const columnTitles: { [key: string]: string } = {
        estActifSite: 'Actif',
        estSignale: 'Signalé',
        tag: 'Tag',
        cliPatente: 'Patente',
        cliCategorie: 'Catégorie',  
        dateMaj: 'Date Maj',
        cliAdresse: 'Adresse',
      }

      return columnTitles[columnName] || columnName;
    }

    onMarkerSelected(event:number[]){
      console.log("Mark Selected",event);
      this.clientGroupeForm.patchValue({
        longitude: event[0],
        latitude: event[1]
      })
    }
    

    detacherClientTransco(){
      console.log("detacherClientTransco ",this.clickedItem ," TransoClient ",this.transcoClient);
      this.inputService.confirm(`Détacher Client ${this.clickedItem?.cliNomPhar}`,"Voulez-vous vraiment supprimer cette liaison ?","Détacher","Annuler").then((res) => {

        this.deleteTranscoClient(this.transcoClient.id);
        // this.getClientAssistantResponse(this.clickedItem);
        this.ClientGroupeAssociation = [];
        this.isCollapsedAssistant = false;
        this.isCollapsedClientGroupe = true;
        this.assistantClientValues = null;
        this.alertService.success("La Laison a été supprimée avec succès");
      }
  ).catch(() => {});
          
    
    }

    attacherGroupeClient(dataItem:ClientGroupe,mode:string = 'create'){
      if(!dataItem){
        this.alertService.warning("Attention, choisir un client groupe pour associer")
        return;
      }
      console.log({
        clientSite: this.clickedItem,
        transcoClient: this.transcoClient,
        clientGroupe: dataItem,
        mode
      });
      const transcoClient = new TranscoClient({
          backupCodeClientGroupe: dataItem.codeClientGroupe,
          codeClientGroupe :  dataItem.codeClientGroupe,
          codeClientSite:this.clickedItem.cliCode,
          codeSite:this.clickedItem.codeSite,
      })
      console.log("Transco Requested to Create :",transcoClient)
      this.transcoClientService.createTranscoClient(transcoClient).subscribe({
        next:(value)=> {
          this.ClientGroupeAssociation = [dataItem];
          this.transcoClient = value;
          this.selectedKey = [];
          // this.createClientGroupeModalRef?.close();
          this.alertService.success("Client associé avec succès");
        },

      })
    }


    clientHasAssociation(dataItem: ClientSite): Promise<boolean> {
      return new Promise((resolve) => {
        this.transcoClientService.searchTranscoClient({
          codeClientSite: dataItem.cliCode,
          codeSite: dataItem.codeSite
        }).subscribe({
          next: (res) => {
            resolve(res && res.length > 0);
          },
          error: () => {
            resolve(false);
          }
        });
      });
    }

    async openCreateClientGroupeDrawer(dataItem:ClientSite){
      if(await this.clientHasAssociation(dataItem)){
        this.alertService.warning("Ce client est déjà associé à un Client Groupe");
        return;
      }
      this.clickedItem = dataItem;
      this.clearClickedItem();
      dataItem['isClicked'] = true;
      const ville = this.villes.find(ville => ville.libelle === dataItem.cliVille);
      this.clientGroupeForm.patchValue({
        raisonSociale: dataItem.cliRaiSoc,
        nomDuPharmacien: dataItem.cliNomPhar,
        adresse: dataItem.cliAdresse,
        adresseComplement: '',
        localite: dataItem.cliLocalite,
        ville: ville,
        telephone: dataItem.cliTel,
        patente: dataItem.cliPatente,
      });
      this.isCreateClientGroupeDrawerOpen = true;
    }
    
    openFilterDrawer(){
      this.isDrawerFilterOpen = true;
      setTimeout(() => {
        (document.querySelector('#codeGroupe') as HTMLInputElement)?.focus();
      }, 200);
    }

    @HostListener('document:keydown.escape', ['$event'])
    onClose(event: MouseEvent) {
        this.isDrawerOpen = false;
        this.clickedItem = null;
        this.ClientGroupeAssociation = [];
        this.assistantClientValues = null;
     }
    openDrawer(){
      this.isDrawerOpen = true;  
    }

    toggleDrawer(forcedValue?: boolean){
      this.isDrawerOpen = forcedValue ?? !this.isDrawerOpen;
      if(!this.isDrawerOpen){
        this.clickedItem = null;
        this.ClientGroupeAssociation = [];
        this.assistantClientValues = null;
      }
    }


    toggleFilterDrawer(forcedValue?: boolean){
      this.isDrawerFilterOpen = forcedValue ?? !this.isDrawerFilterOpen;
    }
    toggleDrawerFilterWhenClickOutside(){
      console.log("Click Outside",this.isDrawerFilterOpen)  
      if(this.isDrawerFilterOpen){
        this.toggleFilterDrawer(false);
      }
    }

    openAssociationModalFromQueryParam(codeClientSite: string){
      const dataItem = this.clientsSite.data.find(item => item.cliCode === codeClientSite);
      this.openAssociationGroupeModal(dataItem)
    }

    openAssociationGroupeModal(dataItem : ClientSite){
      this.openDrawer();
    
      // this.router.navigate(["/references/ref-clients/client-sites"], {queryParams: {cliCode: dataItem.cliCode}});
      this.clickedItem = dataItem;
      this.clearClickedItem();
      dataItem['isClicked'] = true;
      this.searchClientSiteTranscodage(dataItem);
      // const modal =  this.modalService.open(this.modalClientSiteRefGroupe,{size:'xl'});
      // modal.closed.subscribe(result => {
      //   setTimeout(() => {
      //     this.ClientGroupeAssociation = [];
      //     this.clickedItem = null;
      //   }, 200);
      // });
      // modal.dismissed.subscribe(result => {
      //   setTimeout(() => {
      //     this.ClientGroupeAssociation = [];
      //     this.clickedItem = null;
      //   }, 200);

      // });
    }

    clearClickedItem(){
      this.clientsSite.data.forEach(item => {
       delete  item['isClicked'];
      });
    }

    
    onFilterReset(){
      this.clientSiteFilterForm.reset();
    }


    formToClientGroupe(){
      const clientGroupe = new ClientGroupe({
        raisonSociale: this.clientGroupeForm.value.raisonSociale,
        nomPharmacien: this.clientGroupeForm.value.nomDuPharmacien,
        adresse1: this.clientGroupeForm.value.adresse,
        adresse2: this.clientGroupeForm.value.adresseComplement,
        localite: this.clientGroupeForm.value.localite?.libLocalite,
        ville: this.clientGroupeForm.value.ville,
        telephone: this.clientGroupeForm.value.telephone,
        telephone2: this.clientGroupeForm.value.telephone2,
        gsm: this.clientGroupeForm.value.gsm,
        whatsapp: this.clientGroupeForm.value.whatsapp,
        ice: this.clientGroupeForm.value.ice,
        email: this.clientGroupeForm.value.email,
        patente: this.clientGroupeForm.value.patente,
        inpe: this.clientGroupeForm.value.inpe,
        longitude: this.clientGroupeForm.value.longitude,
        latitude: this.clientGroupeForm.value.latitude
      });
      return clientGroupe;
    }

  
  
    FilterToClientSiteCriteria () {
      const clientSiteSearchCriteria = new ClientSiteFilter({
        codeGroupe: this.clientSiteFilterForm.value.codeGroupe,
        codeLocal: this.clientSiteFilterForm.value.codeLocal,
        raisonSociale: this.clientSiteFilterForm.value.raisonSociale,
        nomPharmacien: this.clientSiteFilterForm.value.nomPharmacien,
        codeSite: this.clientSiteFilterForm.value.site?.id,
        ville: this.clientSiteFilterForm.value.ville?.libelle,
        isGrouped: this.clientSiteFilterForm.value.isGrouped,
        signalement: this.clientSiteFilterForm.value.signalement,
        dateDebut: this.isValidateDate(this.clientSiteFilterForm.value.dateDebut) ?   moment(this.clientSiteFilterForm.value.dateDebut).format('YYYY-MM-DD HH:mm:ss') :null,
        dateFin:  this.isValidateDate(this.clientSiteFilterForm.value.dateFin) ?   moment(this.clientSiteFilterForm.value.dateFin).format('YYYY-MM-DD HH:mm:ss') :null,
        tagClient: this.clientSiteFilterForm.value.tagClient,
        tagGroupement: this.clientSiteFilterForm.value.tagGroupement,
      })
      return clientSiteSearchCriteria;
    }

    isValidateDate(date :string){
      return moment(date).isValid() && typeof date != 'undefined' && date != null
    }
  

  cleanFilterQueryParams(filters:  Partial<ClientSiteFilter>) {
    // remove undifined,null values and empty strings
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined || filters[key] === '' || filters[key] === null || (typeof filters[key] === 'string' && filters[key].trim() === '')) {
        delete filters[key];
      }
    });
    return filters;
  }


  
  
  compareVille(ville1:WinClientVille , ville2:WinClientVille){
    return ville1 && ville2 && ville1.id === ville2.id;
  }
  


    pageChange(skip:number) {
      this.navigation.skip =skip;
      const page = Math.ceil(skip / this.navigation.pageSize);
      this.getListClientSite(page);
    }

    clientGroupePageChange(skip:number) {
      this.clientGroupeNavigation.skip =skip;
      const page = Math.ceil(skip / this.clientGroupeNavigation.pageSize);
      this.clientGroupeService.searchClientGroupe({...this.clientGroupeSearchCriteria,page:page,size:this.clientGroupeNavigation.pageSize,}).subscribe(res => {
        this.clientsGroupe.data = res.content;
        this.clientsGroupe.total = res.totalElements;

      });
    }

    rowClass(args:RowClassArgs){
      if(args.dataItem?.isClicked){
        return {'highlight-row-clicked':true};
      }
      return '';
    }


    getVilles(){
      this.villeService.getAllVilles().subscribe(res => {
        this.villes = res.content;
      });
    }


    getSites(){
      this.siteService.getAllSites().subscribe(res => {
        this.sites = res.sort((a,b)=> a.id - b.id);
        this.fillClientSiteWithLabelSite();
      });
    }

    searchClientGroupeByTac(){
      this.getClientAssistantResponse(this.clickedItem);
    }


    private getClientAssistantResponse(clientSite: ClientSite){
      
      const clientAssistanCriteria = new ClientAssistantCriteria({
        nom_pharmacien: clientSite.cliNomPhar,
        raison_sociale: clientSite.cliRaiSoc,
        code_site: clientSite.codeSite,
        code_client: clientSite.cliCode,
        ville: clientSite.cliVille
      });
      this.isLoadingAssistant = true;
      this.clientAssistanService.searchClientAssistan([clientAssistanCriteria]).subscribe(
        {
          next: (res) => {
            const clientgroupeMatches = res[0].matches.map((match)=> new ClientGroupe({
              codeClientGroupe: +match.code_client_groupe,
              raisonSociale: match.raison_sociale,
              nomPharmacien: match.nom_pharmacien,
              adresse1: match.adresse1,
              adresse2: match.adresse2,
              localite: match.localite,
              ville: {libelle: match.ville},
              telephone: match.telephone,

            }))
            this.clientsGroupe.data = clientgroupeMatches;
            this.clientsGroupe.total = clientgroupeMatches.length;
            this.isLoadingAssistant = false;
          },
          complete() {
            this.isLoadingAssistant = false;
          },
        }
      );
    }

    private fillClientSiteWithLabelSite(){
      this.clientsSite.data.forEach(clientSite => {
        clientSite['siteLabel'] = this.sites.find(site => site.id === clientSite.codeSite)?.libelleCourt;
      }); 
      this.clientsSite.data = [...this.clientsSite.data];
    }

    private deleteTranscoClient(id:number){
      this.transcoClientService.deleteTranscoClient(id).subscribe(res => {
        this.alertService.success("La suppression de laison a été exécutée avec succès.");
        // this.getListClientSite();
        this.clientGroupeService.searchClientGroupe({page:0,size:21}).subscribe({
          next: res => {
            this.clientsGroupe.data = res.content;
            this.clientsGroupe.total = res.totalElements;

          },
        })
      });
    }

    onSortChange(sort: SortDescriptor[]){
      this.clientSiteSort = sort;
      if(this.clientSiteSort && this.clientSiteSort.length > 0 && this.clientSiteSort[0].dir){
        this.navigation.sortField = sort[0].field;
        this.navigation.sortMethod = sort[0].dir;
        this.getListClientSite();
      }else{
        this.navigation.sortField = null;
        this.navigation.sortMethod = null;
        this.getListClientSite();
      }
     }
    
  }
