import { TypeTableau } from 'src/app/references-app/references-produits/enums/produit/TypeTableau.enum';

import { Atc } from 'src/app/references-app/references-produits/models/produit/medical/atc.model';
import { FamilleMedicamenteuse } from 'src/app/references-app/references-produits/models/produit/medical/familleMedicamenteuse.model';
import { ProduitComplementPharma } from 'src/app/references-app/references-produits/models/produit/medical/produitComplementPharma.model';


export class ProduitBase { 
    atc?: Atc;
    audited?: boolean;
    familleMedicamenteuse?: FamilleMedicamenteuse;
    id?: number;
    nomRacine?: string;
    produitcomplementpharma?: ProduitComplementPharma;
    psychotrope?: boolean;
    tableau?: TypeTableau;
    userModifiable?: boolean;
}

