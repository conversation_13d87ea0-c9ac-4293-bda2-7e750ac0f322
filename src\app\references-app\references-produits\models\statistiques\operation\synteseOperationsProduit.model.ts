
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { SyntheseOperation } from './syntheseOperation.model';
import { SyntheseStock } from 'src/app/references-app/references-produits/models/statistiques/stock-inventaire/syntheseStock.model';


export class SynteseOperationsProduit { 
    codeProduit?: string;
    designationProduit?: string;
    forme?: FormeProduit;
    partieStock?: SyntheseStock;
    synteseAchats?: SyntheseOperation;
    synteseAvoirsFournisseur?: SyntheseOperation;
    synteseAvoirsInternes?: SyntheseOperation;
    synteseEchangesEntree?: SyntheseOperation;
    synteseEchangesSortie?: SyntheseOperation;
    synteseVentes?: SyntheseOperation;
}
