import { TypeBla } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeBla.enum';
import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';
import { TypeStatutReglement } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeStatutReglement.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { Produit } from '../../produit/base/produit.model';


export class StatistiquesAchatsCriteria {
    categorieProduit?: CategorieProduit;
    dateDebut?: any;
    dateFin?: any;
    etatReglementFacture?: TypeStatutReglement;
    familleTarifaire?: FamilleTarifaire;
    forme?: FormeProduit;
    fournisseur?: Fournisseur;
    operateur?: Operateur;
    statut?: Statut;
    typeBla?: TypeBla;
    produit?: Produit;

    produitIdExistsDetail?: number
}

