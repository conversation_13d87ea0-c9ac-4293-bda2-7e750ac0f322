import { ReglementFactureStatutCriteria } from 'src/app/references-app/references-produits/enums/achat-avoir/ReglementFactureStatutCriteria.enum';

// import { Moment } from 'moment';

import { EnteteFactureAchat } from 'src/app/references-app/references-produits/models/achat-avoir/facture/enteteFactureAchat.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';
import { Fournisseur } from '../../tiers/fournisseur/fournisseur.model';
import { ModePaiement } from 'src/app/references-app/references-produits/enums/common/ModePaiement.enum';


export class ReglementFactureAchatCriteria {
    dateDebut?: any;
    dateFin?: any;
    enteteFactureAchat?: EnteteFactureAchat;
    fournisseurId?: number;
    operateur?: Operateur;
    reference?: string;
    statut?: ReglementFactureStatutCriteria;
    fournisseur?: Fournisseur;

    /// test
    modePaiement?: ModePaiement
}

