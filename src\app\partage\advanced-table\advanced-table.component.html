<form #displayForm="ngForm" class="my-3" *ngIf="isSearchable">
    <div class="row mb-2">
        <div class="col-sm-12 col-md-6" *ngIf="pageSizeOptions.length">
            <div class="text-center text-sm-start">
                <label class="form-label me-1">Show
                    <select class="form-control-sm ps-1 fc-p4  form-select w-auto ms-1 me-1 d-inline" name="pageSize"
                        id="event-category" required [(ngModel)]="service.pageSize" (ngModelChange)="paginate()">
                        <option *ngFor="let option of pageSizeOptions" [value]="option">{{option}}</option>
                    </select>
                    entries</label>
            </div>
        </div><!-- end col-->
        <div class="col-sm-12 col-md-6">
            <div class="text-sm-end d-flex align-items-center justify-content-end">
                <label class="d-inline me-1">Search:
                    <input type="text" name="searchTerm" class="form-control form-control-sm ps-1 fc-p4  d-inline w-auto"
                        aria-controls="advanced-table" [(ngModel)]="service.searchTerm" (ngModelChange)="searchData()">
                </label>
            </div>
        </div><!-- end col-->
    </div>
    <!-- end row -->
</form>

<div class="table-responsive">
    <table id="advancd-table" class="table advanced-table table-nowrap  w-100" [class]="tableClasses" #advancedTable>
        <thead [class]="theadClasses">
            <tr *ngIf="isSortable">
                <th *ngIf="hasRowSelection" class="row_select">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="flexCheckIndeterminate"
                            [checked]="selectAll" [indeterminate]="checkIntermediate()" (change)="selectAllRow($event)">
                    </div>
                </th>

                <ng-container *ngFor="let column of columns">
                    <th class="text-capitalize sortable pe-3 pe-lg-auto" [style.width.px]="column.width"
                        [sortable]="column.name" (sort)="onSort($event)" *ngIf="column.sort!=false">
                        {{column.name}}</th>
                    <th class="text-capitalize" [style.width.px]="column.width" *ngIf="column.sort===false">
                        {{column.name}}</th>
                </ng-container>
            </tr>
            <tr *ngIf="!isSortable">
                <th *ngIf="hasRowSelection" class="row_select">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="flexCheckIndeterminate">
                    </div>
                </th>
                <th *ngFor="let column of columns" [style.width.px]="column.width" class="text-capitalize">
                    {{column.name}}</th>
            </tr>

        </thead>
        <tbody>
            <tr *ngFor="let record of tableData | slice: (service.page-1) * service.pageSize : service.page * service.pageSize;let i=index"
                [class.selected]="isSelected[i+((service.page-1) * service.pageSize)]">

                <td *ngIf="hasRowSelection">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox"
                            [checked]="isSelected[i+((service.page-1) * service.pageSize)]" value=""
                            id="flexCheckIndeterminate" (change)="selectRow(i+((service.page-1) * service.pageSize))">
                    </div>
                </td>
                <td *ngFor="let column of columns">
                    <div [innerHtml]="callFormatter(column,record)"></div>
                </td>
            </tr>
        </tbody>
    </table>


    <div class="d-flex flex-wrap justify-content-between p-2" *ngIf="pagination">
        <div>
            Showing {{service.startIndex }} to
            {{service.endIndex}} of {{service.totalRecords}} entries
        </div>
        <ngb-pagination class="pagination-rounded" [collectionSize]="service.totalRecords" [(page)]="service.page"
            [maxSize]="3" [rotate]="true" [pageSize]="service.pageSize" (pageChange)="paginate()">
        </ngb-pagination>
    </div>

</div>