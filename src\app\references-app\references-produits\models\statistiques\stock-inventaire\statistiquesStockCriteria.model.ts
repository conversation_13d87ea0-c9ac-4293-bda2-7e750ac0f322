import { TypeDocument } from 'src/app/references-app/references-produits/enums/common/TypeDocument.enum';
import { SituationProduit } from 'src/app/references-app/references-produits/enums/statistiques/SituationProduit.enum';
import { TypeStatistiqueCriteria } from 'src/app/references-app/references-produits/enums/statistiques/TypeStatistiqueCriteria.enum';
import { TypeTri } from 'src/app/references-app/references-produits/enums/statistiques/TypeTri.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/references-app/references-produits/models/produit/base/categorieProduit.model';
import { Depot } from 'src/app/references-app/references-produits/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/references-app/references-produits/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/references-app/references-produits/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Produit } from 'src/app/references-app/references-produits/models/produit/base/produit.model';
import { Rayon } from 'src/app/references-app/references-produits/models/produit/base/rayon.model';
import { TypeAlerteStock } from 'src/app/references-app/references-produits/enums/statistiques/TypeAlerteStock.enum';


export class StatistiquesStockCriteria {
    categorie?: CategorieProduit;
    dateDebut?: any;
    dateFin?: any;
    dateInventaire?: any;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    listeCategories?: CategorieProduit[]
    laboratoire?: Fournisseur;
    nbrJour?: number;
    produit?: Produit;
    rayon?: Rayon;
    situationProduit?: SituationProduit;
    tempsReel?: boolean;
    typeOperation?: TypeDocument;
    typeStatistique?: TypeStatistiqueCriteria;
    typeTri?: TypeTri;
    zone?: Depot;

    negatifOnly?: boolean

    datePeremptionMax?: string;


    typeAlerteStock?: TypeAlerteStock;
    seuilAlertStock?: number;

    dateCriteriaAlerteStock?: any

    designationProduit?: string

    /// for FE
    typeImpression?: TypeImpression = TypeImpression.liste


    signStockExpression?: string

}

export enum TypeImpression {
    liste = "liste",
    courrier = "courrier"
}



