import { TypeFournisseur } from 'src/app/references-app/references-produits/enums/tiers/TypeFournisseur.enum';

import { Ville } from 'src/app/references-app/references-produits/models/common/ville.model';


export class FournisseurCriteria { 
    audited?: boolean;
    codeFournisseur?: string;
    estActif?: boolean;
    raisonSociale?: string;
    typeFrn?: TypeFournisseur;
    userModifiable?: boolean;
    ville?: Ville;
}

