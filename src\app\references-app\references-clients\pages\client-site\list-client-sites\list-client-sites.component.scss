
#magnifier{
animation: magnifier-rotate 1s linear infinite alternate;
transform-origin: 60% 60%;
}

@keyframes magnifier-rotate {
  0%{
    transform: rotate(-70deg) scale(0.7) translateX(-20px);

  }
  100%{
    transform: rotate(90deg) scale(1.3);
  }
}

.map-container {
  height: 400px;
  width: 100%;
}


::ng-deep .k-grid .sicky-to-right {

  position: sticky;
  right: 0;
  z-index: 1;
  background-color: white;
  // box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  &-header{
    right: 0;
    background:#ae3536 !important; ;
  }
}
