<div  class="row">
  <div class="col-lg-6 left-homelanding px-0">
    <div class="
        left-homelandingcont
        h-100
        d-flex
        flex-column
        align-content-between
      ">

      <div class="
          left-homelandingcontup
          bg-white
          position-relative
          px-3 px-md-4
          py-4
          p-md-5 p-lg-4
        " style="min-height: 100%; display: grid; place-items: center">
        <div class="blurred-img d-none d-lg-block"></div>
        <h3 class="display-4 position-relative" style="z-index: 1; max-width: 520px">
          Avec WinRefs, atteignez le succès sans efforts inutiles.
        </h3>

        <div class="
            left-homelandingcontdown
            bg-white
            d-block d-lg-none
            pt-4 pt-md-5 pt-lg-4
          ">
          <div class="left-homelandingcontdown-body lead mx-auto" style="max-width: 520px">
            <p class="position-relative">
              Des petites tâches aux grandes initiatives, Asana aide les équipes
              à organiser leur travail de façon à savoir quoi faire, pourquoi et
              comment.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-lg-6 d-flex flex-column justify-content-center" style="background-color: #f3f3f3">
    <div class="loginform d-block overflow-hidden">
      <div class="formcard position-relative my-4 my-lg-0 mx-2">
        <div class="loginformbg d-none d-md-block hidden"></div>
        <div class="card position-relative my-4 my-lg-0" style="z-index: 1">
          <!-- Logo -->
          <div class="card-header py-2 text-center" style="background: #540B0E;">
            <span>
              <img src="assets/images/logo-winref-white.png" alt="" height="70" />
            </span>
          </div>


          <div class="card-body-cus py-4 px-3 px-md-4">
            <div class="text-center w-75 m-auto">
              <h4 class="text-dark-50 text-center mt-0 font-weight-bold">
                S'identifier
              </h4>
             
            </div>
            <form autocomplete="off" name="login-form" [formGroup]="loginForm" (ngSubmit)="login() ">
              <!-- <app-ui-preloader [display]="loading"></app-ui-preloader> -->
              <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{error?.error?.message}}</ngb-alert>

              <div class="mb-3">
                <label></label>

                <label for="emailaddress" class="form-label">Identifiant d'utilisateur</label>
                <input autocomplete="off" tabindex="1" class="form-control" type="text" id="emailaddress"
                  placeholder="Entrer votre Email" formControlName="email" [ngClass]="{
                    'is-invalid': formSubmitted && formValues.email.invalid
                  }" required />

                <div *ngIf="formSubmitted && formValues.email.invalid" class="invalid-feedback">
                  <div *ngIf="formValues.email.errors?.required">
                    L'e-mail est requis.
                  </div>
                  <div *ngIf="formValues.email.errors?.email">
                    L'entrée doit être une adresse e-mail valide
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <a routerLink="/auth/reset-password" tabindex="4" class="text-muted float-end"><small>Mot de passe
                    oublié?</small></a>
                <label for="password" class="form-label">Mot de passe</label>
                <div class="input-group input-group-merge">
                  <input autocomplete="off" tabindex="2" [type]="showPassword ? 'text' : 'password'" id="password"
                    class="form-control" placeholder="Tapez votre mot de passe" formControlName="password" required
                    [ngClass]="{
                      'is-invalid': formSubmitted && formValues.password.invalid
                    }" />
                  <!-- <div class="input-group-text pointer-cus" tabindex="3" (click)="showPassword = !showPassword">
                    <span class="password-eye-off" *ngIf="!showPassword"></span>
                    <span class="password-eye" *ngIf="showPassword"></span>
                  </div> -->
                  <div *ngIf="formSubmitted && formValues.password.invalid" class="invalid-feedback">
                    <div *ngIf="formValues.password.errors?.required">
                      Mot de passe requis.
                    </div>
                    <div *ngIf="formValues.password.errors?.minlength">
                      La longueur du mot de passe doit être supérieure à 3.
                    </div>
                  </div>
                </div>
              </div>

              <div class="mb-3 mb-3">
                <div class="form-check">
                  <input type="checkbox" class="form-check-input " style="background-color: #540B0E !important; border-color: #E09F3E !important;" id="checkbox-signin" checked />
                  <label class="form-check-label" for="checkbox-signin">Se souvenir de moi</label>
                </div>
              </div>

              <div class="mb-3 mb-0 text-center">
                <button class="btn text-white "  style="background-color: #540B0E;"
                onmouseover="this.style.backgroundColor='#540B0E'"
                type="submit">
                  Se connecter
                </button>
              </div>
            </form>

          </div>
          <!-- end card-body-cus -->
        </div>
      </div>
      <!-- end card -->

      <div class="row mt-3">
        <div class="col-12 text-center">
          <!-- <p >Vous n'avez pas de compte? <a routerLink="/account/signup" class="text-muted ml-1 primary-color"><b>S'inscrire</b></a></p> -->
        </div> <!-- end col -->
      </div>
      <!-- end row -->
    </div>
  </div>

</div>

<footer class="footer footer-alt">2025 © WinPlus-Ref - ref.WinPlus.ma <app-version-badge></app-version-badge>

</footer>