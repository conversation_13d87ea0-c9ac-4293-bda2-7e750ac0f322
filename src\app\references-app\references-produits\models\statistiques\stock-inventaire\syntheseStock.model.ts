


export class SyntheseStock {
    nbrLignes?: number;
    nbrProduits?: number;
    qteStock?: number;
    valeurMargeStock?: number;
    valeurPrixAchatStdStockNegatif?: number;
    valeurPrixVenteStdStockNegatif?: number;
    valeurSeuilStockMaxPrixAchatStdTtc?: number;
    valeurSeuilStockMaxPrixVenteStdTtc?: number;
    valeurSeuilStockMinPrixAchatStdTtc?: number;
    valeurSeuilStockMinPrixVenteStdTtc?: number;
    valeurStockPrixAchatStdHt?: number;
    valeurStockPrixAchatStdTtc?: number;
    valeurStockPrixAchatStdTva?: number;
    valeurStockPrixVenteStdHt?: number;
    valeurStockPrixVenteStdTtc?: number;
    valeurStockPrixVenteStdTva?: number;
    couvertureStockJour?: number
    differenceAvecCriteria?: number

}
