import { TypeTiers } from 'src/app/references-app/references-produits/enums/tiers/TypeTiers.enum';
import { TypeRemiseClient } from 'src/app/references-app/references-produits/enums/tiers/TypeRemiseClient.enum';
import { TypeEncaissementClient } from 'src/app/references-app/references-produits/enums/tiers/TypeEncaissementClient.enum';

// import { Moment } from 'moment';

import { Beneficiaire } from 'src/app/references-app/references-produits/models/tiers/client/beneficiaire.model';
import { ClientConventionAss } from './clientConventionAss.model';
import { ClientRemiseFt } from './clientRemiseFt.model';
import { GroupeClient } from './groupeClient.model';
import { Tiers } from 'src/app/references-app/references-produits/models/tiers/tiers.model';
import { Ville } from 'src/app/references-app/references-produits/models/common/ville.model';


export class Client {
    adr1?: string;
    adr2?: string;
    audited?: boolean;
    beneficiaires?: Beneficiaire[];
    clientConventionAssurances?: ClientConventionAss[];
    clientRemiseFts?: ClientRemiseFt[];
    dateDernierCredit?: any;
    dateDernierReglement?: any;
    dernierCredit?: number;
    dernierReglement?: number;
    email?: string;
    estActif?: boolean;
    groupe?: GroupeClient;
    gsm?: string;
    id?: number;
    nom?: string;
    numCin?: string;
    numIce?: string;
    numTelephone?: string;
    plafondCredit?: number;
    prenom?: string;
    soldeClient?: number;
    tauxRemise?: number;
    tiers?: Tiers;
    typeEncaissement?: TypeEncaissementClient;
    typeRemise?: TypeRemiseClient;
    typeTiers?: TypeTiers;
    userModifiable?: boolean;
    ville?: Ville;

    nomprenom?: string;

    caClient?: number

    soldeClientDepart?: number


    offlineUpdate?: boolean = false


    constructor() {
        this.nom = '';
        this.prenom = '';
        this.soldeClient = 0;
        this.plafondCredit = 0;

        this.tauxRemise = 0;
        this.typeRemise = TypeRemiseClient.AUCUNE;

        this.beneficiaires = [];
        this.clientConventionAssurances = [];
        this.clientRemiseFts = [];
    }


    get nomPrenom() {
        return [this.nom, this.prenom].join(" ");
    }
}
