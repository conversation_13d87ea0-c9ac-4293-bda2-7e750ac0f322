import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { Page } from '../../referential/models/Page/page.model';
import { WinClientLocalite } from '../models/localite';
 @Injectable({
  providedIn: 'root'
})
export class WinclientLocaliteService {

  constructor(private http: HttpClient) { }
 

  getAllLocalites() {
    return this.http.get<WinClientLocalite[]>(`${env.winclient_base_url}/api/winclient/localites`);
  }

  createLocalite(localite: Omit<WinClientLocalite, "id">) {
    return this.http.post<WinClientLocalite>(`${env.winclient_base_url}/api/winclient/localites`, localite);
  }

  updateLocalite(id:number,localite: Omit<WinClientLocalite, "id">) {
    return this.http.put<WinClientLocalite>(`${env.winclient_base_url}/api/winclient/localites/${id}`, localite);
  }

}