import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DrawerService } from '../../services/drawer.service';

@Component({
  selector: 'app-drawer-example',
  template: `
    <div class="container-fluid p-4">
      <h1>Accessible Drawer Examples</h1>
      <p class="text-muted">Comprehensive examples of the accessible drawer component</p>
      
      <!-- Trigger <PERSON>tons -->
      <div class="row mb-4">
        <div class="col-12">
          <h3>Basic Drawers</h3>
          <div class="btn-group me-2 mb-2" role="group">
            <button type="button" class="btn btn-primary" (click)="openDrawer('right')">
              Right Drawer
            </button>
            <button type="button" class="btn btn-secondary" (click)="openDrawer('left')">
              Left Drawer
            </button>
            <button type="button" class="btn btn-success" (click)="openDrawer('top')">
              Top Drawer
            </button>
            <button type="button" class="btn btn-info" (click)="openDrawer('bottom')">
              Bottom Drawer
            </button>
          </div>
        </div>
      </div>

      <div class="row mb-4">
        <div class="col-12">
          <h3>Size Variants</h3>
          <div class="btn-group me-2 mb-2" role="group">
            <button type="button" class="btn btn-outline-primary" (click)="openSizedDrawer('small')">
              Small
            </button>
            <button type="button" class="btn btn-outline-primary" (click)="openSizedDrawer('medium')">
              Medium
            </button>
            <button type="button" class="btn btn-outline-primary" (click)="openSizedDrawer('large')">
              Large
            </button>
            <button type="button" class="btn btn-outline-primary" (click)="openSizedDrawer('full')">
              Full Screen
            </button>
          </div>
        </div>
      </div>

      <div class="row mb-4">
        <div class="col-12">
          <h3>Form Example</h3>
          <button type="button" class="btn btn-warning" (click)="openFormDrawer()">
            Open Form Drawer
          </button>
        </div>
      </div>
    </div>

    <!-- Right Drawer -->
    <app-drawer
      [isOpen]="rightDrawerOpen"
      title="Right Side Drawer"
      position="right"
      size="medium"
      [closeOnBackdropClick]="true"
      [closeOnEscape]="true"
      (opened)="onDrawerOpened('right')"
      (closed)="onDrawerClosed('right')">
      
      <div class="p-3">
        <h4>Welcome to the Right Drawer!</h4>
        <p>This drawer slides in from the right side of the screen.</p>
        
        <div class="alert alert-info">
          <strong>Accessibility Features:</strong>
          <ul class="mb-0 mt-2">
            <li>Focus is trapped within the drawer</li>
            <li>Escape key closes the drawer</li>
            <li>Focus returns to trigger button when closed</li>
            <li>Screen reader announcements</li>
            <li>Proper ARIA attributes</li>
          </ul>
        </div>

        <div class="mt-3">
          <button type="button" class="btn btn-primary me-2">Action Button</button>
          <button type="button" class="btn btn-secondary" (click)="rightDrawerOpen = false">
            Close Drawer
          </button>
        </div>
      </div>
    </app-drawer>

    <!-- Left Drawer -->
    <app-drawer
      [isOpen]="leftDrawerOpen"
      title="Left Side Drawer"
      position="left"
      size="medium">
      
      <div class="p-3">
        <h4>Left Side Navigation</h4>
        <nav>
          <ul class="list-unstyled">
            <li><a href="#" class="d-block py-2 text-decoration-none">Dashboard</a></li>
            <li><a href="#" class="d-block py-2 text-decoration-none">Users</a></li>
            <li><a href="#" class="d-block py-2 text-decoration-none">Settings</a></li>
            <li><a href="#" class="d-block py-2 text-decoration-none">Reports</a></li>
          </ul>
        </nav>
      </div>
    </app-drawer>

    <!-- Top Drawer -->
    <app-drawer
      [isOpen]="topDrawerOpen"
      title="Top Notification Panel"
      position="top"
      size="medium">
      
      <div class="p-3">
        <h4>Notifications</h4>
        <div class="list-group">
          <div class="list-group-item">
            <strong>New Message</strong>
            <p class="mb-1">You have received a new message from John Doe.</p>
            <small>2 minutes ago</small>
          </div>
          <div class="list-group-item">
            <strong>System Update</strong>
            <p class="mb-1">System maintenance scheduled for tonight.</p>
            <small>1 hour ago</small>
          </div>
        </div>
      </div>
    </app-drawer>

    <!-- Bottom Drawer -->
    <app-drawer
      [isOpen]="bottomDrawerOpen"
      title="Quick Actions"
      position="bottom"
      size="small">
      
      <div class="p-3">
        <div class="row">
          <div class="col-6 col-md-3 mb-2">
            <button type="button" class="btn btn-outline-primary w-100">
              <i class="mdi mdi-plus"></i><br>
              <small>Add Item</small>
            </button>
          </div>
          <div class="col-6 col-md-3 mb-2">
            <button type="button" class="btn btn-outline-secondary w-100">
              <i class="mdi mdi-download"></i><br>
              <small>Download</small>
            </button>
          </div>
          <div class="col-6 col-md-3 mb-2">
            <button type="button" class="btn btn-outline-success w-100">
              <i class="mdi mdi-share"></i><br>
              <small>Share</small>
            </button>
          </div>
          <div class="col-6 col-md-3 mb-2">
            <button type="button" class="btn btn-outline-danger w-100">
              <i class="mdi mdi-delete"></i><br>
              <small>Delete</small>
            </button>
          </div>
        </div>
      </div>
    </app-drawer>

    <!-- Sized Drawer -->
    <app-drawer
      [isOpen]="sizedDrawerOpen"
      [title]="sizedDrawerTitle"
      position="right"
      [size]="currentSize">
      
      <div class="p-3">
        <h4>{{ sizedDrawerTitle }}</h4>
        <p>This drawer demonstrates different size options:</p>
        <ul>
          <li><strong>Small:</strong> 320px width</li>
          <li><strong>Medium:</strong> 480px width</li>
          <li><strong>Large:</strong> 640px width</li>
          <li><strong>Full:</strong> 100% viewport width</li>
        </ul>
        
        <div class="mt-3">
          <p><strong>Current size:</strong> {{ currentSize }}</p>
        </div>
      </div>
    </app-drawer>

    <!-- Form Drawer -->
    <app-drawer
      [isOpen]="formDrawerOpen"
      title="User Registration Form"
      position="right"
      size="large"
      [closeOnBackdropClick]="false">
      
      <form [formGroup]="userForm" (ngSubmit)="onSubmitForm()" class="p-3">
        <div class="mb-3">
          <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
          <input 
            type="text" 
            class="form-control" 
            id="firstName" 
            formControlName="firstName"
            [class.is-invalid]="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched"
            appAutoFocus>
          <div class="invalid-feedback" *ngIf="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched">
            First name is required
          </div>
        </div>

        <div class="mb-3">
          <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
          <input 
            type="text" 
            class="form-control" 
            id="lastName" 
            formControlName="lastName"
            [class.is-invalid]="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched">
          <div class="invalid-feedback" *ngIf="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched">
            Last name is required
          </div>
        </div>

        <div class="mb-3">
          <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
          <input 
            type="email" 
            class="form-control" 
            id="email" 
            formControlName="email"
            [class.is-invalid]="userForm.get('email')?.invalid && userForm.get('email')?.touched">
          <div class="invalid-feedback" *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched">
            <span *ngIf="userForm.get('email')?.errors?.['required']">Email is required</span>
            <span *ngIf="userForm.get('email')?.errors?.['email']">Please enter a valid email</span>
          </div>
        </div>

        <div class="mb-3">
          <label for="phone" class="form-label">Phone Number</label>
          <input 
            type="tel" 
            class="form-control" 
            id="phone" 
            formControlName="phone">
        </div>

        <div class="mb-3">
          <label for="role" class="form-label">Role</label>
          <select class="form-select" id="role" formControlName="role">
            <option value="">Select a role</option>
            <option value="admin">Administrator</option>
            <option value="user">User</option>
            <option value="moderator">Moderator</option>
          </select>
        </div>

        <div class="mb-3 form-check">
          <input 
            type="checkbox" 
            class="form-check-input" 
            id="newsletter" 
            formControlName="newsletter">
          <label class="form-check-label" for="newsletter">
            Subscribe to newsletter
          </label>
        </div>
      </form>

      <!-- Footer with action buttons -->
      <div slot="footer" class="d-flex justify-content-end gap-2">
        <button type="button" class="btn btn-secondary" (click)="formDrawerOpen = false">
          Cancel
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          (click)="onSubmitForm()"
          [disabled]="userForm.invalid">
          Save User
        </button>
      </div>
    </app-drawer>
  `
})
export class DrawerExampleComponent implements OnInit {
  
  // Drawer states
  rightDrawerOpen = false;
  leftDrawerOpen = false;
  topDrawerOpen = false;
  bottomDrawerOpen = false;
  sizedDrawerOpen = false;
  formDrawerOpen = false;

  // Size demo
  currentSize: 'small' | 'medium' | 'large' | 'full' = 'medium';
  sizedDrawerTitle = 'Medium Drawer';

  // Form
  userForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private drawerService: DrawerService
  ) {
    this.userForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      role: [''],
      newsletter: [false]
    });
  }

  ngOnInit(): void {
    // Example of using the drawer service
    this.drawerService.register({
      id: 'example-drawer',
      isOpen: false,
      title: 'Service Managed Drawer',
      position: 'right'
    });
  }

  openDrawer(position: 'left' | 'right' | 'top' | 'bottom'): void {
    switch (position) {
      case 'right':
        this.rightDrawerOpen = true;
        break;
      case 'left':
        this.leftDrawerOpen = true;
        break;
      case 'top':
        this.topDrawerOpen = true;
        break;
      case 'bottom':
        this.bottomDrawerOpen = true;
        break;
    }
  }

  openSizedDrawer(size: 'small' | 'medium' | 'large' | 'full'): void {
    this.currentSize = size;
    this.sizedDrawerTitle = `${size.charAt(0).toUpperCase() + size.slice(1)} Drawer`;
    this.sizedDrawerOpen = true;
  }

  openFormDrawer(): void {
    this.userForm.reset();
    this.formDrawerOpen = true;
  }

  onSubmitForm(): void {
    if (this.userForm.valid) {
      console.log('Form submitted:', this.userForm.value);
      this.formDrawerOpen = false;
      // Here you would typically send the data to a service
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.userForm.controls).forEach(key => {
        this.userForm.get(key)?.markAsTouched();
      });
    }
  }

  onDrawerOpened(position: string): void {
    console.log(`${position} drawer opened`);
  }

  onDrawerClosed(position: string): void {
    console.log(`${position} drawer closed`);
  }
}
