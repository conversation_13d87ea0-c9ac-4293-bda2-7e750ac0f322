import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';
import { TypeBla } from 'src/app/references-app/references-produits/enums/achat-avoir/TypeBla.enum';

// import { Moment } from 'moment';

import { Depot } from 'src/app/references-app/references-produits/models/produit/stock/depot.model';
import { DetailBlAchat } from './detailBlAchat.model';
import { Devise } from 'src/app/references-app/references-produits/models/common/devise.model';
import { EnteteCmdAchat } from 'src/app/references-app/references-produits/models/achat-avoir/cmd/base/enteteCmdAchat.model';
import { EnteteDemandeAvoir } from 'src/app/references-app/references-produits/models/achat-avoir/avoir/dmdavoir/enteteDemandeAvoir.model';
import { Fournisseur } from 'src/app/references-app/references-produits/models/tiers/fournisseur/fournisseur.model';
import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';


export class EnteteBlAchat {
    audited?: boolean;
    axeAnalytique?: string;
    codeFrnsr?: string;
    cumulBla?: number;
    dateBla?: any;
    dateCreation?: any;
    dateFacture?: any;
    depot?: Depot;
    detailBlAchats?: DetailBlAchat[];
    devise?: Devise;
    enteteCmdAchat?: EnteteCmdAchat;
    enteteDemandeAvoirs?: EnteteDemandeAvoir[];
    flagAccepteEcart?: boolean;
    fournisseur?: Fournisseur;
    id?: number;
    libelleFrnsr?: string;
    mntAchatStd?: number;
    mntBrutHt?: number;
    mntBrutTtc?: number;
    mntNetEffectifHt?: number;
    mntNetEffectifTtc?: number;
    mntNetEffectifTva?: number;
    mntNetHt?: number;
    mntNetTtc?: number;
    mntRemiseEffectifHt?: number;
    mntRemiseEffectifTtc?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTva?: number;
    mntVenteStd?: number;
    nbrLigne?: number;
    numBla?: number;
    numCmd?: number;
    numFacture?: number;
    operateur?: Operateur;
    statut?: Statut;
    totalQtLivre?: number;
    typeBla?: TypeBla;
    userModifiable?: boolean;
    tauxRemise?: number;

    // transient
    diff?: number;

}

