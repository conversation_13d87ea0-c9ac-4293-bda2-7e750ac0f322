import { AuthService } from './../../shared/services/auth.service';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
@Component({
  selector: 'app-logout',
  templateUrl: './logout.component.html',
  styleUrls: ['./logout.component.scss']
})
export class LogoutComponent implements OnInit {

  constructor(private authenticationService: AuthService, private route: ActivatedRoute,
    private router: Router,) { }

  ngOnInit(): void {
    if (this.router.url.includes("tenant")) {
      this.authenticationService.logoutTenant()
    } else this.authenticationService.logout();
  }

  ngAfterViewInit(): void {
    document.body.classList.add('authentication-bg');
  }

}
