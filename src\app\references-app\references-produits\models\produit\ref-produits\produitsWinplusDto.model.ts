export interface ProduitWinplusDto {
    codeWinplus: string;
    dateCreation: string; // $date-time maps to string (ISO 8601)
    dateSuppression: string; // $date-time
    designation: string;
    codeGroupe: string;
    codeBarre: string; // $string maps to string in TypeScript
    id: number; // $int64 maps to number in TypeScript
    pbrH: number;
    pbrP: number;
    prixAchatStd: number;
    prixHosp: number;
    prixVenteStd: number;
  }


export class ProduitWinplus implements ProduitWinplusDto {
    codeWinplus: string;
    dateCreation: string;  
    dateSuppression: string;  
    designation: string;
    codeGroupe: string;
    id: number;  
    pbrH: number;
    pbrP: number;
    prixAchatStd: number;
    prixHosp: number;
    prixVenteStd: number;
    codeBarre: string; // $string maps to string in TypeScript
  
    constructor(produit: Partial<ProduitWinplusDto>) {
         Object.assign(this, produit);
     }
  }