import { TypeMontantCredit } from 'src/app/references-app/references-produits/enums/statistiques/TypeMontantCredit.enum';

// import { Moment } from 'moment';

import { Operateur } from 'src/app/references-app/references-produits/models/common/operateur.model';


export class StatistiquesClientCriteria {
    actif?: boolean;
    dateDebut?: any;
    dateDernierReglement?: any;
    dateFin?: any;
    estClientDormant?: boolean;
    operateur?: Operateur;
    typeMontantCreditEnum?: TypeMontantCredit;
    onlyWithSoldeNonNull?: boolean
}

