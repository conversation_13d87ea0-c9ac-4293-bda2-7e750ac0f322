import { Component, OnInit } from '@angular/core';
import { WinclientStatsService } from '../../Services/winclient.stats';
import { ClientStats } from '../../models/winclient.stats';
import { WinClientSite } from '../../models/sites';
import { WinclientSitesService } from '../../Services/winclient.sites.service';
import { WinclientBatchesService } from '../../Services/winclient.batches';
import { AlertService } from 'src/app/shared/services/alert.service';

type SiteStats = WinClientSite & { derniereMaj: string };
@Component({
  selector: 'app-winclient-acceuil',
  templateUrl: './winclient-acceuil.component.html',
  styleUrls: ['./winclient-acceuil.component.scss']
})
export class WinclientAcceuilComponent implements OnInit {

  winclientStats:ClientStats = null;
  isLoading:boolean = true;
  sitesMajStats : SiteStats[] = [];

  
  tableStats :any[] = [
    { site: 'UGP', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'CPM', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'DIPHARM', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'GIPHAR', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'SOPHACA_CASA', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'SOPHACA_SALMIA', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'TADLAPHARM_BM', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'TADLAPHARM_FBS', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'SOPHATLENTIC', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'MEDIREP', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'SOPHACA_MAR', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'ORP', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'PHARMANORD', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' },
    { site: 'SOPHAGHARB', dateMajClient: '19-02-2025', dateMajCommercial: '19-02-2025' }
  ]

  constructor(
    private statsService: WinclientStatsService,
    private winclientSiteService: WinclientSitesService,
    private winclientBatchService: WinclientBatchesService,
   private alertService: AlertService
  ) { }

  ngOnInit() {
    this.getStats();
  }

  getStats(){
    this.statsService.getStats().subscribe(
      {
         next: (data) => {
           this.winclientStats = data;
           this.getsites();
         },
         complete: () => {
           this.isLoading = false;
         }
 
      }
     )
  }

  getsites(){
    this.winclientSiteService.getAllSites().subscribe(
      {
        next: (data) => {
           this.sitesMajStats = this.mergeSiteStats(this.winclientStats, data);
        } ,
        complete: () => {
          this.isLoading = false;
        }
      }
    );

  }

  executeBatchImportClientSite() {
    this.winclientBatchService.executeBatchImportClientSite().subscribe(
      {
        next: (data) => {
          if(data && data.status === "SUCCESS"){
            this.alertService.success("Importation des sites clients effectuée avec succès");
            this.getStats();
          }
        }
      }
    );
  }

 mergeSiteStats(stats: ClientStats, sites: WinClientSite[]) :SiteStats[] {
  const mergedSites = sites.map(site => {
    // Skip site with id 6 and above 14 and 0
    if (site.id === 6 || site.id > 14 || site.id === 0) {
      return null;
    }
    
    return {
      ...site,
      derniereMaj: stats.derniereMajParSite[site.id.toString()] === "non disponible" ? null :  stats.derniereMajParSite[site.id.toString()]
    };
  }).filter(site => site !== null);

  mergedSites.sort((a, b) => a.id - b.id);

  return mergedSites;
}
}
