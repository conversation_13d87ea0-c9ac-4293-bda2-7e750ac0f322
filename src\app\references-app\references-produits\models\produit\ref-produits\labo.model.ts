interface ILaboCriteria {
  raisonSociale: string,
  typeFrn: string
}

export class LaboCriteria implements ILaboCriteria {
  raisonSociale: string;
  typeFrn: string;

  constructor(data: Partial<ILaboCriteria>) {
    Object.assign(this, data);
  }
}

interface ILabo {
  id: number;
  raisonSociale: string;
  typeFrn: string;
}


export class Labo implements ILabo {
  id: number = 0;
  raisonSociale: string = '';
  typeFrn: string = '';

  constructor(data: Partial<ILabo>) {
    Object.assign(this, data);
  }
}