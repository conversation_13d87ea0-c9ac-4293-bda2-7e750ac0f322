import { AuthService } from "./../../shared/services/auth.service";
import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { first } from "rxjs/operators";
import { TenantPrincipal } from "src/app/shared/models/tenantPrincipal.model";

@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
})
export class LoginComponent implements OnInit {
  loading: boolean = false;
  returnUrl: string = "/dashboard";

  loginForm!: FormGroup;
  formSubmitted: boolean = false;
  error: any;

  showPassword: boolean = false;
  authTenant: boolean = true

  tenant: TenantPrincipal;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authenticationService: AuthService,
    private fb: FormBuilder
  ) {

    // if (this.router.url.includes("user")) {
      //   this.authTenant = false
    // } else {
      //   //console.log(this.authenticationService.isTenantAuthenticated())
      //   if (this.authenticationService.isTenantAuthenticated()) {
        //     //console.log("am hereeee ")
        //     this.router.navigate(["/auth/login/user"], { relativeTo: this.route })
      //   }
    // }

  }



  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: ["", [Validators.required]],
      password: ["", Validators.required],
    });
    // router 

    console.log(this.router.url)

    // this.route.queryParams.subscribe(params => {
    //   console.log("login1", params)
    //   if (params["step"]) {
    //     this.step = params["step"]
    //   }

    // })

    // reset login status
    this.authenticationService.logout();

    // get return url from route parameters or default to '/'
    this.returnUrl =
      this.route.snapshot.queryParams["returnUrl"] || "/";



  }

  ngAfterViewInit(): void {
    document.body.classList.add("authentication-bg");
  }

  /**
   * convenience getter for easy access to form fields
   */
  get formValues() {
    return this.loginForm.controls;
  }

  /**
   * On submit form
   */
  private onSubmitUser(): void {
    this.formSubmitted = true;
    if (this.loginForm.valid) {
      this.loading = true;
      this.authenticationService
        .login(this.formValues.email?.value, this.formValues.password?.value)
        .pipe(first())
        .subscribe(
          (data: any) => {
            console.log('tessst data login' , data);
            this.router.navigate([this.returnUrl]);
           
          
           
          },
          (error: any) => {
            console.log("*****", error)
            this.error = error;
            this.loading = false;
            if (error.status === 503) {
              this.error.error.message = "Application en cours de maintenance"
            }

          }
        );
    }
  }

  private onSubmitTenant(): void {
    this.formSubmitted = true;
    if (this.loginForm.valid) {
      this.loading = true;
      this.authenticationService
        .loginTenant(this.formValues.email?.value, this.formValues.password?.value)
        .pipe(first())
        .subscribe(
          (data: any) => {

            // console.log("hhh data ", data)
            this.router.navigate(["/auth/login"], { relativeTo: this.route })
            //this.tenant = this.authenticationService.getTenant()
          },
          (error: any) => {
            console.log("*****", error)
            this.error = error;
            this.loading = false;
            if (error.status === 503) {
              this.error.error.message = "Application en cours de maintenance"
            }
          }
        );
    }
  }

  login() {
    if (this.loginForm.value.password == null || this.loginForm.value.password.trim() == "") {
      const moPass = (<HTMLInputElement>document.getElementById("password"));
      if (moPass) {
        moPass.focus();
        return;
      }
    }
  
      this.onSubmitUser()
    
  }


  logoutTenant() {
    this.router.navigate(["/auth/login"], { relativeTo: this.route })
  }
}
