import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WinclientBatchesService {
  constructor(private httpClient: HttpClient) { }


  executeBatchImportClientSite() {

    return this.httpClient.get<{message:string,status:string}>(`${env.winclient_base_url}/api/winclient/import/clients-sites`);
  }
 
}