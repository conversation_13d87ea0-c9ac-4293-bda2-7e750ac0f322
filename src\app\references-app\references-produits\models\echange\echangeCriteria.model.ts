import { SousStatutEchange } from 'src/app/references-app/references-produits/enums/echange/SousStatutEchange.enum';
import { TypePrixEchange } from 'src/app/references-app/references-produits/enums/echange/TypePrixEchange.enum';
import { Statut } from 'src/app/references-app/references-produits/enums/common/Statut.enum';
import { SensEchange } from 'src/app/references-app/references-produits/enums/echange/SensEchange.enum';

// import { Moment } from 'moment';

import { Confrere } from 'src/app/references-app/references-produits/models/tiers/confrere/confrere.model';


export class EchangeCriteria { 
    codeProduit?: string;
    confrere?: Confrere;
    dateEchangeMax?: any;
    dateEchangeMin?: any;
    datePeremption?: string;
    designationProduit?: string;
    mntLigne?: number;
    numEchange?: string;
    numLot?: string;
    prixUnit?: number;
    qt?: number;
    qtUg?: number;
    raisonSociale?: string;
    resteSolde?: number;
    sensEchange?: SensEchange;
    sousStatutList?: SousStatutEchange[];
    statut?: Statut;
    tauxRemise?: number;
    typePrix?: TypePrixEchange;
}

